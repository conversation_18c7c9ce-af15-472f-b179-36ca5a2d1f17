#!/usr/bin/env python3
"""
Health Log Monitor - Real-time analysis of production health
Usage: python monitor_health.py
"""

import json
import time
from datetime import datetime, timedelta
from collections import defaultdict, deque
import os

class HealthMonitor:
    def __init__(self, health_file="production_health.jsonl"):
        self.health_file = health_file
        self.last_position = 0
        
    def tail_health_logs(self, lines=10):
        """Get the last N health log entries"""
        if not os.path.exists(self.health_file):
            print(f"❌ Health log file not found: {self.health_file}")
            return []
        
        try:
            with open(self.health_file, 'r') as f:
                all_lines = f.readlines()
                recent_lines = all_lines[-lines:] if len(all_lines) >= lines else all_lines
                
                health_data = []
                for line in recent_lines:
                    try:
                        data = json.loads(line.strip())
                        health_data.append(data)
                    except json.JSONDecodeError:
                        continue
                        
                return health_data
        except Exception as e:
            print(f"❌ Error reading health logs: {e}")
            return []
    
    def analyze_system_trends(self, hours=24):
        """Analyze system performance trends"""
        if not os.path.exists(self.health_file):
            return None
            
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        cpu_values = []
        memory_values = []
        disk_values = []
        timestamps = []
        
        try:
            with open(self.health_file, 'r') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        timestamp = datetime.fromisoformat(data['system']['timestamp'].replace('Z', '+00:00'))
                        
                        if timestamp >= cutoff_time:
                            cpu_values.append(data['system']['cpu_percent'])
                            memory_values.append(data['system']['memory_percent'])
                            disk_values.append(data['system']['disk_free_gb'])
                            timestamps.append(timestamp)
                    except (json.JSONDecodeError, KeyError):
                        continue
        except Exception as e:
            print(f"❌ Error analyzing trends: {e}")
            return None
        
        if not cpu_values:
            return None
            
        return {
            'period_hours': hours,
            'data_points': len(cpu_values),
            'cpu': {
                'avg': sum(cpu_values) / len(cpu_values),
                'max': max(cpu_values),
                'min': min(cpu_values)
            },
            'memory': {
                'avg': sum(memory_values) / len(memory_values),
                'max': max(memory_values),
                'min': min(memory_values)
            },
            'disk_free_gb': {
                'current': disk_values[-1] if disk_values else 0,
                'min': min(disk_values) if disk_values else 0
            }
        }
    
    def analyze_trading_performance(self, hours=24):
        """Analyze trading system performance"""
        if not os.path.exists(self.health_file):
            return None
            
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        connection_uptime = 0
        total_checks = 0
        queue_sizes = []
        position_counts = []
        error_counts = []
        
        try:
            with open(self.health_file, 'r') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        timestamp = datetime.fromisoformat(data['trading']['timestamp'].replace('Z', '+00:00'))
                        
                        if timestamp >= cutoff_time:
                            total_checks += 1
                            if data['trading']['telegram_connected']:
                                connection_uptime += 1
                            
                            queue_sizes.append(data['trading']['signal_queue_size'])
                            position_counts.append(data['trading']['active_positions'])
                            error_counts.append(data['trading']['error_count_1h'])
                    except (json.JSONDecodeError, KeyError):
                        continue
        except Exception as e:
            print(f"❌ Error analyzing trading performance: {e}")
            return None
        
        if total_checks == 0:
            return None
            
        return {
            'period_hours': hours,
            'telegram_uptime_percent': (connection_uptime / total_checks) * 100,
            'avg_queue_size': sum(queue_sizes) / len(queue_sizes) if queue_sizes else 0,
            'max_queue_size': max(queue_sizes) if queue_sizes else 0,
            'avg_positions': sum(position_counts) / len(position_counts) if position_counts else 0,
            'max_positions': max(position_counts) if position_counts else 0,
            'avg_errors_per_hour': sum(error_counts) / len(error_counts) if error_counts else 0,
            'max_errors_per_hour': max(error_counts) if error_counts else 0
        }
    
    def get_current_alerts(self):
        """Get current active alerts"""
        recent_data = self.tail_health_logs(1)
        if recent_data:
            return recent_data[0].get('alerts', [])
        return []
    
    def print_health_summary(self):
        """Print comprehensive health summary"""
        print("\n" + "="*80)
        print("🏥 PRODUCTION HEALTH SUMMARY")
        print("="*80)
        
        # Current status
        recent_data = self.tail_health_logs(1)
        if recent_data:
            current = recent_data[0]
            print(f"📊 CURRENT STATUS (as of {current['system']['timestamp']}):")
            print(f"   CPU: {current['system']['cpu_percent']:.1f}%")
            print(f"   Memory: {current['system']['memory_percent']:.1f}%")
            print(f"   Disk Free: {current['system']['disk_free_gb']:.1f}GB")
            print(f"   Uptime: {current['system']['uptime_hours']:.1f}h")
            print(f"   Telegram: {'✅ Connected' if current['trading']['telegram_connected'] else '❌ Disconnected'}")
            print(f"   Active Positions: {current['trading']['active_positions']}")
            print(f"   Signal Queue: {current['trading']['signal_queue_size']}")
            
            if current.get('alerts'):
                print(f"🚨 ACTIVE ALERTS: {', '.join(current['alerts'])}")
            else:
                print("✅ No active alerts")
        
        # 24-hour trends
        system_trends = self.analyze_system_trends(24)
        if system_trends:
            print(f"\n📈 24-HOUR SYSTEM TRENDS ({system_trends['data_points']} data points):")
            print(f"   CPU: Avg {system_trends['cpu']['avg']:.1f}% (Max: {system_trends['cpu']['max']:.1f}%)")
            print(f"   Memory: Avg {system_trends['memory']['avg']:.1f}% (Max: {system_trends['memory']['max']:.1f}%)")
            print(f"   Disk: {system_trends['disk_free_gb']['current']:.1f}GB free")
        
        # Trading performance
        trading_perf = self.analyze_trading_performance(24)
        if trading_perf:
            print(f"\n📊 24-HOUR TRADING PERFORMANCE:")
            print(f"   Telegram Uptime: {trading_perf['telegram_uptime_percent']:.1f}%")
            print(f"   Avg Queue Size: {trading_perf['avg_queue_size']:.1f}")
            print(f"   Avg Positions: {trading_perf['avg_positions']:.1f}")
            print(f"   Avg Errors/Hour: {trading_perf['avg_errors_per_hour']:.1f}")
        
        print("="*80)

def main():
    monitor = HealthMonitor()
    
    print("🔍 PRODUCTION HEALTH MONITOR")
    print("Commands:")
    print("  1. Current status")
    print("  2. 24-hour summary") 
    print("  3. Live monitoring (updates every 30s)")
    print("  4. Exit")
    
    while True:
        try:
            choice = input("\nEnter choice (1-4): ").strip()
            
            if choice == '1':
                recent = monitor.tail_health_logs(5)
                print(f"\n📊 LAST 5 HEALTH CHECKS:")
                for entry in recent:
                    timestamp = entry['system']['timestamp']
                    cpu = entry['system']['cpu_percent']
                    memory = entry['system']['memory_percent']
                    telegram = "✅" if entry['trading']['telegram_connected'] else "❌"
                    positions = entry['trading']['active_positions']
                    alerts = entry.get('alerts', [])
                    alert_str = f" 🚨 {', '.join(alerts)}" if alerts else ""
                    print(f"   {timestamp}: CPU {cpu:.1f}%, Mem {memory:.1f}%, TG {telegram}, Pos {positions}{alert_str}")
            
            elif choice == '2':
                monitor.print_health_summary()
            
            elif choice == '3':
                print("\n🔴 LIVE MONITORING (Ctrl+C to stop)")
                try:
                    while True:
                        os.system('cls' if os.name == 'nt' else 'clear')
                        monitor.print_health_summary()
                        time.sleep(30)
                except KeyboardInterrupt:
                    print("\n✅ Live monitoring stopped")
            
            elif choice == '4':
                print("👋 Goodbye!")
                break
            
            else:
                print("❌ Invalid choice")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
