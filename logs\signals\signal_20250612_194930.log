2025-06-12 19:49:30,155 - signal_handler - DEBUG - Loaded Telegram API ID: 27993698
2025-06-12 19:49:30,156 - signal_handler - DEBUG - Loaded Telegram API Hash: ********************************
2025-06-12 19:49:30,156 - signal_handler - INFO - Loaded session string from c:\Users\<USER>\Downloads\billy who\session_string.json (length: 353)
2025-06-12 19:49:30,157 - signal_handler - INFO - Using unique session name with timestamp: trading_sim_session_1749737970
2025-06-12 19:49:30,157 - signal_handler - INFO - Using session path: c:\Users\<USER>\Downloads\billy who\trading_sim_session_1749737970
2025-06-12 19:49:30,157 - signal_handler - INFO - Signal queue initialized with size: 0
2025-06-12 19:49:30,172 - signal_handler - INFO - Direct signal callback registered
2025-06-12 19:49:35,296 - signal_handler - INFO - [msg_1749737975293_8564] Queuing Telegram message: [WARNING] IMPORTANT BOT MESSAGE [?] 2025-06-12 19:49:35

[REFRESH] BOT STATE RESET

All positions, s...
2025-06-12 19:49:35,297 - signal_handler - INFO - Started Telegram message queue processor
2025-06-12 19:49:35,297 - signal_handler - INFO - [msg_1749737975293_8564] Message queued with normal priority
2025-06-12 19:49:35,347 - signal_handler - INFO - [msg_1749737975347_5063] Queuing Telegram message:  PORTFOLIO UPDATE [?] STATE RESET

 Timestamp: 2025-06-12 19:49:35
[REPEAT] Trade: SIMULATION

[MONE...
2025-06-12 19:49:35,349 - signal_handler - INFO - [msg_1749737975347_5063] Message queued with normal priority
2025-06-12 19:49:35,356 - signal_handler - INFO - Message queue processor started with adaptive rate limiting
2025-06-12 19:49:35,356 - signal_handler - WARNING - [msg_1749737975293_8564] Telegram client not connected before sending, attempting to reconnect
2025-06-12 19:49:35,356 - signal_handler - INFO - API ID loaded: True
2025-06-12 19:49:35,357 - signal_handler - INFO - API Hash loaded: True
2025-06-12 19:49:35,357 - signal_handler - INFO - Phone number loaded: True
2025-06-12 19:49:35,357 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-12 19:49:35,357 - signal_handler - INFO - Creating new Telegram client (attempt 1/3)
2025-06-12 19:49:35,358 - signal_handler - INFO - Using saved session string
2025-06-12 19:49:35,358 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-12 19:49:35,654 - signal_handler - INFO - Checking authorization status...
2025-06-12 19:49:35,790 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 19:49:35,791 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-12 19:49:35,792 - signal_handler - INFO - Checking authorization status...
2025-06-12 19:49:35,884 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 19:49:35,884 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-12 19:49:35,885 - signal_handler - INFO - Checking authorization status...
2025-06-12 19:49:35,978 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 19:49:35,979 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-12 19:49:35,980 - signal_handler - INFO - Sending test message to verify connection to channel -1002525039395
2025-06-12 19:49:35,980 - signal_handler - INFO - Sending test message directly to channel ID: -1002525039395
2025-06-12 19:49:36,170 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-12 19:49:36,171 - signal_handler - INFO - [msg_1749737975293_8564] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:49:36,255 - signal_handler - INFO - [msg_1749737975293_8564] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:49:36,403 - signal_handler - INFO - [msg_1749737975293_8564] Message sent directly successfully using entity
2025-06-12 19:49:36,403 - signal_handler - INFO - [msg_1749737975293_8564] Message sent successfully via direct send method
2025-06-12 19:49:36,404 - signal_handler - INFO - [msg_1749737975293_8564] Message sent successfully on attempt 1
2025-06-12 19:49:36,405 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 19:49:36,917 - signal_handler - INFO - [msg_1749737975347_5063] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:49:37,056 - signal_handler - INFO - [msg_1749737975347_5063] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:49:37,222 - signal_handler - INFO - [msg_1749737975347_5063] Message sent directly successfully using entity
2025-06-12 19:49:37,223 - signal_handler - INFO - [msg_1749737975347_5063] Message sent successfully via direct send method
2025-06-12 19:49:37,223 - signal_handler - INFO - [msg_1749737975347_5063] Message sent successfully on attempt 1
2025-06-12 19:49:55,948 - signal_handler - INFO - API ID loaded: True
2025-06-12 19:49:55,948 - signal_handler - INFO - API Hash loaded: True
2025-06-12 19:49:55,948 - signal_handler - INFO - Phone number loaded: True
2025-06-12 19:49:55,949 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-12 19:49:55,949 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-12 19:49:55,949 - signal_handler - INFO - Checking authorization status...
2025-06-12 19:49:56,001 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 19:49:56,001 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-12 19:49:56,001 - signal_handler - INFO - Checking authorization status...
2025-06-12 19:49:56,052 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 19:49:56,052 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-12 19:49:56,053 - signal_handler - INFO - Checking authorization status...
2025-06-12 19:49:56,106 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 19:49:56,107 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-12 19:49:56,107 - signal_handler - INFO - Sending test message to verify connection to channel -1002525039395
2025-06-12 19:49:56,107 - signal_handler - INFO - Sending test message directly to channel ID: -1002525039395
2025-06-12 19:49:56,192 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-12 19:49:56,196 - signal_handler - INFO - [msg_1749737996196_2791] Queuing Telegram message:  PORTFOLIO UPDATE [?] PERIODIC UPDATE

 Timestamp: 2025-06-12 19:49:56
[REPEAT] Trade: SIMULATION

[...
2025-06-12 19:49:56,196 - signal_handler - INFO - [msg_1749737996196_2791] Message queued with normal priority
2025-06-12 19:49:56,296 - signal_handler - INFO - Message queue processor active with 1 messages pending
2025-06-12 19:49:56,300 - signal_handler - INFO - [msg_1749737996196_2791] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:49:56,356 - signal_handler - INFO - [msg_1749737996196_2791] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:49:56,445 - signal_handler - INFO - [msg_1749737996196_2791] Message sent directly successfully using entity
2025-06-12 19:49:56,446 - signal_handler - INFO - [msg_1749737996196_2791] Message sent successfully via direct send method
2025-06-12 19:49:56,446 - signal_handler - INFO - [msg_1749737996196_2791] Message sent successfully on attempt 1
2025-06-12 19:49:56,700 - signal_handler - WARNING - [WARNING] Using channel IDs directly (no entities resolved)
2025-06-12 19:49:56,700 - signal_handler - WARNING - Added event handler using channel IDs (entities not resolved yet)
2025-06-12 19:49:56,701 - signal_handler - INFO - Signal listener started in background task. Waiting for messages...
2025-06-12 19:49:56,702 - signal_handler - INFO - Direct signal callback registered
2025-06-12 19:49:56,704 - signal_handler - INFO - [msg_1749737996703_6291] [STARTUP NOTIFICATION]: [ROCKET] Bot Started
[REFRESH] Mode: SIMULATION
[GRAPH] Strategy: AGGRESSIVE
[MONEY] Starting Capita...
2025-06-12 19:49:56,704 - signal_handler - INFO - [msg_1749737996703_6291] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:49:56,705 - signal_handler - INFO - Signal listener running (attempt 1/3).
2025-06-12 19:49:56,758 - signal_handler - INFO - [msg_1749737996703_6291] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:49:56,842 - signal_handler - INFO - [msg_1749737996703_6291] Message sent directly successfully using entity
2025-06-12 19:49:56,845 - signal_handler - INFO - [msg_1749737996703_6291] Message sent directly successfully
2025-06-12 19:50:41,284 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$10.7K(+204.9%)**
*...
2025-06-12 19:50:41,305 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 19:50:41,321 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 19:50:41,337 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 19:50:41,348 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 19:50:41,370 - signal_handler - INFO - Found contract address with 'pump' suffix: EHi8v5Zk3KkWtmGARThZiUPcABMoyxWBQzdDbdHHpump
2025-06-12 19:50:41,395 - signal_handler - INFO - Found contract address: 488sZvadNdXndAJhmVe3KimJfUzgybobFVEc19uZyuxz
2025-06-12 19:50:41,452 - signal_handler - INFO - Using 'pump' address as highest priority: EHi8v5Zk3KkWtmGARThZiUPcABMoyxWBQzdDbdHHpump
2025-06-12 19:50:41,525 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: EHi8v5Zk3KkWtmGARThZiUPcABMoyxWBQzdDbdHHpump
2025-06-12 19:50:41,601 - signal_handler - INFO - Detected GMGN format
2025-06-12 19:50:41,630 - signal_handler - INFO - Found token symbol: 10
2025-06-12 19:50:41,673 - signal_handler - INFO - Found FDV: 10.7K - 10.70 (+204.9%)
2025-06-12 19:50:41,714 - signal_handler - INFO - Detected GMGN channel signal for token: EHi8v5Zk3KkWtmGARThZiUPcABMoyxWBQzdDbdHHpump
2025-06-12 19:50:41,724 - signal_handler - INFO - Extracted signal: Token=EHi8v5Zk3KkWtmGARThZiUPcABMoyxWBQzdDbdHHpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 19:50:41,786 - signal_handler - WARNING - LOW SIGNAL VOLUME: GMGN - 0/8 expected this hour
2025-06-12 19:50:41,812 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solbix - 0/2 expected this hour
2025-06-12 19:50:41,851 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solana Early Trending - 0/1 expected this hour
2025-06-12 19:50:41,888 - signal_handler - INFO - Signal processing health: 1 received, 1 processed, 0 dropped (100.0% success rate)
2025-06-12 19:50:41,916 - signal_handler - INFO - Added signal to queue: EHi8v5Zk3KkWtmGARThZiUPcABMoyxWBQzdDbdHHpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 19:50:41,965 - signal_handler - INFO - Calling direct signal callback for EHi8v5Zk3KkWtmGARThZiUPcABMoyxWBQzdDbdHHpump
