#!/usr/bin/env python3
"""
Production Dashboard - Comprehensive monitoring dashboard
Usage: python dashboard.py
"""

import json
import time
import os
from datetime import datetime, timed<PERSON><PERSON>
from monitor_health import HealthMonitor
from monitor_errors import ErrorMonitor
from trading_metrics import TradingMetricsCollector

class ProductionDashboard:
    def __init__(self):
        self.health_monitor = HealthMonitor()
        self.error_monitor = ErrorMonitor()
        self.trading_metrics = TradingMetricsCollector()
        
    def print_dashboard(self):
        """Print comprehensive production dashboard"""
        os.system('cls' if os.name == 'nt' else 'clear')
        
        print("🚀 SOLANA TRADING BOT - PRODUCTION DASHBOARD")
        print("=" * 80)
        print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} UTC")
        print()
        
        # System Health Section
        self._print_system_health()
        print()
        
        # Trading Performance Section
        self._print_trading_performance()
        print()
        
        # Error Summary Section
        self._print_error_summary()
        print()
        
        # Alerts Section
        self._print_alerts()
        print()
        
        print("=" * 80)
        print("🔄 Auto-refresh every 30 seconds | Press Ctrl+C to exit")
    
    def _print_system_health(self):
        """Print system health section"""
        print("🖥️  SYSTEM HEALTH")
        print("-" * 40)
        
        recent_health = self.health_monitor.tail_health_logs(1)
        if recent_health:
            health = recent_health[0]
            system = health['system']
            trading = health['trading']
            
            # System metrics with color coding
            cpu_color = "🔴" if system['cpu_percent'] > 80 else "🟡" if system['cpu_percent'] > 60 else "🟢"
            mem_color = "🔴" if system['memory_percent'] > 85 else "🟡" if system['memory_percent'] > 70 else "🟢"
            disk_color = "🔴" if system['disk_free_gb'] < 2 else "🟡" if system['disk_free_gb'] < 5 else "🟢"
            
            print(f"CPU Usage:     {cpu_color} {system['cpu_percent']:5.1f}%")
            print(f"Memory Usage:  {mem_color} {system['memory_percent']:5.1f}%")
            print(f"Disk Free:     {disk_color} {system['disk_free_gb']:5.1f} GB")
            print(f"Uptime:        ⏱️  {system['uptime_hours']:5.1f} hours")
            
            # Trading system status
            tg_status = "🟢 Connected" if trading['telegram_connected'] else "🔴 Disconnected"
            queue_color = "🔴" if trading['signal_queue_size'] > 50 else "🟡" if trading['signal_queue_size'] > 20 else "🟢"
            
            print(f"Telegram:      {tg_status}")
            print(f"Signal Queue:  {queue_color} {trading['signal_queue_size']} signals")
            print(f"Positions:     📊 {trading['active_positions']} active")
            print(f"Available SOL: 💰 {trading['available_sol']:.4f}")
        else:
            print("❌ No health data available")
    
    def _print_trading_performance(self):
        """Print trading performance section"""
        print("📊 TRADING PERFORMANCE (24H)")
        print("-" * 40)
        
        # Get trading metrics summary
        summary = self.trading_metrics.get_performance_summary(24)
        if summary:
            win_rate = summary.get('win_rate_percent', 0)
            win_color = "🟢" if win_rate > 60 else "🟡" if win_rate > 40 else "🔴"
            
            print(f"Signals Received: 📡 {summary.get('total_signals_received', 0)}")
            print(f"Trades Executed:  ⚡ {summary.get('total_trades_executed', 0)}")
            print(f"Win Rate:         {win_color} {win_rate:.1f}%")
            print(f"Total PnL:        💵 {summary.get('current_total_pnl', 0):.4f} SOL")
            print(f"Signals/Hour:     📈 {summary.get('signals_per_hour', 0):.1f}")
            print(f"Trades/Hour:      📈 {summary.get('trades_per_hour', 0):.1f}")
            print(f"Avg Processing:   ⚡ {summary.get('avg_signal_processing_ms', 0):.0f}ms")
        else:
            print("❌ No trading metrics available")
            print("💡 Start trading to see performance data")
    
    def _print_error_summary(self):
        """Print error summary section"""
        print("🚨 ERROR SUMMARY (24H)")
        print("-" * 40)
        
        analysis = self.error_monitor.analyze_error_patterns(24)
        if analysis and analysis['total_errors'] > 0:
            error_rate_color = "🔴" if analysis['total_errors'] > 50 else "🟡" if analysis['total_errors'] > 20 else "🟢"
            
            print(f"Total Errors:     {error_rate_color} {analysis['total_errors']}")
            
            # Top error types
            if analysis['error_types']:
                print("Top Error Types:")
                for error_type, count in list(analysis['error_types'].items())[:3]:
                    print(f"  • {error_type}: {count}")
            
            # Severity breakdown
            if analysis['severity_counts']:
                critical_count = analysis['severity_counts'].get('critical', 0)
                error_count = analysis['severity_counts'].get('error', 0)
                
                if critical_count > 0:
                    print(f"Critical Errors:  🔴 {critical_count}")
                if error_count > 0:
                    print(f"Regular Errors:   🟡 {error_count}")
        else:
            print("✅ No errors in last 24 hours")
    
    def _print_alerts(self):
        """Print active alerts section"""
        print("⚠️  ACTIVE ALERTS")
        print("-" * 40)
        
        # Get current alerts from health monitor
        alerts = self.health_monitor.get_current_alerts()
        
        # Get error spikes
        error_spikes = self.error_monitor.find_error_spikes(1, 5)  # Last hour, >5 errors
        
        # Get critical errors
        critical_errors = self.error_monitor.get_critical_errors(1)  # Last hour
        
        alert_count = len(alerts) + len(error_spikes) + len(critical_errors)
        
        if alert_count == 0:
            print("✅ No active alerts - All systems normal")
        else:
            # System alerts
            for alert in alerts:
                print(f"🚨 SYSTEM: {alert}")
            
            # Error spikes
            for spike in error_spikes:
                print(f"🚨 ERROR SPIKE: {spike['error_count']} errors at {spike['hour']}")
            
            # Critical errors
            if critical_errors:
                print(f"🔴 CRITICAL: {len(critical_errors)} critical errors in last hour")
    
    def run_live_dashboard(self):
        """Run live dashboard with auto-refresh"""
        print("🚀 Starting Production Dashboard...")
        print("Press Ctrl+C to exit")
        
        try:
            while True:
                self.print_dashboard()
                time.sleep(30)  # Refresh every 30 seconds
        except KeyboardInterrupt:
            print("\n👋 Dashboard stopped")
    
    def print_detailed_report(self):
        """Print detailed one-time report"""
        print("📋 DETAILED PRODUCTION REPORT")
        print("=" * 80)
        
        # Detailed system analysis
        print("\n🖥️  DETAILED SYSTEM ANALYSIS")
        print("-" * 50)
        self.health_monitor.print_health_summary()
        
        # Detailed error analysis
        print("\n🚨 DETAILED ERROR ANALYSIS")
        print("-" * 50)
        self.error_monitor.print_error_summary()
        
        # Trading metrics
        print("\n📊 DETAILED TRADING METRICS")
        print("-" * 50)
        summary = self.trading_metrics.get_performance_summary(24)
        if summary:
            for key, value in summary.items():
                if isinstance(value, float):
                    print(f"{key.replace('_', ' ').title()}: {value:.2f}")
                else:
                    print(f"{key.replace('_', ' ').title()}: {value}")
        else:
            print("No trading metrics available")

def main():
    dashboard = ProductionDashboard()
    
    print("🚀 PRODUCTION DASHBOARD")
    print("Choose an option:")
    print("1. Live Dashboard (auto-refresh)")
    print("2. One-time Detailed Report")
    print("3. System Health Only")
    print("4. Error Analysis Only")
    print("5. Trading Metrics Only")
    print("6. Exit")
    
    while True:
        try:
            choice = input("\nEnter choice (1-6): ").strip()
            
            if choice == '1':
                dashboard.run_live_dashboard()
                break
            elif choice == '2':
                dashboard.print_detailed_report()
                break
            elif choice == '3':
                dashboard.health_monitor.print_health_summary()
                break
            elif choice == '4':
                dashboard.error_monitor.print_error_summary()
                break
            elif choice == '5':
                summary = dashboard.trading_metrics.get_performance_summary(24)
                print("\n📊 TRADING METRICS SUMMARY:")
                if summary:
                    for key, value in summary.items():
                        print(f"{key.replace('_', ' ').title()}: {value}")
                else:
                    print("No trading metrics available")
                break
            elif choice == '6':
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice")
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
