2025-06-12 14:09:12,904 - signal_handler - INFO - Direct signal processing completed for 7us1kj3u5D5fTf5Z6kDjLTdLpKTEcbe728XguCLpump
2025-06-12 14:09:12,904 - signal_handler - INFO - Signal forwarded to bot controller: 7us1kj3u5D5fTf5Z6kDjLTdLpKTEcbe728XguCLpump from OxyZen Calls (confidence: 0.50)
2025-06-12 14:09:12,905 - signal_handler - INFO - Retrieved signal from queue: 7us1kj3u5D5fTf5Z6kDjLTdLpKTEcbe728XguCLpump from OxyZen Calls
2025-06-12 14:09:12,906 - signal_handler - INFO - [msg_1749717552906_6538] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 14:09:12

 Token CA: 7us1kj3u5D5fTf5Z6kDjLTdLpKTEcbe728XguCLpu...
2025-06-12 14:09:12,906 - signal_handler - INFO - [msg_1749717552906_6538] Message queued with normal priority
2025-06-12 14:09:16,038 - signal_handler - INFO - [msg_1749717556038_8753] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] HIGH WHALE RISK] [?] 2025-06-12 14:09:16

 Token: 7us1kj3u5D5fTf5Z6kDjLTdL...
2025-06-12 14:09:16,039 - signal_handler - INFO - [msg_1749717556038_8753] Message queued with normal priority
2025-06-12 14:09:16,039 - signal_handler - INFO - [msg_1749717552906_6538] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:09:16,098 - signal_handler - INFO - [msg_1749717552906_6538] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:09:16,191 - signal_handler - INFO - [msg_1749717552906_6538] Message sent directly successfully using entity
2025-06-12 14:09:16,191 - signal_handler - INFO - [msg_1749717552906_6538] Message sent successfully via direct send method
2025-06-12 14:09:16,192 - signal_handler - INFO - [msg_1749717552906_6538] Message sent successfully on attempt 1
2025-06-12 14:09:16,192 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 14:09:16,705 - signal_handler - INFO - [msg_1749717556038_8753] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:09:16,852 - signal_handler - INFO - [msg_1749717556038_8753] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:09:17,006 - signal_handler - INFO - [msg_1749717556038_8753] Message sent directly successfully using entity
2025-06-12 14:09:17,006 - signal_handler - INFO - [msg_1749717556038_8753] Message sent successfully via direct send method
2025-06-12 14:09:17,007 - signal_handler - INFO - [msg_1749717556038_8753] Message sent successfully on attempt 1
2025-06-12 14:09:46,568 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$11.8K(+240.6%)**
*...
2025-06-12 14:09:46,568 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 14:09:46,569 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 14:09:46,571 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 14:09:46,573 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 14:09:46,574 - signal_handler - INFO - Found contract address with 'pump' suffix: 4fh1fKjG4oXyN9C45SpBVp4LnVXDHYNwN3LbNkv7pump
2025-06-12 14:09:46,574 - signal_handler - INFO - Found contract address: Aq7x7Ubc9hSsTXtbzju3nsehMuacW65BHxSuHxyvjChN
2025-06-12 14:09:46,575 - signal_handler - INFO - Using 'pump' address as highest priority: 4fh1fKjG4oXyN9C45SpBVp4LnVXDHYNwN3LbNkv7pump
2025-06-12 14:09:46,575 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 4fh1fKjG4oXyN9C45SpBVp4LnVXDHYNwN3LbNkv7pump
2025-06-12 14:09:46,576 - signal_handler - INFO - Detected GMGN format
2025-06-12 14:09:46,577 - signal_handler - INFO - Found token symbol: 11
2025-06-12 14:09:46,578 - signal_handler - INFO - Found FDV: 11.8K - 11.80 (+240.6%)
2025-06-12 14:09:46,579 - signal_handler - INFO - Detected GMGN channel signal for token: 4fh1fKjG4oXyN9C45SpBVp4LnVXDHYNwN3LbNkv7pump
2025-06-12 14:09:46,579 - signal_handler - INFO - Extracted signal: Token=4fh1fKjG4oXyN9C45SpBVp4LnVXDHYNwN3LbNkv7pump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 14:09:46,580 - signal_handler - INFO - Added signal to queue: 4fh1fKjG4oXyN9C45SpBVp4LnVXDHYNwN3LbNkv7pump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 14:09:46,580 - signal_handler - INFO - Calling direct signal callback for 4fh1fKjG4oXyN9C45SpBVp4LnVXDHYNwN3LbNkv7pump
2025-06-12 14:09:46,582 - signal_handler - INFO - Direct signal processing completed for 4fh1fKjG4oXyN9C45SpBVp4LnVXDHYNwN3LbNkv7pump
2025-06-12 14:09:46,582 - signal_handler - INFO - Signal forwarded to bot controller: 4fh1fKjG4oXyN9C45SpBVp4LnVXDHYNwN3LbNkv7pump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 14:09:46,582 - signal_handler - INFO - Retrieved signal from queue: 4fh1fKjG4oXyN9C45SpBVp4LnVXDHYNwN3LbNkv7pump from solana signal alert - gmgn
2025-06-12 14:09:46,583 - signal_handler - INFO - [msg_1749717586583_8424] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 14:09:46

 Token CA: 4fh1fKjG4oXyN9C45SpBVp4LnVXDHYNwN3LbNkv7p...
2025-06-12 14:09:46,583 - signal_handler - INFO - [msg_1749717586583_8424] Message queued with normal priority
2025-06-12 14:09:53,781 - signal_handler - INFO - [msg_1749717593781_5141] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW MARKET CAP] [?] 2025-06-12 14:09:53

 Token: 4fh1fKjG4oXyN9C45SpBVp4Ln...
2025-06-12 14:09:53,782 - signal_handler - INFO - [msg_1749717593781_5141] Message queued with normal priority
2025-06-12 14:09:53,782 - signal_handler - INFO - [msg_1749717586583_8424] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:09:53,840 - signal_handler - INFO - [msg_1749717586583_8424] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:09:53,937 - signal_handler - INFO - [msg_1749717586583_8424] Message sent directly successfully using entity
2025-06-12 14:09:53,939 - signal_handler - INFO - [msg_1749717586583_8424] Message sent successfully via direct send method
2025-06-12 14:09:53,939 - signal_handler - INFO - [msg_1749717586583_8424] Message sent successfully on attempt 1
2025-06-12 14:09:53,940 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 14:09:54,443 - signal_handler - INFO - [msg_1749717593781_5141] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:09:54,502 - signal_handler - INFO - [msg_1749717593781_5141] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:09:54,595 - signal_handler - INFO - [msg_1749717593781_5141] Message sent directly successfully using entity
2025-06-12 14:09:54,596 - signal_handler - INFO - [msg_1749717593781_5141] Message sent successfully via direct send method
2025-06-12 14:09:54,597 - signal_handler - INFO - [msg_1749717593781_5141] Message sent successfully on attempt 1
2025-06-12 14:11:00,158 - signal_handler - INFO - Received message from channel -1002177594166: ********************************************...
2025-06-12 14:11:00,159 - signal_handler - INFO - Setting channel_identifier to 'OxyZen Calls' based on chat_id -1002177594166
2025-06-12 14:11:00,159 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 14:11:00,160 - signal_handler - INFO - Found contract address with 'pump' suffix: ********************************************
2025-06-12 14:11:00,160 - signal_handler - INFO - Using 'pump' address as highest priority: ********************************************
2025-06-12 14:11:00,160 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: ********************************************
2025-06-12 14:11:00,161 - signal_handler - INFO - Detected signal from OxyZen Calls for token: ********************************************
2025-06-12 14:11:00,161 - signal_handler - INFO - Extracted signal: Token=********************************************, Source=OxyZen Calls, Metrics Count=1
2025-06-12 14:11:00,161 - signal_handler - INFO - Added signal to queue: ******************************************** from OxyZen Calls with confidence 0.5, GMGN channel: False
2025-06-12 14:11:00,162 - signal_handler - INFO - Calling direct signal callback for ********************************************
2025-06-12 14:11:00,165 - signal_handler - INFO - Direct signal processing completed for ********************************************
2025-06-12 14:11:00,166 - signal_handler - INFO - Signal forwarded to bot controller: ******************************************** from OxyZen Calls (confidence: 0.50)
2025-06-12 14:11:00,166 - signal_handler - INFO - Retrieved signal from queue: ******************************************** from OxyZen Calls
2025-06-12 14:11:00,168 - signal_handler - INFO - [msg_1749717660167_8592] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 14:11:00

 Token CA: 3SKByWwbwNE6sZWBnWh7wSnA8zzHp6FLhKyiV6nLp...
2025-06-12 14:11:00,168 - signal_handler - INFO - [msg_1749717660167_8592] Message queued with normal priority
2025-06-12 14:11:07,527 - signal_handler - INFO - [msg_1749717667527_8484] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW LIQUIDITY] [?] 2025-06-12 14:11:07

 Token: 3SKByWwbwNE6sZWBnWh7wSnA8z...
2025-06-12 14:11:07,528 - signal_handler - INFO - [msg_1749717667527_8484] Message queued with normal priority
2025-06-12 14:11:07,528 - signal_handler - INFO - [msg_1749717660167_8592] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:11:07,613 - signal_handler - INFO - [msg_1749717660167_8592] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:11:07,746 - signal_handler - INFO - [msg_1749717660167_8592] Message sent directly successfully using entity
2025-06-12 14:11:07,747 - signal_handler - INFO - [msg_1749717660167_8592] Message sent successfully via direct send method
2025-06-12 14:11:07,747 - signal_handler - INFO - [msg_1749717660167_8592] Message sent successfully on attempt 1
2025-06-12 14:11:07,747 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 14:11:08,257 - signal_handler - INFO - [msg_1749717667527_8484] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:11:08,348 - signal_handler - INFO - [msg_1749717667527_8484] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:11:08,476 - signal_handler - INFO - [msg_1749717667527_8484] Message sent directly successfully using entity
2025-06-12 14:11:08,476 - signal_handler - INFO - [msg_1749717667527_8484] Message sent successfully via direct send method
2025-06-12 14:11:08,477 - signal_handler - INFO - [msg_1749717667527_8484] Message sent successfully on attempt 1
2025-06-12 14:11:32,949 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$11.1K(+219.7%)**
*...
2025-06-12 14:11:32,954 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 14:11:32,955 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 14:11:32,957 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 14:11:32,963 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 14:11:32,965 - signal_handler - INFO - Found contract address: 4w7Tv6kh7WrurrQ26C8H2KtzJ1oKB26HoWGiCjoSNh5u
2025-06-12 14:11:32,967 - signal_handler - INFO - Found contract address: CyMKYAowtmJskfjjZYMUM71sKhXGqXykrp2mb1QkxmpH
2025-06-12 14:11:32,968 - signal_handler - INFO - Found 2 unique token addresses
2025-06-12 14:11:32,969 - signal_handler - INFO - Token address: 4w7Tv6kh7WrurrQ26C8H2KtzJ1oKB26HoWGiCjoSNh5u
2025-06-12 14:11:32,969 - signal_handler - INFO - Token address: CyMKYAowtmJskfjjZYMUM71sKhXGqXykrp2mb1QkxmpH
2025-06-12 14:11:32,970 - signal_handler - INFO - Detected GMGN format
2025-06-12 14:11:32,971 - signal_handler - INFO - Found token symbol: 11
2025-06-12 14:11:32,971 - signal_handler - INFO - Found FDV: 11.1K - 11.10 (+219.7%)
2025-06-12 14:11:32,971 - signal_handler - INFO - Detected GMGN channel signal for token: 4w7Tv6kh7WrurrQ26C8H2KtzJ1oKB26HoWGiCjoSNh5u
2025-06-12 14:11:32,972 - signal_handler - INFO - Extracted signal: Token=4w7Tv6kh7WrurrQ26C8H2KtzJ1oKB26HoWGiCjoSNh5u, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 14:11:32,972 - signal_handler - INFO - Added signal to queue: 4w7Tv6kh7WrurrQ26C8H2KtzJ1oKB26HoWGiCjoSNh5u from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 14:11:32,973 - signal_handler - INFO - Calling direct signal callback for 4w7Tv6kh7WrurrQ26C8H2KtzJ1oKB26HoWGiCjoSNh5u
2025-06-12 14:11:32,980 - signal_handler - INFO - Direct signal processing completed for 4w7Tv6kh7WrurrQ26C8H2KtzJ1oKB26HoWGiCjoSNh5u
2025-06-12 14:11:32,981 - signal_handler - INFO - Signal forwarded to bot controller: 4w7Tv6kh7WrurrQ26C8H2KtzJ1oKB26HoWGiCjoSNh5u from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 14:11:32,981 - signal_handler - INFO - Retrieved signal from queue: 4w7Tv6kh7WrurrQ26C8H2KtzJ1oKB26HoWGiCjoSNh5u from solana signal alert - gmgn
2025-06-12 14:11:32,983 - signal_handler - INFO - [msg_1749717692983_7987] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 14:11:32

 Token CA: 4w7Tv6kh7WrurrQ26C8H2KtzJ1oKB26HoWGiCjoSN...
2025-06-12 14:11:32,984 - signal_handler - INFO - [msg_1749717692983_7987] Message queued with normal priority
2025-06-12 14:11:40,247 - signal_handler - INFO - [msg_1749717692983_7987] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:11:40,346 - signal_handler - INFO - [msg_1749717700346_5456] Queuing Telegram message: [WARNING] <b>Position Monitoring Error</b>

'BotController' object has no attribute 'monitoring_inte...
2025-06-12 14:11:40,347 - signal_handler - INFO - [msg_1749717700346_5456] Message queued with normal priority
2025-06-12 14:11:40,401 - signal_handler - INFO - [msg_1749717692983_7987] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:11:40,497 - signal_handler - INFO - [msg_1749717692983_7987] Message sent directly successfully using entity
2025-06-12 14:11:40,497 - signal_handler - INFO - [msg_1749717692983_7987] Message sent successfully via direct send method
2025-06-12 14:11:40,497 - signal_handler - INFO - [msg_1749717692983_7987] Message sent successfully on attempt 1
2025-06-12 14:11:40,498 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 14:11:40,749 - signal_handler - INFO - [msg_1749717700748_6898] [SELL NOTIFICATION]: [GREEN] [SIM BUY EXECUTED] [?] 2025-06-12 14:11:40

 Signal Source: solana signal alert - gmgn
 Toke...
2025-06-12 14:11:40,751 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749717700748_6898.txt
2025-06-12 14:11:40,751 - signal_handler - INFO - [msg_1749717700748_6898] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:11:40,805 - signal_handler - INFO - [msg_1749717700748_6898] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:11:40,899 - signal_handler - INFO - [msg_1749717700748_6898] Message sent directly successfully using entity
2025-06-12 14:11:40,900 - signal_handler - INFO - [msg_1749717700748_6898] Message sent directly successfully
2025-06-12 14:11:40,900 - signal_handler - INFO - [msg_1749717700900_5148] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (SIMULATION) - 4W7TV6KH

 Timestamp: 2025-06-12 14:11:40
[REPEAT] Tr...
2025-06-12 14:11:40,901 - signal_handler - INFO - [msg_1749717700900_5148] Message queued with normal priority
2025-06-12 14:11:40,998 - signal_handler - INFO - [msg_1749717700346_5456] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:11:41,050 - signal_handler - INFO - [msg_1749717700346_5456] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:11:41,170 - signal_handler - INFO - [msg_1749717700346_5456] Message sent directly successfully using entity
2025-06-12 14:11:41,170 - signal_handler - INFO - [msg_1749717700346_5456] Message sent successfully via direct send method
2025-06-12 14:11:41,171 - signal_handler - INFO - [msg_1749717700346_5456] Message sent successfully on attempt 1
2025-06-12 14:11:41,172 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 14:11:41,684 - signal_handler - INFO - [msg_1749717700900_5148] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:11:41,747 - signal_handler - INFO - [msg_1749717700900_5148] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:11:41,853 - signal_handler - INFO - [msg_1749717700900_5148] Message sent directly successfully using entity
2025-06-12 14:11:41,854 - signal_handler - INFO - [msg_1749717700900_5148] Message sent successfully via direct send method
2025-06-12 14:11:41,854 - signal_handler - INFO - [msg_1749717700900_5148] Message sent successfully on attempt 1
2025-06-12 14:11:42,413 - signal_handler - INFO - [msg_1749717702413_9209] Queuing Telegram message: [WARNING] <b>Position Monitoring Error</b>

'BotController' object has no attribute 'monitoring_inte...
2025-06-12 14:11:42,414 - signal_handler - INFO - [msg_1749717702413_9209] Message queued with normal priority
2025-06-12 14:11:42,414 - signal_handler - INFO - [msg_1749717702413_9209] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:11:42,466 - signal_handler - INFO - [msg_1749717702413_9209] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:11:42,553 - signal_handler - INFO - [msg_1749717702413_9209] Message sent directly successfully using entity
2025-06-12 14:11:42,554 - signal_handler - INFO - [msg_1749717702413_9209] Message sent successfully via direct send method
2025-06-12 14:11:42,554 - signal_handler - INFO - [msg_1749717702413_9209] Message sent successfully on attempt 1
2025-06-12 14:11:44,495 - signal_handler - INFO - [msg_1749717704495_5175] Queuing Telegram message: [WARNING] <b>Position Monitoring Error</b>

'BotController' object has no attribute 'monitoring_inte...
2025-06-12 14:11:44,496 - signal_handler - INFO - [msg_1749717704495_5175] Message queued with normal priority
2025-06-12 14:11:44,497 - signal_handler - INFO - [msg_1749717704495_5175] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:11:44,553 - signal_handler - INFO - [msg_1749717704495_5175] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:11:44,661 - signal_handler - INFO - [msg_1749717704495_5175] Message sent directly successfully using entity
2025-06-12 14:11:44,662 - signal_handler - INFO - [msg_1749717704495_5175] Message sent successfully via direct send method
2025-06-12 14:11:44,663 - signal_handler - INFO - [msg_1749717704495_5175] Message sent successfully on attempt 1
2025-06-12 14:11:46,586 - signal_handler - INFO - [msg_1749717706586_2710] Queuing Telegram message: [WARNING] <b>Position Monitoring Error</b>

'BotController' object has no attribute 'monitoring_inte...
2025-06-12 14:11:46,587 - signal_handler - INFO - [msg_1749717706586_2710] Message queued with normal priority
2025-06-12 14:11:46,610 - signal_handler - INFO - Message queue processor active with 1 messages pending
2025-06-12 14:11:46,612 - signal_handler - INFO - [msg_1749717706586_2710] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:11:46,672 - signal_handler - INFO - [msg_1749717706586_2710] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:11:46,757 - signal_handler - INFO - [msg_1749717706586_2710] Message sent directly successfully using entity
2025-06-12 14:11:46,758 - signal_handler - INFO - [msg_1749717706586_2710] Message sent successfully via direct send method
2025-06-12 14:11:46,760 - signal_handler - INFO - [msg_1749717706586_2710] Message sent successfully on attempt 1
2025-06-12 14:11:48,669 - signal_handler - INFO - [msg_1749717708669_7333] Queuing Telegram message: [WARNING] <b>Position Monitoring Error</b>

'BotController' object has no attribute 'monitoring_inte...
2025-06-12 14:11:48,670 - signal_handler - INFO - [msg_1749717708669_7333] Message queued with normal priority
2025-06-12 14:11:48,671 - signal_handler - INFO - [msg_1749717708669_7333] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:11:48,688 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$13.7K(+288.8%)**
*...
2025-06-12 14:11:48,688 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 14:11:48,689 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 14:11:48,689 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 14:11:48,690 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 14:11:48,690 - signal_handler - INFO - Found contract address with 'pump' suffix: EVEqKRdnyopnWma4FwWNZZc5zzobpsmrQLiikD51pump
2025-06-12 14:11:48,691 - signal_handler - INFO - Found contract address: Cy5sfzcei2tKopkEYU2FebdibzmrnpKfPEMYQBwc4sZM
2025-06-12 14:11:48,691 - signal_handler - INFO - Using 'pump' address as highest priority: EVEqKRdnyopnWma4FwWNZZc5zzobpsmrQLiikD51pump
2025-06-12 14:11:48,693 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: EVEqKRdnyopnWma4FwWNZZc5zzobpsmrQLiikD51pump
2025-06-12 14:11:48,694 - signal_handler - INFO - Detected GMGN format
2025-06-12 14:11:48,695 - signal_handler - INFO - Found token symbol: 13
2025-06-12 14:11:48,695 - signal_handler - INFO - Found FDV: 13.7K - 13.70 (+288.8%)
2025-06-12 14:11:48,696 - signal_handler - INFO - Detected GMGN channel signal for token: EVEqKRdnyopnWma4FwWNZZc5zzobpsmrQLiikD51pump
2025-06-12 14:11:48,696 - signal_handler - INFO - Extracted signal: Token=EVEqKRdnyopnWma4FwWNZZc5zzobpsmrQLiikD51pump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 14:11:48,697 - signal_handler - INFO - Added signal to queue: EVEqKRdnyopnWma4FwWNZZc5zzobpsmrQLiikD51pump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 14:11:48,697 - signal_handler - INFO - Calling direct signal callback for EVEqKRdnyopnWma4FwWNZZc5zzobpsmrQLiikD51pump
2025-06-12 14:11:48,700 - signal_handler - INFO - Direct signal processing completed for EVEqKRdnyopnWma4FwWNZZc5zzobpsmrQLiikD51pump
2025-06-12 14:11:48,703 - signal_handler - INFO - Signal forwarded to bot controller: EVEqKRdnyopnWma4FwWNZZc5zzobpsmrQLiikD51pump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 14:11:48,703 - signal_handler - INFO - Retrieved signal from queue: EVEqKRdnyopnWma4FwWNZZc5zzobpsmrQLiikD51pump from solana signal alert - gmgn
2025-06-12 14:11:48,705 - signal_handler - INFO - [msg_1749717708704_3827] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 14:11:48

 Token CA: EVEqKRdnyopnWma4FwWNZZc5zzobpsmrQLiikD51p...
2025-06-12 14:11:48,705 - signal_handler - INFO - [msg_1749717708704_3827] Message queued with normal priority
2025-06-12 14:11:55,979 - signal_handler - INFO - [msg_1749717715979_2379] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] HIGH WHALE RISK] [?] 2025-06-12 14:11:55

 Token: EVEqKRdnyopnWma4FwWNZZc5...
2025-06-12 14:11:55,980 - signal_handler - INFO - [msg_1749717715979_2379] Message queued with normal priority
2025-06-12 14:11:56,056 - signal_handler - INFO - [msg_1749717716056_3093] Queuing Telegram message: [WARNING] <b>Position Monitoring Error</b>

'BotController' object has no attribute 'monitoring_inte...
2025-06-12 14:11:56,056 - signal_handler - INFO - [msg_1749717716056_3093] Message queued with normal priority
2025-06-12 14:11:56,056 - signal_handler - INFO - [msg_1749717708669_7333] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:11:56,147 - signal_handler - INFO - [msg_1749717708669_7333] Message sent directly successfully using entity
2025-06-12 14:11:56,147 - signal_handler - INFO - [msg_1749717708669_7333] Message sent successfully via direct send method
2025-06-12 14:11:56,148 - signal_handler - INFO - [msg_1749717708669_7333] Message sent successfully on attempt 1
2025-06-12 14:11:56,148 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 14:11:56,652 - signal_handler - INFO - [msg_1749717708704_3827] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:11:56,719 - signal_handler - INFO - [msg_1749717708704_3827] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:11:56,830 - signal_handler - INFO - [msg_1749717708704_3827] Message sent directly successfully using entity
2025-06-12 14:11:56,831 - signal_handler - INFO - [msg_1749717708704_3827] Message sent successfully via direct send method
2025-06-12 14:11:56,832 - signal_handler - INFO - [msg_1749717708704_3827] Message sent successfully on attempt 1
2025-06-12 14:11:56,833 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 1.50s (interval: 1.50s)
2025-06-12 14:11:58,180 - signal_handler - INFO - [msg_1749717718180_6441] Queuing Telegram message: [WARNING] <b>Position Monitoring Error</b>

'BotController' object has no attribute 'monitoring_inte...
2025-06-12 14:11:58,187 - signal_handler - INFO - [msg_1749717718180_6441] Message queued with normal priority
2025-06-12 14:11:58,341 - signal_handler - INFO - [msg_1749717715979_2379] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:11:58,397 - signal_handler - INFO - [msg_1749717715979_2379] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:11:58,498 - signal_handler - INFO - [msg_1749717715979_2379] Message sent directly successfully using entity
2025-06-12 14:11:58,498 - signal_handler - INFO - [msg_1749717715979_2379] Message sent successfully via direct send method
2025-06-12 14:11:58,499 - signal_handler - INFO - [msg_1749717715979_2379] Message sent successfully on attempt 1
2025-06-12 14:11:58,499 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 1.50s (interval: 1.50s)
2025-06-12 14:12:00,011 - signal_handler - INFO - [msg_1749717716056_3093] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:12:00,074 - signal_handler - INFO - [msg_1749717716056_3093] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:12:00,163 - signal_handler - INFO - [msg_1749717716056_3093] Message sent directly successfully using entity
2025-06-12 14:12:00,164 - signal_handler - INFO - [msg_1749717716056_3093] Message sent successfully via direct send method
2025-06-12 14:12:00,166 - signal_handler - INFO - [msg_1749717716056_3093] Message sent successfully on attempt 1
2025-06-12 14:12:00,167 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 1.50s (interval: 1.50s)
2025-06-12 14:12:00,271 - signal_handler - INFO - [msg_1749717720271_2538] Queuing Telegram message: [WARNING] <b>Position Monitoring Error</b>

'BotController' object has no attribute 'monitoring_inte...
2025-06-12 14:12:00,272 - signal_handler - INFO - [msg_1749717720271_2538] Message queued with normal priority
2025-06-12 14:12:01,672 - signal_handler - INFO - [msg_1749717718180_6441] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:12:01,729 - signal_handler - INFO - [msg_1749717718180_6441] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:12:01,812 - signal_handler - INFO - [msg_1749717718180_6441] Message sent directly successfully using entity
2025-06-12 14:12:01,812 - signal_handler - INFO - [msg_1749717718180_6441] Message sent successfully via direct send method
2025-06-12 14:12:01,813 - signal_handler - INFO - [msg_1749717718180_6441] Message sent successfully on attempt 1
2025-06-12 14:12:01,813 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 1.50s (interval: 1.50s)
2025-06-12 14:12:02,341 - signal_handler - INFO - [msg_1749717722341_7349] Queuing Telegram message: [WARNING] <b>Position Monitoring Error</b>

'BotController' object has no attribute 'monitoring_inte...
2025-06-12 14:12:02,342 - signal_handler - INFO - [msg_1749717722341_7349] Message queued with normal priority
2025-06-12 14:12:03,324 - signal_handler - INFO - [msg_1749717720271_2538] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:12:03,396 - signal_handler - INFO - [msg_1749717720271_2538] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:12:03,524 - signal_handler - INFO - [msg_1749717720271_2538] Message sent directly successfully using entity
2025-06-12 14:12:03,528 - signal_handler - INFO - [msg_1749717720271_2538] Message sent successfully via direct send method
2025-06-12 14:12:03,530 - signal_handler - INFO - [msg_1749717720271_2538] Message sent successfully on attempt 1
2025-06-12 14:12:03,532 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 1.50s (interval: 1.50s)
2025-06-12 14:12:05,164 - signal_handler - INFO - [msg_1749717722341_7349] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:12:05,239 - signal_handler - INFO - [msg_1749717722341_7349] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:12:11,189 - signal_handler - INFO - Received sell notification: [PURPLE] [SIM SELL [?] STOP LOSS HIT (-39.3% <= -25.0%)] [?] 2025-06-12 14:12:11
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]...
2025-06-12 14:12:11,190 - signal_handler - INFO - [msg_1749717731189_2873] [SELL NOTIFICATION]:  [SIM SELL [?] STOP LOSS HIT (-39.3% <= -25.0%)] [?] 2025-06-12 14:12:11

 Triggered by: solana sign...
2025-06-12 14:12:11,191 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749717731189_2873.txt
2025-06-12 14:12:11,192 - signal_handler - INFO - [msg_1749717731189_2873] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:12:11,199 - signal_handler - WARNING - [msg_1749717722341_7349] Entity resolution or send failed: , trying direct ID approach
2025-06-12 14:12:11,199 - signal_handler - INFO - [msg_1749717722341_7349] Sending message directly to channel ID: -1002525039395
2025-06-12 14:12:11,253 - signal_handler - INFO - [msg_1749717731189_2873] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:12:11,288 - signal_handler - INFO - [msg_1749717722341_7349] Message sent directly successfully using ID
2025-06-12 14:12:11,289 - signal_handler - INFO - [msg_1749717722341_7349] Message sent successfully via direct send method
2025-06-12 14:12:11,289 - signal_handler - INFO - [msg_1749717722341_7349] Message sent successfully on attempt 1
2025-06-12 14:12:11,349 - signal_handler - INFO - [msg_1749717731189_2873] Message sent directly successfully using entity
2025-06-12 14:12:11,350 - signal_handler - INFO - [msg_1749717731189_2873] Message sent directly successfully
2025-06-12 14:12:11,351 - signal_handler - INFO - [msg_1749717731351_4982] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-12 14:12:11
[REPEAT] Trade: SIMULATION

[MONEY...
2025-06-12 14:12:11,351 - signal_handler - INFO - [msg_1749717731351_4982] Message queued with normal priority
2025-06-12 14:12:11,354 - signal_handler - INFO - [msg_1749717731354_1865] Queuing Telegram message: [WARNING] <b>Position Monitoring Error</b>

'BotController' object has no attribute 'monitoring_inte...
2025-06-12 14:12:11,354 - signal_handler - INFO - [msg_1749717731354_1865] Message queued with normal priority
2025-06-12 14:12:11,403 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 1.39s (interval: 1.50s)
2025-06-12 14:12:12,796 - signal_handler - INFO - [msg_1749717731351_4982] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:12:12,938 - signal_handler - INFO - [msg_1749717731351_4982] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:12:13,088 - signal_handler - INFO - [msg_1749717731351_4982] Message sent directly successfully using entity
2025-06-12 14:12:13,089 - signal_handler - INFO - [msg_1749717731351_4982] Message sent successfully via direct send method
2025-06-12 14:12:13,089 - signal_handler - INFO - [msg_1749717731351_4982] Message sent successfully on attempt 1
2025-06-12 14:12:13,090 - signal_handler - INFO - Message queue processor active with 1 messages pending
2025-06-12 14:12:13,090 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 1.50s (interval: 1.50s)
2025-06-12 14:12:14,604 - signal_handler - INFO - [msg_1749717731354_1865] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:12:14,699 - signal_handler - INFO - [msg_1749717731354_1865] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:12:14,804 - signal_handler - INFO - [msg_1749717731354_1865] Message sent directly successfully using entity
2025-06-12 14:12:14,805 - signal_handler - INFO - [msg_1749717731354_1865] Message sent successfully via direct send method
2025-06-12 14:12:14,805 - signal_handler - INFO - [msg_1749717731354_1865] Message sent successfully on attempt 1
