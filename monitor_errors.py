#!/usr/bin/env python3
"""
Error Log Monitor - Real-time analysis of production errors
Usage: python monitor_errors.py
"""

import json
import time
from datetime import datetime, timedelta
from collections import defaultdict, Counter
import os

class ErrorMonitor:
    def __init__(self, error_file="production_errors.jsonl"):
        self.error_file = error_file
        
    def tail_error_logs(self, lines=10):
        """Get the last N error log entries"""
        if not os.path.exists(self.error_file):
            print(f"❌ Error log file not found: {self.error_file}")
            return []
        
        try:
            with open(self.error_file, 'r') as f:
                all_lines = f.readlines()
                recent_lines = all_lines[-lines:] if len(all_lines) >= lines else all_lines
                
                error_data = []
                for line in recent_lines:
                    try:
                        data = json.loads(line.strip())
                        error_data.append(data)
                    except json.JSONDecodeError:
                        continue
                        
                return error_data
        except Exception as e:
            print(f"❌ Error reading error logs: {e}")
            return []
    
    def analyze_error_patterns(self, hours=24):
        """Analyze error patterns and frequencies"""
        if not os.path.exists(self.error_file):
            return None
            
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        error_types = Counter()
        error_modules = Counter()
        error_functions = Counter()
        severity_counts = Counter()
        hourly_counts = defaultdict(int)
        
        try:
            with open(self.error_file, 'r') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        timestamp = datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00'))
                        
                        if timestamp >= cutoff_time:
                            error_types[data['error_type']] += 1
                            error_modules[data['module']] += 1
                            error_functions[f"{data['module']}.{data['function']}"] += 1
                            severity_counts[data['severity']] += 1
                            
                            # Group by hour for trend analysis
                            hour_key = timestamp.strftime('%Y-%m-%d %H:00')
                            hourly_counts[hour_key] += 1
                            
                    except (json.JSONDecodeError, KeyError):
                        continue
        except Exception as e:
            print(f"❌ Error analyzing error patterns: {e}")
            return None
        
        return {
            'period_hours': hours,
            'total_errors': sum(error_types.values()),
            'error_types': dict(error_types.most_common(10)),
            'error_modules': dict(error_modules.most_common(10)),
            'error_functions': dict(error_functions.most_common(10)),
            'severity_counts': dict(severity_counts),
            'hourly_trend': dict(sorted(hourly_counts.items()))
        }
    
    def find_error_spikes(self, hours=24, spike_threshold=10):
        """Find error spikes in the last N hours"""
        if not os.path.exists(self.error_file):
            return []
            
        cutoff_time = datetime.now() - timedelta(hours=hours)
        hourly_counts = defaultdict(int)
        
        try:
            with open(self.error_file, 'r') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        timestamp = datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00'))
                        
                        if timestamp >= cutoff_time:
                            hour_key = timestamp.strftime('%Y-%m-%d %H:00')
                            hourly_counts[hour_key] += 1
                            
                    except (json.JSONDecodeError, KeyError):
                        continue
        except Exception as e:
            print(f"❌ Error finding spikes: {e}")
            return []
        
        spikes = []
        for hour, count in hourly_counts.items():
            if count >= spike_threshold:
                spikes.append({'hour': hour, 'error_count': count})
        
        return sorted(spikes, key=lambda x: x['error_count'], reverse=True)
    
    def get_critical_errors(self, hours=24):
        """Get critical/fatal errors from the last N hours"""
        if not os.path.exists(self.error_file):
            return []
            
        cutoff_time = datetime.now() - timedelta(hours=hours)
        critical_errors = []
        
        try:
            with open(self.error_file, 'r') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        timestamp = datetime.fromisoformat(data['timestamp'].replace('Z', '+00:00'))
                        
                        if timestamp >= cutoff_time and data['severity'] in ['critical', 'fatal']:
                            critical_errors.append(data)
                            
                    except (json.JSONDecodeError, KeyError):
                        continue
        except Exception as e:
            print(f"❌ Error getting critical errors: {e}")
            return []
        
        return sorted(critical_errors, key=lambda x: x['timestamp'], reverse=True)
    
    def print_error_summary(self):
        """Print comprehensive error summary"""
        print("\n" + "="*80)
        print("🚨 PRODUCTION ERROR SUMMARY")
        print("="*80)
        
        # Recent errors
        recent_errors = self.tail_error_logs(5)
        if recent_errors:
            print("📋 RECENT ERRORS (Last 5):")
            for error in recent_errors:
                timestamp = error['timestamp']
                error_type = error['error_type']
                module = error['module']
                function = error['function']
                severity = error['severity'].upper()
                message = error['error_message'][:60] + "..." if len(error['error_message']) > 60 else error['error_message']
                
                severity_emoji = "🔴" if severity in ['CRITICAL', 'FATAL'] else "🟡" if severity == 'ERROR' else "🟠"
                print(f"   {severity_emoji} {timestamp}: [{severity}] {module}.{function}() - {error_type}")
                print(f"      {message}")
        else:
            print("✅ No recent errors found")
        
        # 24-hour analysis
        analysis = self.analyze_error_patterns(24)
        if analysis and analysis['total_errors'] > 0:
            print(f"\n📊 24-HOUR ERROR ANALYSIS ({analysis['total_errors']} total errors):")
            
            print("   🔥 Top Error Types:")
            for error_type, count in list(analysis['error_types'].items())[:5]:
                print(f"      {error_type}: {count}")
            
            print("   📦 Top Error Modules:")
            for module, count in list(analysis['error_modules'].items())[:5]:
                print(f"      {module}: {count}")
            
            print("   ⚠️  Severity Breakdown:")
            for severity, count in analysis['severity_counts'].items():
                print(f"      {severity.upper()}: {count}")
        
        # Error spikes
        spikes = self.find_error_spikes(24, 5)
        if spikes:
            print(f"\n🚨 ERROR SPIKES (>5 errors/hour):")
            for spike in spikes[:3]:
                print(f"   {spike['hour']}: {spike['error_count']} errors")
        
        # Critical errors
        critical = self.get_critical_errors(24)
        if critical:
            print(f"\n🔴 CRITICAL ERRORS (Last 24h): {len(critical)}")
            for error in critical[:3]:
                print(f"   {error['timestamp']}: {error['error_type']} in {error['module']}")
        else:
            print("\n✅ No critical errors in last 24 hours")
        
        print("="*80)

def main():
    monitor = ErrorMonitor()
    
    print("🚨 PRODUCTION ERROR MONITOR")
    print("Commands:")
    print("  1. Recent errors")
    print("  2. Error patterns (24h)")
    print("  3. Critical errors")
    print("  4. Error spikes")
    print("  5. Live monitoring")
    print("  6. Exit")
    
    while True:
        try:
            choice = input("\nEnter choice (1-6): ").strip()
            
            if choice == '1':
                recent = monitor.tail_error_logs(10)
                print(f"\n📋 LAST 10 ERRORS:")
                for error in recent:
                    timestamp = error['timestamp']
                    error_type = error['error_type']
                    module = error['module']
                    severity = error['severity'].upper()
                    message = error['error_message'][:80] + "..." if len(error['error_message']) > 80 else error['error_message']
                    print(f"   [{severity}] {timestamp}: {error_type} in {module}")
                    print(f"      {message}")
            
            elif choice == '2':
                analysis = monitor.analyze_error_patterns(24)
                if analysis:
                    print(f"\n📊 ERROR PATTERNS (24 hours, {analysis['total_errors']} total):")
                    print("Top Error Types:")
                    for error_type, count in analysis['error_types'].items():
                        print(f"   {error_type}: {count}")
                    print("Top Modules:")
                    for module, count in analysis['error_modules'].items():
                        print(f"   {module}: {count}")
                else:
                    print("✅ No errors found in last 24 hours")
            
            elif choice == '3':
                critical = monitor.get_critical_errors(24)
                if critical:
                    print(f"\n🔴 CRITICAL ERRORS ({len(critical)} found):")
                    for error in critical:
                        print(f"   {error['timestamp']}: {error['error_type']}")
                        print(f"      Module: {error['module']}.{error['function']}()")
                        print(f"      Message: {error['error_message']}")
                        print()
                else:
                    print("✅ No critical errors in last 24 hours")
            
            elif choice == '4':
                spikes = monitor.find_error_spikes(24, 3)
                if spikes:
                    print(f"\n🚨 ERROR SPIKES (>3 errors/hour):")
                    for spike in spikes:
                        print(f"   {spike['hour']}: {spike['error_count']} errors")
                else:
                    print("✅ No error spikes detected")
            
            elif choice == '5':
                print("\n🔴 LIVE ERROR MONITORING (Ctrl+C to stop)")
                try:
                    while True:
                        os.system('cls' if os.name == 'nt' else 'clear')
                        monitor.print_error_summary()
                        time.sleep(30)
                except KeyboardInterrupt:
                    print("\n✅ Live monitoring stopped")
            
            elif choice == '6':
                print("👋 Goodbye!")
                break
            
            else:
                print("❌ Invalid choice")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
