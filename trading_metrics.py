#!/usr/bin/env python3
"""
Trading Metrics Collector - Custom KPIs for trading performance
Usage: Import and use in your trading bot
"""

import json
import time
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
import os

@dataclass
class TradingMetrics:
    """Trading performance metrics"""
    timestamp: str
    
    # Signal Processing Metrics
    signals_received_1h: int
    signals_processed_1h: int
    signals_filtered_out_1h: int
    signal_processing_time_avg_ms: float
    
    # Trading Performance Metrics
    trades_executed_1h: int
    trades_successful_1h: int
    trades_failed_1h: int
    win_rate_24h: float
    
    # Financial Metrics
    pnl_1h: float
    pnl_24h: float
    pnl_total: float
    capital_utilization_percent: float
    
    # Risk Metrics
    max_drawdown_24h: float
    positions_at_risk: int
    avg_position_hold_time_minutes: float
    
    # Performance Metrics
    api_response_time_avg_ms: float
    api_error_rate_1h: float
    telegram_uptime_percent_24h: float
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

class TradingMetricsCollector:
    """Collect and track trading-specific metrics"""
    
    def __init__(self, metrics_file="trading_metrics.jsonl"):
        self.metrics_file = metrics_file
        self.start_time = time.time()
        
        # Counters for current hour
        self.signals_received = 0
        self.signals_processed = 0
        self.signals_filtered = 0
        self.trades_executed = 0
        self.trades_successful = 0
        self.trades_failed = 0
        
        # Performance tracking
        self.signal_processing_times = []
        self.api_response_times = []
        self.api_errors_1h = 0
        
        # Reset counters every hour
        self.last_reset = time.time()
        
    def record_signal_received(self):
        """Record that a signal was received"""
        self._check_hourly_reset()
        self.signals_received += 1
    
    def record_signal_processed(self, processing_time_ms: float):
        """Record that a signal was processed"""
        self._check_hourly_reset()
        self.signals_processed += 1
        self.signal_processing_times.append(processing_time_ms)
    
    def record_signal_filtered(self, reason: str):
        """Record that a signal was filtered out"""
        self._check_hourly_reset()
        self.signals_filtered += 1
    
    def record_trade_executed(self, success: bool):
        """Record trade execution result"""
        self._check_hourly_reset()
        self.trades_executed += 1
        if success:
            self.trades_successful += 1
        else:
            self.trades_failed += 1
    
    def record_api_call(self, response_time_ms: float, success: bool):
        """Record API call performance"""
        self._check_hourly_reset()
        self.api_response_times.append(response_time_ms)
        if not success:
            self.api_errors_1h += 1
    
    def _check_hourly_reset(self):
        """Reset hourly counters if an hour has passed"""
        current_time = time.time()
        if current_time - self.last_reset >= 3600:  # 1 hour
            self._reset_hourly_counters()
            self.last_reset = current_time
    
    def _reset_hourly_counters(self):
        """Reset all hourly counters"""
        self.signals_received = 0
        self.signals_processed = 0
        self.signals_filtered = 0
        self.trades_executed = 0
        self.trades_successful = 0
        self.trades_failed = 0
        self.signal_processing_times = []
        self.api_response_times = []
        self.api_errors_1h = 0
    
    def get_trading_metrics(self, state_manager=None, bot_controller=None) -> TradingMetrics:
        """Generate current trading metrics"""
        try:
            # Calculate averages
            avg_signal_processing = (
                sum(self.signal_processing_times) / len(self.signal_processing_times)
                if self.signal_processing_times else 0.0
            )
            
            avg_api_response = (
                sum(self.api_response_times) / len(self.api_response_times)
                if self.api_response_times else 0.0
            )
            
            api_error_rate = (
                (self.api_errors_1h / len(self.api_response_times)) * 100
                if self.api_response_times else 0.0
            )
            
            # Get financial metrics from state manager
            pnl_1h = 0.0
            pnl_24h = 0.0
            pnl_total = 0.0
            capital_utilization = 0.0
            win_rate_24h = 0.0
            max_drawdown_24h = 0.0
            positions_at_risk = 0
            avg_hold_time = 0.0
            telegram_uptime = 100.0
            
            if state_manager:
                try:
                    pnl_total = getattr(state_manager, 'total_profit', 0.0)
                    capital_utilization = (
                        (getattr(state_manager, 'total_sol_capital', 1.0) - 
                         getattr(state_manager, 'available_sol', 1.0)) / 
                        getattr(state_manager, 'total_sol_capital', 1.0) * 100
                    )
                    
                    # Count positions at risk (negative PnL)
                    positions = getattr(state_manager, 'positions', {})
                    positions_at_risk = sum(
                        1 for pos in positions.values() 
                        if pos.get('current_pnl', 0) < -5.0  # More than 5% loss
                    )
                    
                    # Calculate win rate from recent trades
                    total_trades = getattr(state_manager, 'total_trades', 0)
                    if total_trades > 0:
                        win_rate_24h = (self.trades_successful / max(1, self.trades_executed)) * 100
                        
                except Exception as e:
                    pass  # Use defaults if state manager access fails
            
            return TradingMetrics(
                timestamp=datetime.now(timezone.utc).isoformat(),
                signals_received_1h=self.signals_received,
                signals_processed_1h=self.signals_processed,
                signals_filtered_out_1h=self.signals_filtered,
                signal_processing_time_avg_ms=avg_signal_processing,
                trades_executed_1h=self.trades_executed,
                trades_successful_1h=self.trades_successful,
                trades_failed_1h=self.trades_failed,
                win_rate_24h=win_rate_24h,
                pnl_1h=pnl_1h,
                pnl_24h=pnl_24h,
                pnl_total=pnl_total,
                capital_utilization_percent=capital_utilization,
                max_drawdown_24h=max_drawdown_24h,
                positions_at_risk=positions_at_risk,
                avg_position_hold_time_minutes=avg_hold_time,
                api_response_time_avg_ms=avg_api_response,
                api_error_rate_1h=api_error_rate,
                telegram_uptime_percent_24h=telegram_uptime
            )
            
        except Exception as e:
            # Return default metrics if calculation fails
            return TradingMetrics(
                timestamp=datetime.now(timezone.utc).isoformat(),
                signals_received_1h=0, signals_processed_1h=0, signals_filtered_out_1h=0,
                signal_processing_time_avg_ms=0.0, trades_executed_1h=0, trades_successful_1h=0,
                trades_failed_1h=0, win_rate_24h=0.0, pnl_1h=0.0, pnl_24h=0.0, pnl_total=0.0,
                capital_utilization_percent=0.0, max_drawdown_24h=0.0, positions_at_risk=0,
                avg_position_hold_time_minutes=0.0, api_response_time_avg_ms=0.0,
                api_error_rate_1h=0.0, telegram_uptime_percent_24h=100.0
            )
    
    def log_metrics(self, metrics: TradingMetrics):
        """Log metrics to file"""
        try:
            with open(self.metrics_file, 'a') as f:
                f.write(json.dumps(metrics.to_dict()) + '\n')
        except Exception as e:
            print(f"Error logging trading metrics: {e}")
    
    def get_performance_summary(self, hours=24) -> Dict[str, Any]:
        """Get performance summary from logged metrics"""
        if not os.path.exists(self.metrics_file):
            return {}
        
        try:
            metrics_data = []
            with open(self.metrics_file, 'r') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        metrics_data.append(data)
                    except json.JSONDecodeError:
                        continue
            
            if not metrics_data:
                return {}
            
            # Get recent data
            recent_data = metrics_data[-hours:] if len(metrics_data) >= hours else metrics_data
            
            # Calculate summary statistics
            total_signals = sum(d['signals_received_1h'] for d in recent_data)
            total_trades = sum(d['trades_executed_1h'] for d in recent_data)
            successful_trades = sum(d['trades_successful_1h'] for d in recent_data)
            
            avg_processing_time = (
                sum(d['signal_processing_time_avg_ms'] for d in recent_data) / len(recent_data)
                if recent_data else 0
            )
            
            current_pnl = recent_data[-1]['pnl_total'] if recent_data else 0
            
            return {
                'period_hours': hours,
                'total_signals_received': total_signals,
                'total_trades_executed': total_trades,
                'successful_trades': successful_trades,
                'win_rate_percent': (successful_trades / max(1, total_trades)) * 100,
                'avg_signal_processing_ms': avg_processing_time,
                'current_total_pnl': current_pnl,
                'signals_per_hour': total_signals / max(1, hours),
                'trades_per_hour': total_trades / max(1, hours)
            }
            
        except Exception as e:
            print(f"Error calculating performance summary: {e}")
            return {}

# Global metrics collector instance
_trading_metrics = None

def get_trading_metrics_collector() -> TradingMetricsCollector:
    """Get or create trading metrics collector"""
    global _trading_metrics
    if _trading_metrics is None:
        _trading_metrics = TradingMetricsCollector()
    return _trading_metrics

# Convenience functions for easy integration
def record_signal_received():
    get_trading_metrics_collector().record_signal_received()

def record_signal_processed(processing_time_ms: float):
    get_trading_metrics_collector().record_signal_processed(processing_time_ms)

def record_signal_filtered(reason: str):
    get_trading_metrics_collector().record_signal_filtered(reason)

def record_trade_executed(success: bool):
    get_trading_metrics_collector().record_trade_executed(success)

def record_api_call(response_time_ms: float, success: bool):
    get_trading_metrics_collector().record_api_call(response_time_ms, success)

def log_current_metrics(state_manager=None, bot_controller=None):
    """Log current trading metrics"""
    collector = get_trading_metrics_collector()
    metrics = collector.get_trading_metrics(state_manager, bot_controller)
    collector.log_metrics(metrics)

if __name__ == "__main__":
    # Demo usage
    collector = TradingMetricsCollector()
    
    # Simulate some activity
    collector.record_signal_received()
    collector.record_signal_processed(150.0)
    collector.record_trade_executed(True)
    
    # Get and display metrics
    metrics = collector.get_trading_metrics()
    print("📊 Current Trading Metrics:")
    print(json.dumps(metrics.to_dict(), indent=2))
