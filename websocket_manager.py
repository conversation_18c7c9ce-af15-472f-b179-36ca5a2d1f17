import asyncio
import json
import logging
import time
from typing import Dict, Any, Callable, List
from collections import deque
import aiohttp
from datetime import datetime

# OPTIMIZED: Added deque import for efficient buffer management

class WebSocketMessage:
    """Represents a WebSocket message with metadata"""

    def __init__(self, data: Dict[str, Any], source: str = "unknown"):
        self.data = data
        self.source = source
        self.timestamp = datetime.now()
        self.processed = False

    def __str__(self):
        return f"WebSocketMessage(source={self.source}, processed={self.processed}, data={self.data})"

class WebSocketManager:
    """Enhanced WebSocket manager with improved connection handling and message queuing"""

    def __init__(self, config):
        self.config = config
        self.session = None
        self.ws = None
        self.connected = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 10  # Increased from 5
        self.reconnect_delay = 5
        self.heartbeat_interval = 30  # Seconds between heartbeats
        self.last_heartbeat = 0
        self.last_message_received = 0

        # Message handling
        self.message_handlers = {}  # type -> [handlers]
        self.message_queue = asyncio.Queue()
        self.subscriptions = set()  # Track active subscriptions
        self.message_processor_task = None
        self.heartbeat_task = None
        # SURGICAL FIX: Track listener task to prevent resource leak
        self.listener_task = None
        # SURGICAL FIX: Track reconnection task to prevent multiple reconnections
        self.reconnect_task = None

        # Statistics
        self.stats = {
            'messages_received': 0,
            'messages_sent': 0,
            'errors': 0,
            'reconnects': 0,
            'last_error': None,
            'connected_since': None
        }

        self.logger = logging.getLogger('websocket_manager')

        # Configuration
        self.ws_urls = self._get_ws_urls()
        self.current_url_index = 0
        self.auto_reconnect = True
        self.message_buffer_size = 1000
        # OPTIMIZED: Use deque for O(1) append/pop operations instead of O(n) list slicing
        self.message_buffer = deque(maxlen=self.message_buffer_size)

        # Token tracking
        self.monitored_tokens = set()  # Track tokens we're monitoring

    def _get_ws_urls(self) -> List[str]:
        """Get WebSocket URLs from config with fallbacks"""
        primary_url = self.config.get('websocket', 'url', default=None)
        backup_urls = self.config.get('websocket', 'backup_urls', default=[])

        urls = []
        if primary_url:
            urls.append(primary_url)
        if isinstance(backup_urls, list):
            urls.extend(backup_urls)

        # Add default fallback if no URLs configured
        if not urls:
            urls = [
                # DexScreener will use HTTP polling
                "wss://example.com/disabled"  # Placeholder that will be skipped
            ]

        return urls

    async def connect(self):
        """Connect to WebSocket server with improved error handling"""
        if self.connected and self.ws and not self.ws.closed:
            self.logger.debug("Already connected to WebSocket")
            return True

        # Clean up any existing connection
        await self._cleanup()

        # Get the next URL to try
        if not self.ws_urls:
            self.logger.error("No WebSocket URLs configured")
            return False

        # Track the starting index to detect if we've tried all URLs
        starting_index = self.current_url_index
        tried_all_urls = False

        while True:
            url = self.ws_urls[self.current_url_index]
            self.current_url_index = (self.current_url_index + 1) % len(self.ws_urls)

            # Check if we've tried all URLs (detect when we wrap around to the starting index)
            if self.current_url_index == starting_index:
                tried_all_urls = True

            # Skip connections to disabled services
            if any(service in url for service in ["dexscreener.com", "example.com"]):
                # Log appropriate warning based on the URL (only once per URL)
                if "dexscreener.com" in url:
                    self.logger.warning("DexScreener doesn't support WebSocket connections. Using HTTP polling instead.")
                elif "example.com" in url:
                    self.logger.warning("Example URL is disabled. Skipping connection.")

                # If we've tried all URLs and none are supported, break the loop
                if tried_all_urls:
                    self.logger.warning("Tried all WebSocket URLs, none are supported.")
                    return False

                # Continue to the next URL
                continue

            # Skip placeholder URLs
            if "example.com/disabled" in url:
                self.logger.warning("No valid WebSocket URLs configured. WebSocket functionality will be disabled.")
                return False

            # If we get here, we have a URL to try
            break

        self.logger.info(f"Connecting to WebSocket: {url}")

        try:
            # Create a new session if needed
            if not self.session or self.session.closed:
                self.session = aiohttp.ClientSession()

            # Connect with timeout
            self.ws = await self.session.ws_connect(
                url,
                timeout=30,
                heartbeat=self.heartbeat_interval,
                autoclose=False,
                autoping=True
            )

            self.connected = True
            self.reconnect_attempts = 0
            self.stats['connected_since'] = datetime.now().isoformat()
            self.last_message_received = time.time()
            self.last_heartbeat = time.time()

            self.logger.info(f"WebSocket connected to {url}")

            # Start message processor if not running
            if not self.message_processor_task or self.message_processor_task.done():
                self.message_processor_task = asyncio.create_task(self._process_message_queue())

            # Start heartbeat task if not running
            if not self.heartbeat_task or self.heartbeat_task.done():
                self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())

            # SURGICAL FIX: Track listener task for proper cleanup
            self.listener_task = asyncio.create_task(self._listen())

            # Resubscribe to active channels
            await self._resubscribe()

            # Set up message handlers for different message types

            return True

        except Exception as e:
            self.logger.error(f"WebSocket connection failed: {e}")
            self.stats['last_error'] = f"{datetime.now().isoformat()}: Connection failed: {str(e)}"
            self.stats['errors'] += 1
            self.connected = False

            if self.auto_reconnect:
                # SURGICAL FIX: Track reconnection task and cancel existing one
                if self.reconnect_task and not self.reconnect_task.done():
                    self.reconnect_task.cancel()
                self.reconnect_task = asyncio.create_task(self._handle_reconnect())

            return False

    async def disconnect(self):
        """Disconnect from WebSocket server with proper cleanup"""
        self.auto_reconnect = False  # Disable auto reconnect
        await self._cleanup()
        self.logger.info("WebSocket disconnected")
        return True

    async def _cleanup(self):
        """Clean up WebSocket resources"""
        self.connected = False

        # Close WebSocket
        if self.ws:
            try:
                await self.ws.close()
            except Exception as e:
                self.logger.warning(f"Error closing WebSocket: {e}")
            self.ws = None

        # SURGICAL FIX: Cancel all tracked tasks including listener and reconnect
        for task_name in ['message_processor_task', 'heartbeat_task', 'listener_task', 'reconnect_task']:
            task = getattr(self, task_name, None)
            if task and not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                setattr(self, task_name, None)

    async def _listen(self):
        """Listen for WebSocket messages with improved error handling"""
        if not self.ws:
            self.logger.error("Cannot listen: WebSocket not connected")
            return

        while self.connected and not self.ws.closed:
            try:
                msg = await self.ws.receive(timeout=self.heartbeat_interval * 2)
                self.last_message_received = time.time()

                if msg.type == aiohttp.WSMsgType.TEXT:
                    try:
                        data = json.loads(msg.data)
                        self.stats['messages_received'] += 1

                        # Add to buffer with timestamp
                        message = WebSocketMessage(data, source=self.ws.url.host if self.ws.url else "unknown")
                        await self.message_queue.put(message)

                        # OPTIMIZED: Add to message buffer - deque automatically handles size limit
                        self.message_buffer.append(message)

                    except json.JSONDecodeError:
                        self.logger.warning(f"Received invalid JSON: {msg.data[:100]}...")
                        self.stats['errors'] += 1

                elif msg.type == aiohttp.WSMsgType.CLOSED:
                    self.logger.warning("WebSocket connection closed by server")
                    break

                elif msg.type == aiohttp.WSMsgType.ERROR:
                    self.logger.error(f"WebSocket error: {msg.data}")
                    self.stats['errors'] += 1
                    break

                elif msg.type == aiohttp.WSMsgType.CLOSING:
                    self.logger.warning("WebSocket closing")
                    break

                elif msg.type == aiohttp.WSMsgType.CLOSE:
                    self.logger.warning("WebSocket closed")
                    break

            except asyncio.TimeoutError:
                self.logger.warning("WebSocket receive timeout")
                # Check if we've missed too many heartbeats
                if time.time() - self.last_message_received > self.heartbeat_interval * 3:
                    self.logger.error("WebSocket connection seems dead, reconnecting...")
                    break

            except asyncio.CancelledError:
                self.logger.info("WebSocket listener cancelled")
                break

            except Exception as e:
                self.logger.error(f"Error in WebSocket listener: {e}")
                self.stats['errors'] += 1
                break

        # Connection is closed or broken
        self.connected = False
        if self.auto_reconnect and not self.reconnect_task:
            # SURGICAL FIX: Prevent multiple reconnection tasks
            self.reconnect_task = asyncio.create_task(self._handle_reconnect())

    async def _process_message_queue(self):
        """Process messages from the queue"""
        self.logger.info("Starting WebSocket message processor")

        while True:
            try:
                # Get message from queue
                message = await self.message_queue.get()

                # Process message
                await self._handle_message(message)

                # Mark as done
                self.message_queue.task_done()

            except asyncio.CancelledError:
                self.logger.info("WebSocket message processor cancelled")
                break

            except Exception as e:
                self.logger.error(f"Error processing WebSocket message: {e}")
                self.stats['errors'] += 1

        self.logger.info("WebSocket message processor stopped")

    async def _handle_message(self, message: WebSocketMessage):
        """Handle incoming WebSocket message with type-based routing"""
        try:
            data = message.data

            # Check if this is a DexScreener message
            if message.source and 'dexscreener.com' in message.source:
                # DexScreener messages have a different structure
                # They contain price, marketCap, liquidity, volume data directly
                self.logger.debug(f"Received DexScreener data: {data}")

                # Create a standardized message format for our handlers
                standardized_data = {
                    'type': 'price_update',
                    'token': self._extract_token_from_url(),
                    'price': data.get('priceUsd'),
                    'marketCap': data.get('marketCap'),
                    'liquidity': data.get('liquidity', {}).get('usd'),
                    'volume': data.get('volume', {}).get('h1')
                }

                # Call handlers for price_update
                handlers = self.message_handlers.get('price_update', [])
                handlers.extend(self.message_handlers.get('*', []))  # Add wildcard handlers

                for handler in handlers:
                    try:
                        await handler(standardized_data)
                    except Exception as e:
                        self.logger.error(f"Error in DexScreener message handler: {e}")

            # Handle other types of WebSocket messages if needed in the future
            else:
                # Standard WebSocket message with 'type' field
                message_type = data.get('type', 'unknown')

                # Call handlers for this message type
                handlers = self.message_handlers.get(message_type, [])
                handlers.extend(self.message_handlers.get('*', []))  # Add wildcard handlers

                for handler in handlers:
                    try:
                        await handler(data)
                    except Exception as e:
                        self.logger.error(f"Error in message handler for {message_type}: {e}")

            # Mark as processed
            message.processed = True

        except Exception as e:
            self.logger.error(f"Error handling WebSocket message: {e}")
            self.stats['errors'] += 1

    def _extract_token_from_url(self) -> str:
        """Extract token address from the WebSocket URL"""
        if not self.ws or not hasattr(self.ws, 'url'):
            return ""

        # URL format: wss://io.dexscreener.com/dex/screener/pairs/solana/TOKEN_ADDRESS
        url_parts = self.ws.url.path.split('/')
        if len(url_parts) > 0:
            return url_parts[-1]  # Last part should be the token address

        return ""

    async def is_supported_connection(self) -> bool:
        """Check if the current WebSocket connection is to a supported service"""
        if not self.ws or not hasattr(self.ws, 'url') or not self.ws.url:
            return False

        # Currently only DexScreener is supported (via HTTP polling, not WebSocket)
        return False

    async def _handle_reconnect(self):
        """Handle WebSocket reconnection with exponential backoff"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            self.logger.error("Max reconnection attempts reached")
            return False

        self.connected = False
        self.reconnect_attempts += 1
        self.stats['reconnects'] += 1

        # Exponential backoff with jitter
        delay = min(60, self.reconnect_delay * (2 ** (self.reconnect_attempts - 1)))
        jitter = delay * 0.2 * (asyncio.get_event_loop().time() % 1)  # 20% jitter
        delay = delay + jitter

        self.logger.info(f"Attempting to reconnect in {delay:.1f} seconds (attempt {self.reconnect_attempts}/{self.max_reconnect_attempts})...")
        await asyncio.sleep(delay)

        # Try to connect
        success = await self.connect()
        if not success and self.auto_reconnect:
            # Will trigger another reconnect attempt
            pass

        return success

    async def _heartbeat_loop(self):
        """Send periodic heartbeats to keep connection alive and monitor connection health"""
        self.logger.info("Starting WebSocket heartbeat loop")

        while self.connected:
            try:
                # Check if it's time for a heartbeat
                now = time.time()
                if now - self.last_heartbeat >= self.heartbeat_interval:
                    # Send heartbeat
                    if self.ws and not self.ws.closed:
                        await self.ws.ping()
                        self.last_heartbeat = now
                        self.logger.debug("Sent WebSocket heartbeat ping")

                        # Check if we've received any messages recently
                        time_since_last_message = now - self.last_message_received
                        if time_since_last_message > self.heartbeat_interval * 3:
                            self.logger.warning(f"No messages received in {time_since_last_message:.1f} seconds, connection may be stale")

                            # If no messages for too long, force reconnection
                            if time_since_last_message > self.heartbeat_interval * 5:
                                self.logger.error("Connection appears dead, forcing reconnection")
                                self.connected = False
                                asyncio.create_task(self._handle_reconnect())
                                break

                # Add any additional heartbeat logic here if needed

                # Sleep until next heartbeat check
                await asyncio.sleep(1)

            except asyncio.CancelledError:
                self.logger.info("WebSocket heartbeat loop cancelled")
                break

            except Exception as e:
                self.logger.error(f"Error in WebSocket heartbeat: {e}")
                await asyncio.sleep(5)  # Wait before retry

        self.logger.info("WebSocket heartbeat loop stopped")

    async def _resubscribe(self):
        """Resubscribe to all active subscriptions after reconnect"""
        if not self.subscriptions:
            return

        self.logger.info(f"Resubscribing to {len(self.subscriptions)} channels")

        for sub_info in self.subscriptions:
            channel, params = sub_info
            try:
                await self.subscribe(channel, params)
            except Exception as e:
                self.logger.error(f"Error resubscribing to {channel}: {e}")

    def add_message_handler(self, message_type: str, handler: Callable):
        """Add a message handler for a specific message type"""
        if message_type not in self.message_handlers:
            self.message_handlers[message_type] = []

        if handler not in self.message_handlers[message_type]:
            self.message_handlers[message_type].append(handler)
            self.logger.debug(f"Added handler for message type: {message_type}")

    def remove_message_handler(self, message_type: str, handler: Callable):
        """Remove a message handler"""
        if message_type in self.message_handlers and handler in self.message_handlers[message_type]:
            self.message_handlers[message_type].remove(handler)
            self.logger.debug(f"Removed handler for message type: {message_type}")

    async def send_message(self, message: Dict[str, Any]) -> bool:
        """Send a message through the WebSocket with retry"""
        if not self.connected or not self.ws or self.ws.closed:
            self.logger.warning("Cannot send message: WebSocket not connected")
            if self.auto_reconnect:
                await self.connect()
                if not self.connected:
                    return False
            else:
                return False

        try:
            await self.ws.send_json(message)
            self.stats['messages_sent'] += 1
            return True

        except Exception as e:
            self.logger.error(f"Error sending WebSocket message: {e}")
            self.stats['errors'] += 1
            self.stats['last_error'] = f"{datetime.now().isoformat()}: Send failed: {str(e)}"

            # Connection might be broken, trigger reconnect
            if self.auto_reconnect:
                self.connected = False
                asyncio.create_task(self._handle_reconnect())

            return False

    async def subscribe(self, channel: str, params: Dict[str, Any] = None) -> bool:
        """Subscribe to a WebSocket channel with tracking"""
        message = {
            'type': 'subscribe',
            'channel': channel,
            'params': params or {}
        }

        success = await self.send_message(message)
        if success:
            # Track subscription for reconnects
            self.subscriptions.add((channel, params))
            self.logger.info(f"Subscribed to channel: {channel}")

        return success

    async def unsubscribe(self, channel: str, params: Dict[str, Any] = None) -> bool:
        """Unsubscribe from a WebSocket channel"""
        message = {
            'type': 'unsubscribe',
            'channel': channel
        }

        success = await self.send_message(message)
        if success:
            # Remove from tracked subscriptions
            if params is not None:
                self.subscriptions.discard((channel, params))
            else:
                # Remove all subscriptions for this channel
                self.subscriptions = {s for s in self.subscriptions if s[0] != channel}

            self.logger.info(f"Unsubscribed from channel: {channel}")

        return success

    async def poll_dexscreener_token(self, token_address: str) -> Dict[str, Any]:
        """
        Poll DexScreener API for token data using HTTP instead of WebSocket

        Args:
            token_address: The token address to poll

        Returns:
            Dict containing token data or error information
        """
        if not self.session or self.session.closed:
            try:
                self.session = aiohttp.ClientSession()
            except Exception as e:
                self.logger.error(f"Failed to create session for DexScreener polling: {e}")
                return {"error": f"Session creation failed: {str(e)}"}

        # Use the correct DexScreener API endpoint for tokens from config
        dexscreener_endpoint = self.config.get('api_endpoints', 'dexscreener', default="https://api.dexscreener.com/token-pairs/v1/{chainId}/{tokenAddress}")

        # CRITICAL FIX: Check if token address ends with "pump" and ensure we use the full address
        if token_address.lower().endswith("pump"):
            self.logger.warning(f"PUMP TOKEN DETECTED in websocket_manager.py: {token_address}")
            self.logger.warning(f"IMPORTANT: Using FULL address with 'pump' suffix: {token_address}")

        # Replace the placeholders in the URL - NEVER modify the token address
        # First replace chainId, then tokenAddress to avoid issues with tokens containing "{chainId}"
        url = dexscreener_endpoint.replace("{chainId}", "solana").replace("{tokenAddress}", token_address)

        # Verify the final URL contains the exact token address
        if token_address not in url:
            self.logger.error(f"ERROR: Final URL does not contain the exact token address!")
            self.logger.error(f"Token address: {token_address}")
            self.logger.error(f"URL: {url}")
            # Try to fix the URL by directly appending the token address
            corrected_url = f"https://api.dexscreener.com/token-pairs/v1/solana/{token_address}"
            self.logger.warning(f"CORRECTED URL: {corrected_url}")
            url = corrected_url

        # Log the final URL with high visibility
        self.logger.warning(f"FINAL DexScreener API URL in websocket_manager.py: {url}")

        try:
            async with self.session.get(url, timeout=10) as response:
                if response.status == 200:
                    data = await response.json()

                    if not data or 'pairs' not in data or not data['pairs']:
                        return {"error": "No pairs found"}

                    # Find the most liquid pair
                    if len(data['pairs']) > 1:
                        pair = max(data['pairs'], key=lambda p: float(p.get('liquidity', {}).get('usd', 0) or 0))
                    else:
                        pair = data['pairs'][0]

                    # Create a standardized message format for our handlers
                    standardized_data = {
                        'type': 'price_update',
                        'token': token_address,
                        'price': pair.get('priceUsd'),
                        'marketCap': pair.get('fdv'),  # Use FDV as market cap
                        'liquidity': pair.get('liquidity', {}).get('usd'),
                        'volume': pair.get('volume', {}).get('h1'),
                        'volume24h': pair.get('volume', {}).get('h24'),
                        'pairCreatedAt': pair.get('pairCreatedAt'),
                        'source': 'dexscreener'
                    }

                    # Create a WebSocketMessage and process it
                    message = WebSocketMessage(standardized_data, source="dexscreener.com")
                    await self.message_queue.put(message)

                    return standardized_data
                else:
                    self.logger.warning(f"DexScreener API returned status {response.status} for {token_address}")
                    return {"error": f"HTTP {response.status}"}

        except asyncio.TimeoutError:
            self.logger.warning(f"DexScreener API request timed out for {token_address}")
            return {"error": "Request timed out"}
        except Exception as e:
            self.logger.error(f"Error polling DexScreener API for {token_address}: {e}")
            return {"error": str(e)}

    async def start_dexscreener_polling(self, token_address: str, interval_seconds: int = 30):
        """
        Start polling DexScreener API for token data at regular intervals

        Args:
            token_address: The token address to poll
            interval_seconds: How often to poll the API (default: 30 seconds)
        """
        self.logger.info(f"Starting DexScreener polling for {token_address} every {interval_seconds} seconds")

        # Store the polling task so we can cancel it later
        polling_task_name = f"dexscreener_polling_{token_address}"

        # Cancel any existing polling task for this token
        existing_task = getattr(self, polling_task_name, None)
        if existing_task and not existing_task.done():
            existing_task.cancel()
            try:
                await existing_task
            except asyncio.CancelledError:
                pass

        # Define the polling coroutine
        async def _polling_loop():
            while True:
                try:
                    result = await self.poll_dexscreener_token(token_address)
                    if "error" in result:
                        self.logger.warning(f"DexScreener polling error: {result['error']}")
                    await asyncio.sleep(interval_seconds)
                except asyncio.CancelledError:
                    self.logger.info(f"DexScreener polling for {token_address} cancelled")
                    break
                except Exception as e:
                    self.logger.error(f"Error in DexScreener polling loop for {token_address}: {e}")
                    await asyncio.sleep(interval_seconds)

        # Start the polling task
        polling_task = asyncio.create_task(_polling_loop())
        setattr(self, polling_task_name, polling_task)

        return True

    async def stop_dexscreener_polling(self, token_address: str):
        """Stop polling DexScreener API for a specific token"""
        polling_task_name = f"dexscreener_polling_{token_address}"
        polling_task = getattr(self, polling_task_name, None)

        if polling_task and not polling_task.done():
            polling_task.cancel()
            try:
                await polling_task
            except asyncio.CancelledError:
                pass
            self.logger.info(f"Stopped DexScreener polling for {token_address}")
            return True

        return False

    # Birdeye API integration removed

    async def check_status(self) -> Dict[str, Any]:
        """Return detailed connection status and statistics"""
        status = "Connected" if self.connected and self.ws and not self.ws.closed else "Disconnected"

        if status == "Disconnected" and self.reconnect_attempts > 0 and self.reconnect_attempts < self.max_reconnect_attempts:
            status = "Reconnecting"

        # Calculate uptime if connected
        uptime_seconds = 0
        if self.stats['connected_since']:
            try:
                connected_time = datetime.fromisoformat(self.stats['connected_since'])
                uptime_seconds = (datetime.now() - connected_time).total_seconds()
            except (ValueError, TypeError):
                pass

        # Add queue info
        queue_size = self.message_queue.qsize()

        return {
            'status': status,
            'url': self.ws.url.human_repr() if self.ws and hasattr(self.ws, 'url') else None,
            'reconnect_attempts': self.reconnect_attempts,
            'uptime_seconds': uptime_seconds,
            'messages_received': self.stats['messages_received'],
            'messages_sent': self.stats['messages_sent'],
            'errors': self.stats['errors'],
            'reconnects': self.stats['reconnects'],
            'last_error': self.stats['last_error'],
            'queue_size': queue_size,
            'subscriptions': len(self.subscriptions),
            'last_message_age': time.time() - self.last_message_received if self.last_message_received else None
        }

    def get_recent_messages(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent messages from the buffer"""
        return [
            {
                'data': msg.data,
                'source': msg.source,
                'timestamp': msg.timestamp.isoformat(),
                'processed': msg.processed
            }
            for msg in self.message_buffer[-limit:]
        ]

    # --- DexScreener WebSocket API Methods ---

    async def is_dexscreener_connection(self) -> bool:
        """Check if the current WebSocket connection is to DexScreener"""
        if not self.ws or not hasattr(self.ws, 'url'):
            return False
        return 'dexscreener.com' in self.ws.url.host

    async def subscribe_dexscreener_token(self, token_address: str, interval_seconds: int = 30) -> bool:
        """
        Subscribe to updates for a specific token on DexScreener using HTTP polling

        Args:
            token_address: The token address to monitor
            interval_seconds: How often to poll the API (default: 30 seconds)

        Returns:
            bool: True if subscription was successful, False otherwise
        """
        self.logger.info(f"Setting up DexScreener HTTP polling for token {token_address}")

        # Start polling for this token
        return await self.start_dexscreener_polling(token_address, interval_seconds)

    # PumpPortal WebSocket integration removed











    # --- Utility Methods ---