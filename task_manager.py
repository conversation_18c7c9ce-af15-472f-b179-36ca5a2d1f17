"""
OPTIMIZED: Centralized Task Management Service
Provides unified async task lifecycle management with proper cleanup
"""

import asyncio
import logging
import time
from typing import Dict, Optional, Set, Callable, Any
from datetime import datetime

logger = logging.getLogger(__name__)


class TaskManager:
    """
    Centralized async task manager for proper lifecycle management.
    Eliminates orphaned tasks and provides monitoring capabilities.
    """
    
    def __init__(self, name: str = "TaskManager"):
        self.name = name
        self.tasks: Dict[str, asyncio.Task] = {}
        self.task_metadata: Dict[str, Dict[str, Any]] = {}
        self._shutdown_event = asyncio.Event()
        self._cleanup_task: Optional[asyncio.Task] = None
        
        # Start cleanup task
        self._start_cleanup_task()
    
    def _start_cleanup_task(self):
        """Start the background cleanup task"""
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._cleanup_completed_tasks())
    
    async def _cleanup_completed_tasks(self):
        """Background task to clean up completed tasks"""
        while not self._shutdown_event.is_set():
            try:
                completed_tasks = []
                
                for task_name, task in self.tasks.items():
                    if task.done():
                        completed_tasks.append(task_name)
                        
                        # Log task completion
                        metadata = self.task_metadata.get(task_name, {})
                        start_time = metadata.get('start_time', time.time())
                        duration = time.time() - start_time
                        
                        if task.cancelled():
                            logger.debug(f"Task '{task_name}' was cancelled after {duration:.2f}s")
                        elif task.exception():
                            logger.error(f"Task '{task_name}' failed after {duration:.2f}s: {task.exception()}")
                        else:
                            logger.debug(f"Task '{task_name}' completed successfully in {duration:.2f}s")
                
                # Remove completed tasks
                for task_name in completed_tasks:
                    self.tasks.pop(task_name, None)
                    self.task_metadata.pop(task_name, None)
                
                # Wait before next cleanup cycle
                await asyncio.sleep(30)  # Cleanup every 30 seconds
                
            except Exception as e:
                logger.error(f"Error in task cleanup: {e}")
                await asyncio.sleep(5)  # Short wait on error
    
    def create_task(self, name: str, coro, replace_existing: bool = True) -> asyncio.Task:
        """
        Create and track a new async task
        
        Args:
            name: Unique task name
            coro: Coroutine to execute
            replace_existing: Whether to cancel existing task with same name
            
        Returns:
            Created asyncio.Task
        """
        # Handle existing task
        if name in self.tasks:
            if replace_existing:
                logger.debug(f"Cancelling existing task: {name}")
                self.cancel_task(name)
            else:
                raise ValueError(f"Task '{name}' already exists")
        
        # Create new task
        task = asyncio.create_task(coro)
        self.tasks[name] = task
        self.task_metadata[name] = {
            'start_time': time.time(),
            'created_at': datetime.now().isoformat()
        }
        
        logger.debug(f"Created task: {name}")
        return task
    
    def get_task(self, name: str) -> Optional[asyncio.Task]:
        """Get task by name"""
        return self.tasks.get(name)
    
    def cancel_task(self, name: str) -> bool:
        """
        Cancel a specific task
        
        Args:
            name: Task name to cancel
            
        Returns:
            True if task was cancelled, False if not found
        """
        task = self.tasks.get(name)
        if task and not task.done():
            task.cancel()
            logger.debug(f"Cancelled task: {name}")
            return True
        return False
    
    def cancel_all_tasks(self) -> int:
        """
        Cancel all running tasks
        
        Returns:
            Number of tasks cancelled
        """
        cancelled_count = 0
        for name, task in list(self.tasks.items()):
            if not task.done():
                task.cancel()
                cancelled_count += 1
        
        logger.info(f"Cancelled {cancelled_count} tasks")
        return cancelled_count
    
    def get_task_status(self) -> Dict[str, Dict[str, Any]]:
        """
        Get status of all tasks
        
        Returns:
            Dictionary with task status information
        """
        status = {}
        current_time = time.time()
        
        for name, task in self.tasks.items():
            metadata = self.task_metadata.get(name, {})
            start_time = metadata.get('start_time', current_time)
            
            status[name] = {
                'done': task.done(),
                'cancelled': task.cancelled(),
                'running_time': current_time - start_time,
                'created_at': metadata.get('created_at', 'unknown'),
                'exception': str(task.exception()) if task.done() and task.exception() else None
            }
        
        return status
    
    def get_running_tasks(self) -> Set[str]:
        """Get names of all running tasks"""
        return {name for name, task in self.tasks.items() if not task.done()}
    
    def get_task_count(self) -> Dict[str, int]:
        """Get task count statistics"""
        total = len(self.tasks)
        running = len([t for t in self.tasks.values() if not t.done()])
        completed = total - running
        
        return {
            'total': total,
            'running': running,
            'completed': completed
        }
    
    async def wait_for_task(self, name: str, timeout: Optional[float] = None) -> Any:
        """
        Wait for a specific task to complete
        
        Args:
            name: Task name
            timeout: Optional timeout in seconds
            
        Returns:
            Task result
            
        Raises:
            asyncio.TimeoutError: If timeout exceeded
            KeyError: If task not found
        """
        task = self.tasks.get(name)
        if not task:
            raise KeyError(f"Task '{name}' not found")
        
        if timeout:
            return await asyncio.wait_for(task, timeout=timeout)
        else:
            return await task
    
    async def shutdown(self, timeout: float = 30.0):
        """
        Graceful shutdown of task manager
        
        Args:
            timeout: Maximum time to wait for tasks to complete
        """
        logger.info(f"Shutting down {self.name} with {len(self.tasks)} tasks")
        
        # Signal shutdown
        self._shutdown_event.set()
        
        # Cancel cleanup task
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
        
        # Cancel all tasks
        self.cancel_all_tasks()
        
        # Wait for tasks to complete or timeout
        if self.tasks:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*self.tasks.values(), return_exceptions=True),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                logger.warning(f"Some tasks did not complete within {timeout}s timeout")
        
        # Clear all tasks
        self.tasks.clear()
        self.task_metadata.clear()
        
        logger.info(f"{self.name} shutdown complete")


# OPTIMIZED: Global task manager instances for different components
main_task_manager = TaskManager("MainTaskManager")
websocket_task_manager = TaskManager("WebSocketTaskManager")
trading_task_manager = TaskManager("TradingTaskManager")


def get_main_task_manager() -> TaskManager:
    """Get the main task manager instance"""
    return main_task_manager


def get_websocket_task_manager() -> TaskManager:
    """Get the websocket task manager instance"""
    return websocket_task_manager


def get_trading_task_manager() -> TaskManager:
    """Get the trading task manager instance"""
    return trading_task_manager


async def shutdown_all_task_managers(timeout: float = 30.0):
    """Shutdown all task managers gracefully"""
    await asyncio.gather(
        main_task_manager.shutdown(timeout),
        websocket_task_manager.shutdown(timeout),
        trading_task_manager.shutdown(timeout),
        return_exceptions=True
    )
