#!/usr/bin/env python3
"""
Trading Performance Analyzer
Analyzes actual trading performance from trade logs
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Any

logger = logging.getLogger(__name__)

class TradingPerformanceAnalyzer:
    """Analyze trading performance from CSV logs"""
    
    def __init__(self, trade_log_path: str = "simulation_logs/trade_log.csv"):
        self.trade_log_path = trade_log_path
        self.trades_df = None
        self.performance_metrics = {}
        
    def load_trade_data(self) -> bool:
        """Load and parse trade data from CSV"""
        try:
            self.trades_df = pd.read_csv(self.trade_log_path)
            logger.info(f"Loaded {len(self.trades_df)} trade records")
            return True
        except Exception as e:
            logger.error(f"Error loading trade data: {e}")
            return False
    
    def analyze_trading_pairs(self) -> Dict[str, Any]:
        """Analyze buy/sell pairs to calculate actual PnL"""
        if self.trades_df is None:
            return {}
        
        # Group trades by token address
        token_groups = self.trades_df.groupby('token_address')
        
        completed_trades = []
        open_positions = []
        
        for token_address, group in token_groups:
            trades = group.sort_values('timestamp')
            
            # Track position for this token
            position_size = 0.0
            total_cost = 0.0
            entry_time = None
            
            for _, trade in trades.iterrows():
                if trade['trade_type'] == 'buy':
                    # Add to position
                    position_size += float(trade['amount'])
                    total_cost += float(trade['amount']) * float(trade['price'])
                    if entry_time is None:
                        entry_time = trade['timestamp']
                        
                elif trade['trade_type'] == 'sell_attempt' and position_size > 0:
                    # Calculate PnL for this sell
                    avg_entry_price = total_cost / position_size if position_size > 0 else 0
                    sell_amount = float(trade['amount'])
                    sell_price = float(trade['price']) if float(trade['price']) > 0 else avg_entry_price
                    
                    # Calculate actual PnL
                    cost_basis = sell_amount * avg_entry_price
                    sell_value = sell_amount * sell_price
                    pnl_sol = sell_value - cost_basis
                    pnl_percent = (pnl_sol / cost_basis * 100) if cost_basis > 0 else 0
                    
                    # Duration calculation
                    try:
                        entry_dt = pd.to_datetime(entry_time)
                        exit_dt = pd.to_datetime(trade['timestamp'])
                        duration = (exit_dt - entry_dt).total_seconds()
                    except:
                        duration = 0
                    
                    completed_trades.append({
                        'token_address': token_address,
                        'entry_time': entry_time,
                        'exit_time': trade['timestamp'],
                        'duration_seconds': duration,
                        'entry_price': avg_entry_price,
                        'exit_price': sell_price,
                        'amount_sol': sell_amount * avg_entry_price,  # SOL invested
                        'pnl_sol': pnl_sol,
                        'pnl_percent': pnl_percent,
                        'exit_reason': 'sell_attempt'
                    })
                    
                    # Reduce position
                    if sell_amount >= position_size:
                        # Position fully closed
                        position_size = 0
                        total_cost = 0
                        entry_time = None
                    else:
                        # Partial sell
                        position_size -= sell_amount
                        total_cost -= cost_basis
            
            # Track remaining open positions
            if position_size > 0:
                avg_entry_price = total_cost / position_size
                open_positions.append({
                    'token_address': token_address,
                    'entry_time': entry_time,
                    'position_size': position_size,
                    'avg_entry_price': avg_entry_price,
                    'cost_basis': total_cost
                })
        
        return {
            'completed_trades': completed_trades,
            'open_positions': open_positions
        }
    
    def calculate_performance_metrics(self, trade_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate comprehensive performance metrics"""
        completed_trades = trade_analysis.get('completed_trades', [])
        open_positions = trade_analysis.get('open_positions', [])
        
        if not completed_trades:
            return {'error': 'No completed trades found'}
        
        # Convert to DataFrame for easier analysis
        trades_df = pd.DataFrame(completed_trades)
        
        # Basic metrics
        total_trades = len(completed_trades)
        winning_trades = len(trades_df[trades_df['pnl_percent'] > 0])
        losing_trades = len(trades_df[trades_df['pnl_percent'] < 0])
        breakeven_trades = len(trades_df[trades_df['pnl_percent'] == 0])
        
        # Win rate
        win_rate = (winning_trades / total_trades * 100) if total_trades > 0 else 0
        
        # PnL metrics
        total_pnl_sol = trades_df['pnl_sol'].sum()
        avg_pnl_percent = trades_df['pnl_percent'].mean()
        median_pnl_percent = trades_df['pnl_percent'].median()
        
        # Win/Loss analysis
        winning_trades_df = trades_df[trades_df['pnl_percent'] > 0]
        losing_trades_df = trades_df[trades_df['pnl_percent'] < 0]
        
        avg_win_percent = winning_trades_df['pnl_percent'].mean() if len(winning_trades_df) > 0 else 0
        avg_loss_percent = losing_trades_df['pnl_percent'].mean() if len(losing_trades_df) > 0 else 0
        
        # Duration analysis
        avg_duration_seconds = trades_df['duration_seconds'].mean()
        avg_duration_minutes = avg_duration_seconds / 60
        
        # Risk metrics
        max_win_percent = trades_df['pnl_percent'].max()
        max_loss_percent = trades_df['pnl_percent'].min()
        
        # Recent performance (last 24 hours)
        try:
            recent_cutoff = datetime.now() - timedelta(hours=24)
            trades_df['exit_time_dt'] = pd.to_datetime(trades_df['exit_time'])
            recent_trades = trades_df[trades_df['exit_time_dt'] > recent_cutoff]
            recent_win_rate = (len(recent_trades[recent_trades['pnl_percent'] > 0]) / len(recent_trades) * 100) if len(recent_trades) > 0 else 0
            recent_pnl = recent_trades['pnl_sol'].sum() if len(recent_trades) > 0 else 0
        except:
            recent_win_rate = 0
            recent_pnl = 0
            recent_trades = pd.DataFrame()
        
        return {
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'breakeven_trades': breakeven_trades,
            'win_rate_percent': win_rate,
            'total_pnl_sol': total_pnl_sol,
            'avg_pnl_percent': avg_pnl_percent,
            'median_pnl_percent': median_pnl_percent,
            'avg_win_percent': avg_win_percent,
            'avg_loss_percent': avg_loss_percent,
            'avg_duration_minutes': avg_duration_minutes,
            'max_win_percent': max_win_percent,
            'max_loss_percent': max_loss_percent,
            'open_positions_count': len(open_positions),
            'recent_24h_trades': len(recent_trades),
            'recent_24h_win_rate': recent_win_rate,
            'recent_24h_pnl': recent_pnl
        }
    
    def generate_performance_report(self) -> str:
        """Generate a comprehensive performance report"""
        if not self.load_trade_data():
            return "❌ Failed to load trade data"
        
        trade_analysis = self.analyze_trading_pairs()
        metrics = self.calculate_performance_metrics(trade_analysis)
        
        if 'error' in metrics:
            return f"❌ {metrics['error']}"
        
        # Format report
        lines = []
        lines.append("📊 TRADING PERFORMANCE ANALYSIS")
        lines.append("=" * 50)
        lines.append("")
        
        # Overall Performance
        lines.append("🎯 OVERALL PERFORMANCE:")
        lines.append(f"  Total Trades: {metrics['total_trades']}")
        lines.append(f"  Win Rate: {metrics['win_rate_percent']:.1f}%")
        lines.append(f"  Total PnL: {metrics['total_pnl_sol']:.4f} SOL")
        lines.append(f"  Average PnL: {metrics['avg_pnl_percent']:.2f}%")
        lines.append(f"  Median PnL: {metrics['median_pnl_percent']:.2f}%")
        lines.append("")
        
        # Win/Loss Breakdown
        lines.append("📈 WIN/LOSS BREAKDOWN:")
        lines.append(f"  Winning Trades: {metrics['winning_trades']} ({metrics['winning_trades']/metrics['total_trades']*100:.1f}%)")
        lines.append(f"  Losing Trades: {metrics['losing_trades']} ({metrics['losing_trades']/metrics['total_trades']*100:.1f}%)")
        lines.append(f"  Breakeven Trades: {metrics['breakeven_trades']} ({metrics['breakeven_trades']/metrics['total_trades']*100:.1f}%)")
        lines.append(f"  Average Win: {metrics['avg_win_percent']:.2f}%")
        lines.append(f"  Average Loss: {metrics['avg_loss_percent']:.2f}%")
        lines.append("")
        
        # Risk Metrics
        lines.append("⚠️ RISK METRICS:")
        lines.append(f"  Best Trade: {metrics['max_win_percent']:.2f}%")
        lines.append(f"  Worst Trade: {metrics['max_loss_percent']:.2f}%")
        lines.append(f"  Average Hold Time: {metrics['avg_duration_minutes']:.1f} minutes")
        lines.append("")
        
        # Recent Performance
        lines.append("🕐 RECENT PERFORMANCE (24H):")
        lines.append(f"  Recent Trades: {metrics['recent_24h_trades']}")
        lines.append(f"  Recent Win Rate: {metrics['recent_24h_win_rate']:.1f}%")
        lines.append(f"  Recent PnL: {metrics['recent_24h_pnl']:.4f} SOL")
        lines.append("")
        
        # Open Positions
        lines.append("💼 CURRENT STATUS:")
        lines.append(f"  Open Positions: {metrics['open_positions_count']}")
        lines.append("")
        
        # Performance Assessment
        lines.append("🎯 PERFORMANCE ASSESSMENT:")
        if metrics['win_rate_percent'] >= 60:
            lines.append("  ✅ Excellent win rate")
        elif metrics['win_rate_percent'] >= 40:
            lines.append("  ⚠️ Moderate win rate")
        else:
            lines.append("  ❌ Low win rate - strategy needs review")
        
        if metrics['avg_pnl_percent'] > 5:
            lines.append("  ✅ Strong average returns")
        elif metrics['avg_pnl_percent'] > 0:
            lines.append("  ⚠️ Modest positive returns")
        else:
            lines.append("  ❌ Negative average returns")
        
        if metrics['avg_duration_minutes'] < 30:
            lines.append("  ✅ Fast execution strategy")
        elif metrics['avg_duration_minutes'] < 120:
            lines.append("  ⚠️ Medium-term holds")
        else:
            lines.append("  ❌ Long hold times - may indicate exit issues")
        
        lines.append("")
        lines.append("=" * 50)
        
        return "\n".join(lines)

def main():
    """Run performance analysis"""
    analyzer = TradingPerformanceAnalyzer()
    report = analyzer.generate_performance_report()
    print(report)

if __name__ == "__main__":
    main()
