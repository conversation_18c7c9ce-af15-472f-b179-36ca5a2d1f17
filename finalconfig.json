{"_comment_api_rate_limits": "DexScreener: 295 RPM (4.9 RPS), Helius RPC: 600 RPM (10 RPS) with smart retry logic", "api_rate_limits": {"dexscreener_rpm": 295, "dexscreener_rps": 4.9, "helius_rpc_rpm": 600, "helius_retry_delay_ms": 300, "helius_max_retries": 2, "coingecko_rps": 10.0, "request_spacing_buffer_ms": 50}, "api_endpoints": {"dexscreener": "https://api.dexscreener.com/token-pairs/v1/{chainId}/{tokenAddress}", "helius_rpc": "https://mainnet.helius-rpc.com/?api-key={api_key}", "coingecko_sol_price": "https://api.coingecko.com/api/v3/simple/price?ids=solana&vs_currencies=usd", "solana_rpc": "https://api.mainnet-beta.solana.com"}, "pump_fun_settings": {"graduation_target": 70000, "virtual_liquidity_multiplier": 1.95, "default_sol_price": 200.0, "request_timeout": 10, "request_headers": {"Accept": "*/*"}}, "telegram_settings": {"target_channel_id": "0", "bot_session_name": "trading_sim_session", "target_channels": [-1002187518673, -1002017173747, -1002017857449, -1001938040677, -1002177594166, -1002093384030, -1002428819353], "bot_info_channel_id": -1002525039395, "direct_signal_processing": true}, "wallet_settings": {"default_wallet_name": "MAIN", "auto_select_wallet": true, "wallet_rotation": false, "max_trades_per_wallet": 100}, "trading_settings": {"signal_listener_poll_seconds": 0.1, "ws_reconnect_delay_seconds": 10, "adaptive_position_monitoring": true, "position_monitoring_intervals": {"critical_risk_ms": 600, "high_risk_ms": 700, "medium_risk_ms": 800, "low_risk_ms": 1500}, "risk_detection_thresholds": {"critical_pnl_percent": -12.0, "critical_profit_percent": 40.0, "critical_volatility": 0.08, "critical_drawdown_percent": -8.0, "critical_age_minutes": 1.5, "high_pnl_loss_percent": -8.0, "high_pnl_profit_percent": 25.0, "high_volatility": 0.04, "high_drawdown_percent": -4.0, "high_age_minutes": 3.0, "high_profit_drop_threshold": 1.15, "high_profit_drop_ratio": 0.92, "medium_pnl_loss_percent": -4.0, "medium_pnl_profit_percent": 12.0, "medium_volatility": 0.015, "medium_age_minutes": 10.0, "medium_profit_drop_threshold": 1.08, "medium_profit_drop_ratio": 0.96}, "profit_taking": {"enabled": false, "levels": [{"percent": 5.0, "sell_fraction": 0.6}, {"percent": 10.0, "sell_fraction": 0.5}, {"percent": 15.0, "sell_fraction": 1.0}]}, "indicator_price_points": 30, "sol_trade_amount": 0.4, "total_sol_capital": 1.5, "min_wallet_balance": 0.5, "trade_cooldown_seconds": 5, "max_concurrent_trades": 3, "max_hold_time_minutes": 3, "enforce_max_hold_time": true, "confidence_threshold_for_tg_buy": 0.4, "liquidity_thresholds": {"min_usd": 8000.0, "safe_entry_usd": 8000.0, "absolute_minimum_usd": 3000.0}, "_comment_max_age": "Max age disabled - volume/liquidity/mc/txn activity matters more than age", "fdv_surge_threshold_percent": 150, "prevent_buy_if_x_multiplier": true, "rug_protection": {"enabled": true, "min_liquidity_usd": 8000.0, "min_volume_5m_usd": 5000.0, "min_fdv_usd": 10000, "min_market_cap_usd": 10000, "max_top10_holders_percent": 30.0, "min_token_age_minutes": 0, "check_blacklist": true, "check_mintable": false, "check_dev_wallet_activity": true, "dev_sell_confidence_penalty": 0.3, "honeypot_protection": true, "whale_concentration_protection": true, "instant_rug_protection": true, "realtime_monitoring": {"liquidity_drain_threshold_percent": 30.0, "holder_exodus_threshold_percent": 20.0, "whale_concentration_increase_threshold_percent": 10.0, "enable_liquidity_monitoring": true, "enable_holder_monitoring": true, "enable_whale_monitoring": true}}, "default_strategy_name": "AGGRESSIVE", "strategies": {"DEFAULT": {"take_profit_percent": 20.0, "stop_loss_percent": -25.0, "min_confidence": 0.6, "tp_min_percent": 12.0, "tp_max_percent": 18.0, "use_adaptive_tp_sl": false, "slippage_buffer_percent": 8}, "SAFE": {"take_profit_percent": 15.0, "stop_loss_percent": -20.0, "min_confidence": 0.6, "tp_min_percent": 12.0, "tp_max_percent": 18.0, "use_adaptive_tp_sl": false, "slippage_buffer_percent": 8}, "AGGRESSIVE": {"take_profit_percent": 20.0, "stop_loss_percent": -20.0, "min_confidence": 0.5, "tp_min_percent": 15.0, "tp_max_percent": 20.0, "use_adaptive_tp_sl": true, "take_profit_percent_range": [20, 30], "stop_loss_percent_range": [-21, -20], "slippage_buffer_percent": 23}, "ULTRA_CONSERVATIVE": {"take_profit_percent": 10.0, "stop_loss_percent": -20.0, "min_confidence": 0.7, "tp_min_percent": 8.0, "tp_max_percent": 12.0, "use_adaptive_tp_sl": false, "take_profit_percent_range": [10, 20], "stop_loss_percent_range": [-25, -20], "slippage_buffer_percent": 7}}, "transaction_settings": {"slippage_percent": 10.0, "buy_tip_sol": 0.005, "handling_fee_percent": 1.0, "gas_price_sol": 0.005, "platform_fee_percent": 0.5, "use_adaptive_slippage": true}, "num_analysis_workers": 3, "num_execution_workers": 4, "api_request_timeout": 1.0, "position_monitor_interval_seconds": 1.5, "api_request_spacing_ms": 1500, "requests_per_monitoring_cycle": 3, "sol_price_cache_minutes": 1, "token_analysis_cache_minutes": 2, "_comment_helius_rate_limiting": "Helius RPC rate limiting: 600 RPM (10 RPS) with 100ms spacing and smart retry logic", "helius_rpc_rate_limit": {"min_request_spacing_ms": 100, "retry_delay_ms": 300, "max_retries": 2, "backoff_multiplier": 1.5, "circuit_breaker_threshold": 5, "circuit_breaker_timeout_seconds": 30}}}