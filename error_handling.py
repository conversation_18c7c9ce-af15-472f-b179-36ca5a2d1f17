"""
SURGICAL FIX: Comprehensive error handling utilities for production-ready trading bot
Provides bulletproof API calls, circuit breakers, and global exception handling
"""

import asyncio
import functools
import json
import logging
import time
from typing import Any, Callable, Dict, Optional, Union
import aiohttp

logger = logging.getLogger(__name__)


def robust_api_call(
    max_retries: int = 3,
    timeout: float = 10.0,
    fallback_value: Any = None,
    exponential_backoff: bool = True,
    retry_on_status: list = None
):
    """
    Decorator for bulletproof API calls with comprehensive error handling
    
    Args:
        max_retries: Maximum number of retry attempts
        timeout: Timeout in seconds for each attempt
        fallback_value: Value to return if all retries fail
        exponential_backoff: Use exponential backoff between retries
        retry_on_status: HTTP status codes to retry on (default: [429, 500, 502, 503, 504])
    """
    if retry_on_status is None:
        retry_on_status = [429, 500, 502, 503, 504]
    
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries):
                try:
                    # Wrap in timeout
                    result = await asyncio.wait_for(
                        func(*args, **kwargs),
                        timeout=timeout
                    )
                    return result
                    
                except asyncio.TimeoutError:
                    last_exception = f"Timeout after {timeout}s"
                    logger.warning(f"{func.__name__} timeout (attempt {attempt+1}/{max_retries})")
                    
                except aiohttp.ClientConnectorError as e:
                    last_exception = f"Connection error: {e}"
                    logger.warning(f"{func.__name__} connection error (attempt {attempt+1}/{max_retries}): {e}")
                    
                except aiohttp.ClientResponseError as e:
                    last_exception = f"HTTP {e.status}: {e}"
                    if e.status in retry_on_status:
                        if e.status == 429:  # Rate limited
                            wait_time = (2 ** attempt) if exponential_backoff else 1.0
                            logger.warning(f"Rate limited, waiting {wait_time}s (attempt {attempt+1}/{max_retries})")
                            await asyncio.sleep(wait_time)
                        else:
                            logger.warning(f"HTTP {e.status} error (attempt {attempt+1}/{max_retries}): {e}")
                    else:
                        logger.error(f"Non-retryable HTTP error {e.status}: {e}")
                        break  # Don't retry on client errors like 400, 401, 403, 404
                        
                except json.JSONDecodeError as e:
                    last_exception = f"Invalid JSON response: {e}"
                    logger.error(f"{func.__name__} received invalid JSON (attempt {attempt+1}/{max_retries}): {e}")
                    break  # Don't retry on JSON errors
                    
                except Exception as e:
                    last_exception = f"Unexpected error: {e}"
                    logger.error(f"{func.__name__} unexpected error (attempt {attempt+1}/{max_retries}): {e}")
                    
                # Wait before retry (except on last attempt)
                if attempt < max_retries - 1:
                    wait_time = (0.5 * (2 ** attempt)) if exponential_backoff else 0.5
                    await asyncio.sleep(wait_time)
            
            # All retries failed
            logger.error(f"{func.__name__} failed after {max_retries} attempts: {last_exception}")
            return fallback_value
            
        return wrapper
    return decorator


class CircuitBreaker:
    """
    Circuit breaker pattern implementation to prevent cascade failures
    
    States:
    - CLOSED: Normal operation, requests pass through
    - OPEN: Failure threshold exceeded, requests fail fast
    - HALF_OPEN: Testing if service has recovered
    """
    
    def __init__(self, failure_threshold: int = 5, timeout: int = 60, name: str = "unnamed"):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.name = name
        self.failure_count = 0
        self.last_failure_time = 0
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN
        self.success_count = 0  # For half-open state
        
    async def call(self, func: Callable, *args, **kwargs):
        """Execute function through circuit breaker"""
        current_time = time.time()
        
        if self.state == "OPEN":
            if current_time - self.last_failure_time > self.timeout:
                self.state = "HALF_OPEN"
                self.success_count = 0
                logger.info(f"Circuit breaker {self.name} transitioning to HALF_OPEN")
            else:
                raise Exception(f"Circuit breaker {self.name} is OPEN - failing fast")
        
        try:
            result = await func(*args, **kwargs)
            
            # Success handling
            if self.state == "HALF_OPEN":
                self.success_count += 1
                if self.success_count >= 3:  # Require 3 successes to close
                    self.state = "CLOSED"
                    self.failure_count = 0
                    logger.info(f"Circuit breaker {self.name} transitioning to CLOSED")
            elif self.state == "CLOSED":
                self.failure_count = 0  # Reset failure count on success
                
            return result
            
        except Exception as e:
            # Failure handling
            self.failure_count += 1
            self.last_failure_time = current_time
            
            if self.failure_count >= self.failure_threshold:
                if self.state != "OPEN":
                    self.state = "OPEN"
                    logger.error(f"Circuit breaker {self.name} transitioning to OPEN after {self.failure_count} failures")
            elif self.state == "HALF_OPEN":
                self.state = "OPEN"
                logger.error(f"Circuit breaker {self.name} returning to OPEN after failure in HALF_OPEN")
            
            raise
    
    def get_state(self) -> Dict[str, Any]:
        """Get current circuit breaker state"""
        return {
            'name': self.name,
            'state': self.state,
            'failure_count': self.failure_count,
            'failure_threshold': self.failure_threshold,
            'last_failure_time': self.last_failure_time,
            'timeout': self.timeout
        }


class GlobalExceptionHandler:
    """Global exception handler for unhandled exceptions"""
    
    @staticmethod
    def setup():
        """Setup global exception handler for asyncio loop"""
        def handle_exception(loop, context):
            exception = context.get('exception')
            if exception:
                logger.critical(f"UNHANDLED EXCEPTION: {exception}")
                logger.critical(f"Context: {context}")
                
                # Log stack trace if available
                if hasattr(exception, '__traceback__'):
                    import traceback
                    logger.critical(f"Traceback: {''.join(traceback.format_tb(exception.__traceback__))}")
            else:
                logger.critical(f"UNHANDLED ERROR: {context.get('message', 'Unknown error')}")
                logger.critical(f"Full context: {context}")
        
        try:
            loop = asyncio.get_event_loop()
            loop.set_exception_handler(handle_exception)
            logger.info("Global exception handler installed")
        except RuntimeError:
            logger.warning("No event loop running - global exception handler not installed")


# Pre-configured circuit breakers for common services
api_circuit_breakers = {
    'dexscreener': CircuitBreaker(failure_threshold=3, timeout=30, name='dexscreener'),
    'helius': CircuitBreaker(failure_threshold=5, timeout=60, name='helius'),
    'telegram': CircuitBreaker(failure_threshold=3, timeout=30, name='telegram'),
    'solana_rpc': CircuitBreaker(failure_threshold=5, timeout=60, name='solana_rpc'),
    'pumpportal': CircuitBreaker(failure_threshold=3, timeout=30, name='pumpportal')
}


def get_circuit_breaker(service_name: str) -> CircuitBreaker:
    """Get circuit breaker for a service"""
    return api_circuit_breakers.get(service_name, CircuitBreaker(name=service_name))


async def safe_api_call(service_name: str, func: Callable, *args, **kwargs):
    """Make API call through circuit breaker and error handling"""
    circuit_breaker = get_circuit_breaker(service_name)
    return await circuit_breaker.call(func, *args, **kwargs)


# Network timeout configurations for different services
NETWORK_TIMEOUTS = {
    'dexscreener': 5.0,
    'helius': 10.0,
    'telegram': 15.0,
    'solana_rpc': 30.0,
    'pumpportal': 10.0,
    'default': 10.0
}


def get_timeout(service_name: str) -> float:
    """Get appropriate timeout for a service"""
    return NETWORK_TIMEOUTS.get(service_name, NETWORK_TIMEOUTS['default'])


# Initialize global exception handler
GlobalExceptionHandler.setup()
