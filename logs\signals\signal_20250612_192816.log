2025-06-12 19:28:16,272 - signal_handler - DEBUG - Loaded Telegram API ID: 27993698
2025-06-12 19:28:16,272 - signal_handler - DEBUG - Loaded Telegram API Hash: ********************************
2025-06-12 19:28:16,273 - signal_handler - INFO - Loaded session string from c:\Users\<USER>\Downloads\billy who\session_string.json (length: 353)
2025-06-12 19:28:16,275 - signal_handler - INFO - Using unique session name with timestamp: trading_sim_session_1749736696
2025-06-12 19:28:16,275 - signal_handler - INFO - Using session path: c:\Users\<USER>\Downloads\billy who\trading_sim_session_1749736696
2025-06-12 19:28:16,277 - signal_handler - INFO - Signal queue initialized with size: 0
2025-06-12 19:28:16,288 - signal_handler - INFO - Direct signal callback registered
2025-06-12 19:28:31,907 - signal_handler - INFO - [msg_1749736711904_3541] Queuing Telegram message: [WARNING] IMPORTANT BOT MESSAGE [?] 2025-06-12 19:28:31

[REFRESH] BOT STATE RESET

All positions, s...
2025-06-12 19:28:31,907 - signal_handler - INFO - Started Telegram message queue processor
2025-06-12 19:28:31,908 - signal_handler - INFO - [msg_1749736711904_3541] Message queued with normal priority
2025-06-12 19:28:31,954 - signal_handler - INFO - [msg_1749736711954_4421] Queuing Telegram message:  PORTFOLIO UPDATE [?] STATE RESET

 Timestamp: 2025-06-12 19:28:31
[REPEAT] Trade: SIMULATION

[MONE...
2025-06-12 19:28:31,954 - signal_handler - INFO - [msg_1749736711954_4421] Message queued with normal priority
2025-06-12 19:28:31,962 - signal_handler - INFO - Message queue processor started with adaptive rate limiting
2025-06-12 19:28:31,963 - signal_handler - WARNING - [msg_1749736711904_3541] Telegram client not connected before sending, attempting to reconnect
2025-06-12 19:28:31,963 - signal_handler - INFO - API ID loaded: True
2025-06-12 19:28:31,963 - signal_handler - INFO - API Hash loaded: True
2025-06-12 19:28:31,963 - signal_handler - INFO - Phone number loaded: True
2025-06-12 19:28:31,964 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-12 19:28:31,964 - signal_handler - INFO - Creating new Telegram client (attempt 1/3)
2025-06-12 19:28:31,964 - signal_handler - INFO - Using saved session string
2025-06-12 19:28:31,965 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-12 19:28:32,228 - signal_handler - INFO - Checking authorization status...
2025-06-12 19:28:32,285 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 19:28:32,286 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-12 19:28:32,286 - signal_handler - INFO - Checking authorization status...
2025-06-12 19:28:32,338 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 19:28:32,338 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-12 19:28:32,339 - signal_handler - INFO - Checking authorization status...
2025-06-12 19:28:32,398 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 19:28:32,399 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-12 19:28:32,399 - signal_handler - INFO - Sending test message to verify connection to channel -1002525039395
2025-06-12 19:28:32,400 - signal_handler - INFO - Sending test message directly to channel ID: -1002525039395
2025-06-12 19:28:32,575 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-12 19:28:32,576 - signal_handler - INFO - [msg_1749736711904_3541] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:28:32,629 - signal_handler - INFO - [msg_1749736711904_3541] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:28:32,721 - signal_handler - INFO - [msg_1749736711904_3541] Message sent directly successfully using entity
2025-06-12 19:28:32,722 - signal_handler - INFO - [msg_1749736711904_3541] Message sent successfully via direct send method
2025-06-12 19:28:32,722 - signal_handler - INFO - [msg_1749736711904_3541] Message sent successfully on attempt 1
2025-06-12 19:28:32,723 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 19:28:33,231 - signal_handler - INFO - [msg_1749736711954_4421] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:28:33,283 - signal_handler - INFO - [msg_1749736711954_4421] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:28:33,386 - signal_handler - INFO - [msg_1749736711954_4421] Message sent directly successfully using entity
2025-06-12 19:28:33,386 - signal_handler - INFO - [msg_1749736711954_4421] Message sent successfully via direct send method
2025-06-12 19:28:33,386 - signal_handler - INFO - [msg_1749736711954_4421] Message sent successfully on attempt 1
