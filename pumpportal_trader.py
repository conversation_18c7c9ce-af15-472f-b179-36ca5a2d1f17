#!/usr/bin/env python3
"""
PumpPortal Trader Integration for Real Mode
Replaces GMGN trader with direct Solana transactions via Helius RPC
"""

import logging
import os
import sys
import base64
from typing import Dict, Any, Optional
from dotenv import load_dotenv
from helius_rate_limiter import get_helius_rate_limiter

# Import Solana packages
try:
    import requests
    import base58
    from solders.keypair import Keypair
    from solders.transaction import VersionedTransaction
    from solders import message
except ImportError as e:
    print(f"❌ Missing packages: {e}")
    print("💡 Install: pip install solders solana requests base58")
    sys.exit(1)

logger = logging.getLogger(__name__)

class PumpPortalTrader:
    """PumpPortal trader for real mode execution - replaces GMGN trader"""
    
    def __init__(self, config_manager):
        self.config_manager = config_manager

        # Get API settings
        api_endpoints = config_manager.get_section('api_endpoints')
        self.pumpportal_api_url = api_endpoints.get('pumpportal_api', 'https://pumpportal.fun/api/trade-local')
        self.helius_rpc_url = self._get_helius_rpc_url()

        # Initialize Helius rate limiter
        self.helius_limiter = get_helius_rate_limiter(config_manager)

        # SURGICAL FIX: Load transaction settings for external fees from root level
        self.transaction_settings = config_manager.get_section('transaction_settings')
        logger.info(f"Loaded transaction settings: {self.transaction_settings}")

        # Load default wallet
        self.default_wallet = self._load_default_wallet()

        logger.info("PumpPortal Trader initialized for real mode")
        logger.info(f"API URL: {self.pumpportal_api_url}")
        logger.info(f"RPC URL: {self.helius_rpc_url[:50]}...")
        logger.info(f"Helius rate limiter: {self.helius_limiter.config.requests_per_second} RPS")
        
    def _get_helius_rpc_url(self) -> str:
        """Get Helius RPC URL with API key"""
        try:
            api_endpoints = self.config_manager.get_section('api_endpoints')
            helius_template = api_endpoints.get('helius_rpc', 'https://mainnet.helius-rpc.com/?api-key={api_key}')
            
            # Get API key from .env
            load_dotenv(override=True)
            api_key = os.getenv('HELIUS_API_KEY')
            
            if not api_key:
                logger.warning("HELIUS_API_KEY not found in .env, using default RPC")
                return api_endpoints.get('solana_rpc', 'https://api.mainnet-beta.solana.com')
            
            return helius_template.format(api_key=api_key)
            
        except Exception as e:
            logger.error(f"Error getting Helius RPC URL: {e}")
            return 'https://api.mainnet-beta.solana.com'
    
    def _load_default_wallet(self) -> Optional[Dict[str, Any]]:
        """Load default wallet from .env"""
        try:
            load_dotenv(override=True)
            
            # Try to find any wallet in .env
            for key in os.environ:
                if key.startswith('WALLET_') and key.endswith('_PRIVATE_KEY'):
                    wallet_name = key.replace('WALLET_', '').replace('_PRIVATE_KEY', '')
                    private_key = os.getenv(key)
                    
                    if private_key:
                        logger.info(f"Found default wallet: {wallet_name}")
                        return {
                            'name': wallet_name,
                            'private_key': private_key
                        }
            
            logger.warning("No wallet found in .env file")
            return None
            
        except Exception as e:
            logger.error(f"Error loading default wallet: {e}")
            return None
    
    def get_wallet_from_env(self, wallet_name: str) -> Optional[Dict[str, Any]]:
        """Get specific wallet from .env file"""
        try:
            load_dotenv(override=True)
            private_key = os.getenv(f'WALLET_{wallet_name.upper()}_PRIVATE_KEY')
            
            if not private_key:
                return None
            
            return {
                'name': wallet_name,
                'private_key': private_key
            }
        except Exception as e:
            logger.error(f"Error loading wallet {wallet_name}: {e}")
            return None
    
    async def check_wallet_balance(self) -> float:
        """Check current SOL balance of the default wallet"""
        try:
            if not self.default_wallet:
                logger.error("No wallet available for balance check")
                return 0.0

            # Get public key from wallet
            private_key_bytes = base58.b58decode(self.default_wallet['private_key'])
            if len(private_key_bytes) == 64:
                keypair = Keypair.from_bytes(private_key_bytes)
            elif len(private_key_bytes) == 32:
                keypair = Keypair.from_seed(private_key_bytes)
            else:
                keypair = Keypair.from_bytes(private_key_bytes)
            public_key = str(keypair.pubkey())

            # Prepare RPC request for balance
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getBalance",
                "params": [public_key]
            }

            # Use Helius rate limiter for balance check
            response_data = self.helius_limiter.make_rpc_request(self.helius_rpc_url, payload, timeout=6)

            if response_data and 'result' in response_data and 'value' in response_data['result']:
                lamports = response_data['result']['value']
                sol_balance = lamports / 1_000_000_000  # Convert lamports to SOL
                logger.debug(f"Wallet balance: {sol_balance:.6f} SOL")
                return sol_balance
            else:
                logger.error("Failed to get wallet balance from RPC")
                return 0.0

        except Exception as e:
            logger.error(f"Error checking wallet balance: {e}")
            return 0.0

    async def buy_token(self, token_address: str, sol_amount: float, slippage_percent: float = 10.0) -> Dict[str, Any]:
        """Execute buy order via PumpPortal with balance validation and external fees"""
        try:
            if not self.default_wallet:
                logger.error("No wallet available for buy order")
                return {
                    "success": False,
                    "data": {},
                    "error": "No wallet available for buy order",
                    "error_code": "NO_WALLET",
                    "retry_after": 0
                }

            # CALCULATE EXTERNAL FEES FROM CONFIG
            buy_tip = self.transaction_settings.get('buy_tip_sol', 0.005)
            gas_price = self.transaction_settings.get('gas_price_sol', 0.005)
            handling_fee_percent = self.transaction_settings.get('handling_fee_percent', 1.0)
            platform_fee_percent = self.transaction_settings.get('platform_fee_percent', 1.0)

            # Calculate percentage-based fees
            handling_fee_sol = sol_amount * (handling_fee_percent / 100.0)
            platform_fee_sol = sol_amount * (platform_fee_percent / 100.0)

            # Total external fees
            total_external_fees = buy_tip + gas_price + handling_fee_sol + platform_fee_sol

            logger.info(f"💰 EXTERNAL FEES: Buy tip: {buy_tip:.4f} SOL, Gas: {gas_price:.4f} SOL, Handling: {handling_fee_sol:.4f} SOL, Platform: {platform_fee_sol:.4f} SOL")
            logger.info(f"💰 TOTAL EXTERNAL FEES: {total_external_fees:.4f} SOL")

            # BALANCE VALIDATION - Check wallet balance including external fees
            current_balance = await self.check_wallet_balance()
            total_required = sol_amount + total_external_fees

            if current_balance < total_required:
                error_msg = f"Insufficient balance: Need {total_required:.4f} SOL (trade: {sol_amount:.4f} + fees: {total_external_fees:.4f}), have {current_balance:.4f} SOL"
                logger.error(f"❌ {error_msg}")
                return {
                    "success": False,
                    "data": {"required": total_required, "available": current_balance},
                    "error": error_msg,
                    "error_code": "INSUFFICIENT_BALANCE",
                    "retry_after": 0
                }

            if current_balance < 0.05:  # Warn when balance is getting low
                logger.warning(f"⚠️ Low wallet balance: {current_balance:.4f} SOL remaining")

            # Convert slippage from percentage to integer
            slippage_int = int(slippage_percent)

            logger.info(f"🟢 REAL BUY: {sol_amount:.4f} SOL of {token_address} (slippage: {slippage_percent}%) + {total_external_fees:.4f} SOL fees - Balance: {current_balance:.4f} SOL")

            result = await self._execute_trade(
                wallet=self.default_wallet,
                action="buy",
                mint=token_address,
                amount=sol_amount,
                denominated_in_sol=True,
                slippage=slippage_int,
                external_fees=total_external_fees
            )

            if result["success"]:
                logger.info(f"✅ Buy successful: {result['transaction_signature']} (Total cost: {sol_amount + total_external_fees:.4f} SOL)")
                return {
                    "success": True,
                    "data": {
                        "transaction_signature": result['transaction_signature'],
                        "sol_amount": sol_amount,
                        "total_cost": sol_amount + total_external_fees,
                        "external_fees": total_external_fees
                    },
                    "error": "",
                    "error_code": "",
                    "retry_after": 0
                }
            else:
                logger.error(f"❌ Buy failed: {result['error']}")
                return {
                    "success": False,
                    "data": {},
                    "error": result['error'],
                    "error_code": "TRADE_EXECUTION_FAILED",
                    "retry_after": 5
                }

        except Exception as e:
            logger.error(f"Error in buy_token: {e}")
            return {
                "success": False,
                "data": {},
                "error": str(e),
                "error_code": "UNEXPECTED_ERROR",
                "retry_after": 10
            }
    
    async def sell_token(self, token_address: str, token_amount: float, slippage_percent: float = 10.0) -> Dict[str, Any]:
        """Execute sell order via PumpPortal with external fees"""
        try:
            if not self.default_wallet:
                logger.error("No wallet available for sell order")
                return {
                    "success": False,
                    "data": {},
                    "error": "No wallet available for sell order",
                    "error_code": "NO_WALLET",
                    "retry_after": 0
                }

            # CALCULATE EXTERNAL FEES FROM CONFIG (for sell operations)
            gas_price = self.transaction_settings.get('gas_price_sol', 0.005)
            handling_fee_percent = self.transaction_settings.get('handling_fee_percent', 1.0)
            platform_fee_percent = self.transaction_settings.get('platform_fee_percent', 1.0)

            # Note: For sells, percentage fees will be deducted from SOL received
            # Gas fee still needs to be available in wallet
            logger.info(f"💰 SELL FEES: Gas: {gas_price:.4f} SOL, Handling: {handling_fee_percent}%, Platform: {platform_fee_percent}%")

            # Convert slippage from percentage to integer
            slippage_int = int(slippage_percent)

            logger.info(f"🔴 REAL SELL: {token_amount:.4f} tokens of {token_address} (slippage: {slippage_percent}%) + {gas_price:.4f} SOL gas")

            result = await self._execute_trade(
                wallet=self.default_wallet,
                action="sell",
                mint=token_address,
                amount=token_amount,
                denominated_in_sol=False,
                slippage=slippage_int,
                external_fees=gas_price  # Only gas fee for sells
            )

            if result["success"]:
                logger.info(f"✅ Sell successful: {result['transaction_signature']} (Gas cost: {gas_price:.4f} SOL)")
                return {
                    "success": True,
                    "data": {
                        "transaction_signature": result['transaction_signature'],
                        "token_amount": token_amount,
                        "gas_cost": gas_price
                    },
                    "error": "",
                    "error_code": "",
                    "retry_after": 0
                }
            else:
                logger.error(f"❌ Sell failed: {result['error']}")
                return {
                    "success": False,
                    "data": {},
                    "error": result['error'],
                    "error_code": "TRADE_EXECUTION_FAILED",
                    "retry_after": 5
                }

        except Exception as e:
            logger.error(f"Error in sell_token: {e}")
            return {
                "success": False,
                "data": {},
                "error": str(e),
                "error_code": "UNEXPECTED_ERROR",
                "retry_after": 10
            }
    
    async def get_balance(self) -> float:
        """Get SOL balance from wallet"""
        try:
            if not self.default_wallet:
                return 0.0
            
            # Create keypair to get public key
            private_key_bytes = base58.b58decode(self.default_wallet['private_key'])
            if len(private_key_bytes) == 64:
                keypair = Keypair.from_bytes(private_key_bytes)
            elif len(private_key_bytes) == 32:
                keypair = Keypair.from_seed(private_key_bytes)
            else:
                keypair = Keypair.from_bytes(private_key_bytes)
            
            public_key = str(keypair.pubkey())
            
            # Get balance via rate-limited RPC
            rpc_payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "getBalance",
                "params": [public_key]
            }

            response_data = self.helius_limiter.make_rpc_request(self.helius_rpc_url, rpc_payload, timeout=10)

            if response_data and 'result' in response_data:
                lamports = response_data['result']['value']
                sol_balance = lamports / 1_000_000_000  # Convert lamports to SOL
                return sol_balance
            
            return 0.0
            
        except Exception as e:
            logger.error(f"Error getting balance: {e}")
            return 0.0
    
    async def _execute_trade(self, wallet: Dict[str, Any], action: str, mint: str, amount: float,
                           denominated_in_sol: bool, slippage: int, external_fees: float = 0.0) -> Dict[str, Any]:
        """Execute trade via PumpPortal API"""
        try:
            # Create keypair from private key
            private_key_bytes = base58.b58decode(wallet['private_key'])
            
            if len(private_key_bytes) == 64:
                keypair = Keypair.from_bytes(private_key_bytes)
            elif len(private_key_bytes) == 32:
                keypair = Keypair.from_seed(private_key_bytes)
            else:
                keypair = Keypair.from_bytes(private_key_bytes)
            
            public_key = str(keypair.pubkey())

            # Get priority fee from config (gas_price_sol)
            priority_fee = self.transaction_settings.get('gas_price_sol', 0.005)

            # Prepare PumpPortal API request
            payload = {
                "publicKey": public_key,
                "action": action,
                "mint": mint,
                "amount": amount,
                "denominatedInSol": "true" if denominated_in_sol else "false",
                "slippage": slippage,
                "priorityFee": priority_fee,  # Use config-based priority fee
                "pool": "auto"
            }

            logger.debug(f"PumpPortal payload: action={action}, amount={amount}, slippage={slippage}%, priorityFee={priority_fee} SOL")
            
            # Get transaction from PumpPortal
            response = requests.post(self.pumpportal_api_url, json=payload, timeout=30)
            
            if response.status_code != 200:
                return {
                    "success": False,
                    "error": f"PumpPortal API error: {response.status_code} - {response.text}"
                }
            
            # Get transaction bytes
            tx_bytes = response.content
            
            # Sign transaction
            raw_transaction = VersionedTransaction.from_bytes(base64.b64decode(base64.b64encode(tx_bytes)))
            signature = keypair.sign_message(message.to_bytes_versioned(raw_transaction.message))
            signed_txn = VersionedTransaction.populate(raw_transaction.message, [signature])
            signed_tx_bytes = bytes(signed_txn)
            
            # Send transaction via RPC
            signed_tx_b64 = base64.b64encode(signed_tx_bytes).decode('utf-8')
            
            rpc_payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "sendTransaction",
                "params": [
                    signed_tx_b64,
                    {
                        "encoding": "base64",
                        "preflightCommitment": "confirmed",
                        "skipPreflight": False,
                        "maxRetries": 3
                    }
                ]
            }
            
            # Send transaction via rate-limited RPC
            rpc_result = self.helius_limiter.make_rpc_request(self.helius_rpc_url, rpc_payload, timeout=30)

            if rpc_result and 'result' in rpc_result:
                transaction_id = rpc_result['result']
                return {
                    "success": True,
                    "transaction_signature": transaction_id,
                    "solscan_url": f"https://solscan.io/tx/{transaction_id}"
                }
            elif rpc_result and 'error' in rpc_result:
                error_msg = rpc_result.get('error', {}).get('message', 'Unknown RPC error')
                return {"success": False, "error": f"RPC error: {error_msg}"}
            else:
                return {
                    "success": False,
                    "error": "RPC request failed - rate limited or network error"
                }
                
        except Exception as e:
            return {"success": False, "error": str(e)}
