#!/usr/bin/env python3
"""
Configuration Validator - Production Config Validation
Operator's Rule: Validate everything before it breaks in production
"""

import os
import json
import logging
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class ValidationResult:
    """Configuration validation result"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    critical_issues: List[str]
    
    def add_error(self, message: str):
        self.errors.append(message)
        self.is_valid = False
    
    def add_warning(self, message: str):
        self.warnings.append(message)
    
    def add_critical(self, message: str):
        self.critical_issues.append(message)
        self.is_valid = False

class ConfigValidator:
    """Comprehensive configuration validator"""
    
    def __init__(self):
        self.required_env_vars = [
            'TELEGRAM_API_ID',
            'TELEGRAM_API_HASH', 
            'HELIUS_API_KEY'
        ]
        
        self.required_config_sections = [
            'trading_settings',
            'telegram_settings',
            'api_endpoints'
        ]

        self.required_trading_settings = [
            'total_sol_capital',
            'sol_trade_amount',  # Actual field name in config
            'max_concurrent_trades',
            'max_hold_time_minutes'
        ]
    
    def validate_environment(self) -> ValidationResult:
        """Validate environment variables"""
        result = ValidationResult(True, [], [], [])
        
        for env_var in self.required_env_vars:
            value = os.getenv(env_var)
            if not value:
                result.add_critical(f"Missing environment variable: {env_var}")
            elif env_var == 'TELEGRAM_API_ID' and not value.isdigit():
                result.add_error(f"TELEGRAM_API_ID must be numeric, got: {value}")
            elif env_var == 'TELEGRAM_API_HASH' and len(value) < 32:
                result.add_error(f"TELEGRAM_API_HASH appears invalid (too short)")
        
        # Check for wallet configuration
        wallet_found = False
        for key in os.environ:
            if key.startswith('WALLET_') and key.endswith('_PRIVATE_KEY'):
                wallet_value = os.getenv(key)
                if wallet_value and wallet_value != 'demo_wallet_replace_with_real_private_key_for_trading':
                    wallet_found = True
                    # Validate wallet key format (basic check)
                    if len(wallet_value) < 32:
                        result.add_error(f"Wallet private key {key} appears invalid (too short)")
                    break
        
        if not wallet_found:
            result.add_warning("No valid wallet private key configured (will run in simulation mode only)")
        
        return result
    
    def validate_config_file(self, config_path: str = "finalconfig.json") -> ValidationResult:
        """Validate configuration file"""
        result = ValidationResult(True, [], [], [])
        
        # Check if file exists
        if not os.path.exists(config_path):
            result.add_critical(f"Configuration file not found: {config_path}")
            return result
        
        # Load and parse JSON
        try:
            with open(config_path, 'r') as f:
                config = json.load(f)
        except json.JSONDecodeError as e:
            result.add_critical(f"Invalid JSON in {config_path}: {e}")
            return result
        except Exception as e:
            result.add_critical(f"Error reading {config_path}: {e}")
            return result
        
        # Validate required sections
        for section in self.required_config_sections:
            if section not in config:
                result.add_critical(f"Missing required config section: {section}")
            elif not isinstance(config[section], dict):
                result.add_error(f"Config section {section} must be an object")
        
        # Validate trading settings
        if 'trading_settings' in config:
            self._validate_trading_settings(config['trading_settings'], result)
        
        # Validate transaction settings (nested inside trading_settings)
        if 'trading_settings' in config and 'transaction_settings' in config['trading_settings']:
            self._validate_transaction_settings(config['trading_settings']['transaction_settings'], result)
        
        # Validate telegram settings
        if 'telegram_settings' in config:
            self._validate_telegram_settings(config['telegram_settings'], result)
        
        # Validate API endpoints
        if 'api_endpoints' in config:
            self._validate_api_endpoints(config['api_endpoints'], result)
        
        return result
    
    def _validate_trading_settings(self, settings: Dict[str, Any], result: ValidationResult):
        """Validate trading settings"""
        for required in self.required_trading_settings:
            if required not in settings:
                result.add_error(f"Missing required trading setting: {required}")
            else:
                value = settings[required]
                if required in ['total_sol_capital', 'sol_trade_amount'] and (not isinstance(value, (int, float)) or value <= 0):
                    result.add_error(f"Trading setting {required} must be positive number, got: {value}")
                elif required in ['max_concurrent_trades', 'max_hold_time_minutes'] and (not isinstance(value, int) or value <= 0):
                    result.add_error(f"Trading setting {required} must be positive integer, got: {value}")
        
        # Validate rug protection settings
        if 'rug_protection' in settings:
            rug_settings = settings['rug_protection']
            rug_required = ['min_liquidity_usd', 'min_volume_5m_usd', 'min_fdv_usd']
            for req in rug_required:
                if req not in rug_settings:
                    result.add_warning(f"Missing rug protection setting: {req}")
                elif not isinstance(rug_settings[req], (int, float)) or rug_settings[req] < 0:
                    result.add_error(f"Rug protection {req} must be non-negative number")
        
        # Validate strategy settings
        if 'strategies' in settings:
            strategies = settings['strategies']
            for strategy_name, strategy_config in strategies.items():
                if not isinstance(strategy_config, dict):
                    result.add_error(f"Strategy {strategy_name} must be an object")
                    continue
                
                required_strategy_fields = ['take_profit_percent', 'stop_loss_percent', 'min_confidence']
                for field in required_strategy_fields:
                    if field not in strategy_config:
                        result.add_warning(f"Strategy {strategy_name} missing {field}")
    
    def _validate_transaction_settings(self, settings: Dict[str, Any], result: ValidationResult):
        """Validate transaction settings"""
        required_tx_fields = ['slippage_percent', 'gas_price_sol']
        for field in required_tx_fields:
            if field not in settings:
                result.add_warning(f"Missing transaction setting: {field}")
            elif not isinstance(settings[field], (int, float)) or settings[field] < 0:
                result.add_error(f"Transaction setting {field} must be non-negative number")
        
        # Validate slippage is reasonable
        if 'slippage_percent' in settings:
            slippage = settings['slippage_percent']
            if slippage > 10:
                result.add_warning(f"High slippage setting: {slippage}% (consider reducing)")
            elif slippage < 0.1:
                result.add_warning(f"Very low slippage setting: {slippage}% (may cause failed trades)")
    
    def _validate_telegram_settings(self, settings: Dict[str, Any], result: ValidationResult):
        """Validate telegram settings"""
        if 'target_channels' in settings:
            channels = settings['target_channels']
            if not isinstance(channels, list):
                result.add_error("target_channels must be a list")
            elif len(channels) == 0:
                result.add_warning("No target channels configured")
        
        if 'info_group_id' in settings:
            info_group = settings['info_group_id']
            if not isinstance(info_group, (int, str)):
                result.add_error("info_group_id must be a number or string")
    
    def _validate_api_endpoints(self, settings: Dict[str, Any], result: ValidationResult):
        """Validate API endpoints"""
        required_endpoints = ['solana_rpc', 'helius_rpc']
        for endpoint in required_endpoints:
            if endpoint not in settings:
                result.add_warning(f"Missing API endpoint: {endpoint}")
            elif not isinstance(settings[endpoint], str) or not settings[endpoint].startswith('http'):
                result.add_error(f"API endpoint {endpoint} must be a valid HTTP URL")
    
    def validate_all(self, config_path: str = "finalconfig.json") -> ValidationResult:
        """Validate all configuration"""
        # Combine all validation results
        env_result = self.validate_environment()
        config_result = self.validate_config_file(config_path)
        
        combined = ValidationResult(
            is_valid=env_result.is_valid and config_result.is_valid,
            errors=env_result.errors + config_result.errors,
            warnings=env_result.warnings + config_result.warnings,
            critical_issues=env_result.critical_issues + config_result.critical_issues
        )
        
        return combined
    
    def print_validation_report(self, result: ValidationResult):
        """Print detailed validation report"""
        print("\n" + "="*80)
        print("🔍 CONFIGURATION VALIDATION REPORT")
        print("="*80)
        
        if result.is_valid:
            print("✅ OVERALL STATUS: VALID")
        else:
            print("❌ OVERALL STATUS: INVALID")
        
        if result.critical_issues:
            print(f"\n🚨 CRITICAL ISSUES ({len(result.critical_issues)}):")
            for issue in result.critical_issues:
                print(f"  • {issue}")
        
        if result.errors:
            print(f"\n❌ ERRORS ({len(result.errors)}):")
            for error in result.errors:
                print(f"  • {error}")
        
        if result.warnings:
            print(f"\n⚠️  WARNINGS ({len(result.warnings)}):")
            for warning in result.warnings:
                print(f"  • {warning}")
        
        if result.is_valid and not result.warnings:
            print("\n🎉 Configuration is perfect!")
        elif result.is_valid:
            print("\n✅ Configuration is valid (with warnings)")
        else:
            print("\n🛑 Configuration must be fixed before production deployment")
        
        print("="*80)

def validate_production_config(config_path: str = "finalconfig.json", print_report: bool = True) -> ValidationResult:
    """Main validation function"""
    validator = ConfigValidator()
    result = validator.validate_all(config_path)
    
    if print_report:
        validator.print_validation_report(result)
    
    return result

if __name__ == "__main__":
    # Run validation when script is executed directly
    validate_production_config()
