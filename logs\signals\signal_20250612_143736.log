2025-06-12 14:37:36,794 - signal_handler - DEBUG - Loaded Telegram API ID: 27993698
2025-06-12 14:37:36,794 - signal_handler - DEBUG - Loaded Telegram API Hash: ********************************
2025-06-12 14:37:36,795 - signal_handler - INFO - Loaded session string from c:\Users\<USER>\Downloads\billy who\session_string.json (length: 353)
2025-06-12 14:37:36,795 - signal_handler - INFO - Using unique session name with timestamp: trading_sim_session_1749719256
2025-06-12 14:37:36,795 - signal_handler - INFO - Using session path: c:\Users\<USER>\Downloads\billy who\trading_sim_session_1749719256
2025-06-12 14:37:36,796 - signal_handler - INFO - Signal queue initialized with size: 0
2025-06-12 14:37:36,808 - signal_handler - INFO - Direct signal callback registered
2025-06-12 14:37:50,311 - signal_handler - INFO - [msg_1749719270308_3896] Queuing Telegram message: [WRENCH] CRITICAL STATE FIX COMPLETED [?] 2025-06-12 14:37:50

[CHECK] STATE SYNCHRONIZATION SUCCESS...
2025-06-12 14:37:50,312 - signal_handler - INFO - Started Telegram message queue processor
2025-06-12 14:37:50,312 - signal_handler - INFO - [msg_1749719270308_3896] Message queued with normal priority
2025-06-12 14:37:50,326 - signal_handler - INFO - Message queue processor started with adaptive rate limiting
2025-06-12 14:37:50,326 - signal_handler - WARNING - [msg_1749719270308_3896] Telegram client not connected before sending, attempting to reconnect
2025-06-12 14:37:50,326 - signal_handler - INFO - API ID loaded: True
2025-06-12 14:37:50,326 - signal_handler - INFO - API Hash loaded: True
2025-06-12 14:37:50,327 - signal_handler - INFO - Phone number loaded: True
2025-06-12 14:37:50,327 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-12 14:37:50,327 - signal_handler - INFO - Creating new Telegram client (attempt 1/3)
2025-06-12 14:37:50,328 - signal_handler - INFO - Using saved session string
2025-06-12 14:37:50,329 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-12 14:37:50,584 - signal_handler - INFO - Checking authorization status...
2025-06-12 14:37:50,637 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 14:37:50,637 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-12 14:37:50,638 - signal_handler - INFO - Checking authorization status...
2025-06-12 14:37:50,695 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 14:37:50,696 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-12 14:37:50,700 - signal_handler - INFO - Checking authorization status...
2025-06-12 14:37:50,753 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 14:37:50,754 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-12 14:37:50,754 - signal_handler - INFO - Sending test message to verify connection to channel -1002525039395
2025-06-12 14:37:50,755 - signal_handler - INFO - Sending test message directly to channel ID: -1002525039395
2025-06-12 14:37:50,903 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-12 14:37:50,904 - signal_handler - INFO - [msg_1749719270308_3896] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:37:50,959 - signal_handler - INFO - [msg_1749719270308_3896] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:37:51,053 - signal_handler - INFO - [msg_1749719270308_3896] Message sent directly successfully using entity
2025-06-12 14:37:51,053 - signal_handler - INFO - [msg_1749719270308_3896] Message sent successfully via direct send method
2025-06-12 14:37:51,054 - signal_handler - INFO - [msg_1749719270308_3896] Message sent successfully on attempt 1
2025-06-12 14:38:00,634 - signal_handler - INFO - [msg_1749719280634_7212] Queuing Telegram message: [WARNING] IMPORTANT BOT MESSAGE [?] 2025-06-12 14:38:00

[REFRESH] BOT STATE RESET

All positions, s...
2025-06-12 14:38:00,635 - signal_handler - INFO - [msg_1749719280634_7212] Message queued with normal priority
2025-06-12 14:38:00,669 - signal_handler - INFO - [msg_1749719280669_4748] Queuing Telegram message:  PORTFOLIO UPDATE [?] STATE RESET

 Timestamp: 2025-06-12 14:38:00
[REPEAT] Trade: SIMULATION

[MONE...
2025-06-12 14:38:00,670 - signal_handler - INFO - [msg_1749719280669_4748] Message queued with normal priority
2025-06-12 14:38:00,676 - signal_handler - INFO - [msg_1749719280634_7212] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:38:00,736 - signal_handler - INFO - [msg_1749719280634_7212] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:38:00,835 - signal_handler - INFO - [msg_1749719280634_7212] Message sent directly successfully using entity
2025-06-12 14:38:00,835 - signal_handler - INFO - [msg_1749719280634_7212] Message sent successfully via direct send method
2025-06-12 14:38:00,836 - signal_handler - INFO - [msg_1749719280634_7212] Message sent successfully on attempt 1
2025-06-12 14:38:00,836 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 14:38:01,342 - signal_handler - INFO - [msg_1749719280669_4748] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:38:01,404 - signal_handler - INFO - [msg_1749719280669_4748] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:38:01,504 - signal_handler - INFO - [msg_1749719280669_4748] Message sent directly successfully using entity
2025-06-12 14:38:01,506 - signal_handler - INFO - [msg_1749719280669_4748] Message sent successfully via direct send method
2025-06-12 14:38:01,507 - signal_handler - INFO - [msg_1749719280669_4748] Message sent successfully on attempt 1
2025-06-12 14:38:30,295 - signal_handler - INFO - API ID loaded: True
2025-06-12 14:38:30,295 - signal_handler - INFO - API Hash loaded: True
2025-06-12 14:38:30,295 - signal_handler - INFO - Phone number loaded: True
2025-06-12 14:38:30,296 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-12 14:38:30,296 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-12 14:38:30,296 - signal_handler - INFO - Checking authorization status...
2025-06-12 14:38:30,354 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 14:38:30,355 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-12 14:38:30,355 - signal_handler - INFO - Checking authorization status...
2025-06-12 14:38:30,423 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 14:38:30,424 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-12 14:38:30,424 - signal_handler - INFO - Checking authorization status...
2025-06-12 14:38:30,523 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 14:38:30,523 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-12 14:38:30,524 - signal_handler - INFO - Sending test message to verify connection to channel -1002525039395
2025-06-12 14:38:30,525 - signal_handler - INFO - Sending test message directly to channel ID: -1002525039395
2025-06-12 14:38:30,721 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-12 14:38:30,728 - signal_handler - INFO - [msg_1749719310727_5157] Queuing Telegram message:  PORTFOLIO UPDATE [?] PERIODIC UPDATE

 Timestamp: 2025-06-12 14:38:30
[REPEAT] Trade: SIMULATION

[...
2025-06-12 14:38:30,729 - signal_handler - INFO - [msg_1749719310727_5157] Message queued with normal priority
2025-06-12 14:38:30,829 - signal_handler - INFO - [msg_1749719310727_5157] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:38:31,024 - signal_handler - INFO - [msg_1749719310727_5157] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:38:31,230 - signal_handler - INFO - [msg_1749719310727_5157] Message sent directly successfully using entity
2025-06-12 14:38:31,231 - signal_handler - INFO - [msg_1749719310727_5157] Message sent successfully via direct send method
2025-06-12 14:38:31,239 - signal_handler - INFO - [msg_1749719310727_5157] Message sent successfully on attempt 1
2025-06-12 14:38:31,243 - signal_handler - WARNING - [WARNING] Using channel IDs directly (no entities resolved)
2025-06-12 14:38:31,244 - signal_handler - WARNING - Added event handler using channel IDs (entities not resolved yet)
2025-06-12 14:38:31,245 - signal_handler - INFO - Signal listener started in background task. Waiting for messages...
2025-06-12 14:38:31,251 - signal_handler - INFO - Direct signal callback registered
2025-06-12 14:38:31,255 - signal_handler - INFO - [msg_1749719311253_6845] [STARTUP NOTIFICATION]: [ROCKET] Bot Started
[REFRESH] Mode: SIMULATION
[GRAPH] Strategy: SAFE
[MONEY] Starting Capital: 1.5...
2025-06-12 14:38:31,258 - signal_handler - INFO - [msg_1749719311253_6845] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:38:31,259 - signal_handler - INFO - Signal listener running (attempt 1/3).
2025-06-12 14:38:31,345 - signal_handler - INFO - [msg_1749719311253_6845] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:38:31,442 - signal_handler - INFO - [msg_1749719311253_6845] Message sent directly successfully using entity
2025-06-12 14:38:31,442 - signal_handler - INFO - [msg_1749719311253_6845] Message sent directly successfully
2025-06-12 14:38:55,011 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$11.2K(+227.5%)**
*...
2025-06-12 14:38:55,013 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 14:38:55,014 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 14:38:55,014 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 14:38:55,015 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 14:38:55,019 - signal_handler - INFO - Found contract address: 4X3iuneXpMVXRsxKNPcYVJzA4DGnV5XprSCQwbxQTCPm
2025-06-12 14:38:55,020 - signal_handler - INFO - Found contract address: GZVSEAajExLJEvACHHQcujBw7nJq98GWUEZtood9LM9b
2025-06-12 14:38:55,022 - signal_handler - INFO - Found 2 unique token addresses
2025-06-12 14:38:55,023 - signal_handler - INFO - Token address: 4X3iuneXpMVXRsxKNPcYVJzA4DGnV5XprSCQwbxQTCPm
2025-06-12 14:38:55,023 - signal_handler - INFO - Token address: GZVSEAajExLJEvACHHQcujBw7nJq98GWUEZtood9LM9b
2025-06-12 14:38:55,026 - signal_handler - INFO - Detected GMGN format
2025-06-12 14:38:55,027 - signal_handler - INFO - Found token symbol: 11
2025-06-12 14:38:55,028 - signal_handler - INFO - Found FDV: 11.2K - 11.20 (+227.5%)
2025-06-12 14:38:55,029 - signal_handler - INFO - Detected GMGN channel signal for token: 4X3iuneXpMVXRsxKNPcYVJzA4DGnV5XprSCQwbxQTCPm
2025-06-12 14:38:55,029 - signal_handler - INFO - Extracted signal: Token=4X3iuneXpMVXRsxKNPcYVJzA4DGnV5XprSCQwbxQTCPm, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 14:38:55,030 - signal_handler - WARNING - LOW SIGNAL VOLUME: GMGN - 0/8 expected this hour
2025-06-12 14:38:55,031 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solbix - 0/2 expected this hour
2025-06-12 14:38:55,031 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solana Early Trending - 0/1 expected this hour
2025-06-12 14:38:55,032 - signal_handler - INFO - Signal processing health: 1 received, 1 processed, 0 dropped (100.0% success rate)
2025-06-12 14:38:55,032 - signal_handler - INFO - Added signal to queue: 4X3iuneXpMVXRsxKNPcYVJzA4DGnV5XprSCQwbxQTCPm from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 14:38:55,033 - signal_handler - INFO - Calling direct signal callback for 4X3iuneXpMVXRsxKNPcYVJzA4DGnV5XprSCQwbxQTCPm
