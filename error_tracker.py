#!/usr/bin/env python3
"""
Error Tracker - Production Error Monitoring
Operator's Rule: Track everything, alert on patterns
"""

import logging
import time
import json
from datetime import datetime, timezone
from collections import defaultdict, deque
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class ErrorEvent:
    """Single error event"""
    timestamp: str
    error_type: str
    error_message: str
    module: str
    function: str
    severity: str
    context: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)

class ErrorTracker:
    """Track and analyze error patterns"""
    
    def __init__(self):
        self.error_log_file = "production_errors.jsonl"
        self.error_counts = defaultdict(int)
        self.recent_errors = deque(maxlen=1000)  # Keep last 1000 errors
        self.error_patterns = defaultdict(list)
        self.alert_thresholds = {
            'same_error_5min': 5,      # Same error 5 times in 5 minutes
            'total_errors_1h': 50,     # 50 errors in 1 hour
            'critical_errors_1h': 5    # 5 critical errors in 1 hour
        }
        
    def record_error(self, 
                    error_type: str,
                    error_message: str,
                    module: str = "unknown",
                    function: str = "unknown",
                    severity: str = "error",
                    context: Optional[Dict[str, Any]] = None):
        """Record an error event"""
        try:
            error_event = ErrorEvent(
                timestamp=datetime.now(timezone.utc).isoformat(),
                error_type=error_type,
                error_message=error_message,
                module=module,
                function=function,
                severity=severity,
                context=context or {}
            )
            
            # Add to recent errors
            self.recent_errors.append(error_event)
            
            # Update counts
            self.error_counts[error_type] += 1
            
            # Track patterns
            pattern_key = f"{module}.{function}.{error_type}"
            self.error_patterns[pattern_key].append(time.time())
            
            # Clean old pattern data (keep last hour)
            cutoff_time = time.time() - 3600
            self.error_patterns[pattern_key] = [
                t for t in self.error_patterns[pattern_key] if t > cutoff_time
            ]
            
            # Log to file
            self._log_to_file(error_event)
            
            # Check for alerts
            self._check_error_alerts(error_event, pattern_key)
            
        except Exception as e:
            # Don't let error tracking break the system
            logger.error(f"Error in error tracker: {e}")
    
    def _log_to_file(self, error_event: ErrorEvent):
        """Log error to file"""
        try:
            with open(self.error_log_file, 'a') as f:
                f.write(json.dumps(error_event.to_dict()) + '\n')
        except Exception as e:
            logger.error(f"Failed to log error to file: {e}")
    
    def _check_error_alerts(self, error_event: ErrorEvent, pattern_key: str):
        """Check if error patterns warrant alerts"""
        try:
            current_time = time.time()
            
            # Check for repeated same error in 5 minutes
            recent_same_errors = [
                t for t in self.error_patterns[pattern_key] 
                if current_time - t < 300  # 5 minutes
            ]
            
            if len(recent_same_errors) >= self.alert_thresholds['same_error_5min']:
                logger.critical(f"🚨 ERROR PATTERN ALERT: {error_event.error_type} "
                              f"occurred {len(recent_same_errors)} times in 5 minutes")
            
            # Check total errors in last hour
            recent_all_errors = [
                e for e in self.recent_errors 
                if current_time - datetime.fromisoformat(e.timestamp.replace('Z', '+00:00')).timestamp() < 3600
            ]
            
            if len(recent_all_errors) >= self.alert_thresholds['total_errors_1h']:
                logger.critical(f"🚨 HIGH ERROR RATE: {len(recent_all_errors)} errors in last hour")
            
            # Check critical errors
            critical_errors = [
                e for e in recent_all_errors 
                if e.severity in ['critical', 'fatal']
            ]
            
            if len(critical_errors) >= self.alert_thresholds['critical_errors_1h']:
                logger.critical(f"🚨 CRITICAL ERROR SPIKE: {len(critical_errors)} critical errors in last hour")
                
        except Exception as e:
            logger.error(f"Error checking alerts: {e}")
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get error summary for monitoring"""
        try:
            current_time = time.time()
            
            # Errors in last hour
            recent_errors = [
                e for e in self.recent_errors 
                if current_time - datetime.fromisoformat(e.timestamp.replace('Z', '+00:00')).timestamp() < 3600
            ]
            
            # Group by type
            error_types = defaultdict(int)
            for error in recent_errors:
                error_types[error.error_type] += 1
            
            # Group by severity
            severity_counts = defaultdict(int)
            for error in recent_errors:
                severity_counts[error.severity] += 1
            
            return {
                'total_errors_1h': len(recent_errors),
                'error_types': dict(error_types),
                'severity_counts': dict(severity_counts),
                'top_errors': dict(sorted(error_types.items(), key=lambda x: x[1], reverse=True)[:5])
            }
            
        except Exception as e:
            logger.error(f"Error getting summary: {e}")
            return {}

# Global error tracker
_error_tracker = None

def get_error_tracker() -> ErrorTracker:
    """Get or create error tracker"""
    global _error_tracker
    if _error_tracker is None:
        _error_tracker = ErrorTracker()
    return _error_tracker

def track_error(error_type: str, 
               error_message: str,
               module: str = "unknown",
               function: str = "unknown", 
               severity: str = "error",
               context: Optional[Dict[str, Any]] = None):
    """Convenience function to track errors"""
    tracker = get_error_tracker()
    tracker.record_error(error_type, error_message, module, function, severity, context)

# Custom logging handler to automatically track errors
class ErrorTrackingHandler(logging.Handler):
    """Logging handler that automatically tracks errors"""
    
    def __init__(self):
        super().__init__()
        self.tracker = get_error_tracker()
    
    def emit(self, record):
        """Handle log record"""
        try:
            if record.levelno >= logging.ERROR:
                severity = "critical" if record.levelno >= logging.CRITICAL else "error"
                
                self.tracker.record_error(
                    error_type=record.name,
                    error_message=record.getMessage(),
                    module=record.module if hasattr(record, 'module') else record.name,
                    function=record.funcName if hasattr(record, 'funcName') else "unknown",
                    severity=severity,
                    context={
                        'filename': record.filename,
                        'lineno': record.lineno,
                        'levelname': record.levelname
                    }
                )
        except Exception:
            # Don't let error tracking break logging
            pass

def setup_error_tracking():
    """Setup automatic error tracking"""
    try:
        # Add error tracking handler to root logger
        root_logger = logging.getLogger()
        error_handler = ErrorTrackingHandler()
        root_logger.addHandler(error_handler)
        logger.info("[SUCCESS] Error tracking enabled")  # Removed emoji for Windows compatibility
    except Exception as e:
        logger.error(f"Failed to setup error tracking: {e}")
