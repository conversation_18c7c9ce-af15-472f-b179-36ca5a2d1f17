2025-06-12 18:22:24,844 - signal_handler - INFO - Direct signal processing completed for 25eoV2rjiunpPDeCo4Eiqr3HwCcrPedANhXjYMFRpump
2025-06-12 18:22:24,844 - signal_handler - INFO - Signal forwarded to bot controller: 25eoV2rjiunpPDeCo4Eiqr3HwCcrPedANhXjYMFRpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 18:22:24,845 - signal_handler - INFO - Retrieved signal from queue: 25eoV2rjiunpPDeCo4Eiqr3HwCcrPedANhXjYMFRpump from solana signal alert - gmgn
2025-06-12 18:22:24,846 - signal_handler - INFO - [msg_1749732744845_5602] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:22:24

 Token CA: 25eoV2rjiunpPDeCo4Eiqr3HwCcrPedANhXjYMFRp...
2025-06-12 18:22:24,846 - signal_handler - INFO - [msg_1749732744845_5602] Message queued with normal priority
2025-06-12 18:22:32,593 - signal_handler - INFO - [msg_1749732752593_4350] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW LIQUIDITY] [?] 2025-06-12 18:22:32

 Token: 25eoV2rjiunpPDeCo4Eiqr3HwC...
2025-06-12 18:22:32,594 - signal_handler - INFO - [msg_1749732752593_4350] Message queued with normal priority
2025-06-12 18:22:32,595 - signal_handler - INFO - [msg_1749732744845_5602] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:22:33,114 - signal_handler - INFO - [msg_1749732744845_5602] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:22:33,507 - signal_handler - INFO - [msg_1749732744845_5602] Message sent directly successfully using entity
2025-06-12 18:22:33,507 - signal_handler - INFO - [msg_1749732744845_5602] Message sent successfully via direct send method
2025-06-12 18:22:33,508 - signal_handler - INFO - [msg_1749732744845_5602] Message sent successfully on attempt 1
2025-06-12 18:22:33,508 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 18:22:34,024 - signal_handler - INFO - [msg_1749732752593_4350] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:22:34,089 - signal_handler - INFO - [msg_1749732752593_4350] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:22:34,215 - signal_handler - INFO - [msg_1749732752593_4350] Message sent directly successfully using entity
2025-06-12 18:22:34,216 - signal_handler - INFO - [msg_1749732752593_4350] Message sent successfully via direct send method
2025-06-12 18:22:34,217 - signal_handler - INFO - [msg_1749732752593_4350] Message sent successfully on attempt 1
2025-06-12 18:24:05,448 - signal_handler - INFO - Received message from channel -1002177594166: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump...
2025-06-12 18:24:05,450 - signal_handler - INFO - Setting channel_identifier to 'OxyZen Calls' based on chat_id -1002177594166
2025-06-12 18:24:05,451 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:24:05,452 - signal_handler - INFO - Found contract address with 'pump' suffix: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 18:24:05,453 - signal_handler - INFO - Using 'pump' address as highest priority: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 18:24:05,455 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 18:24:05,456 - signal_handler - INFO - Detected signal from OxyZen Calls for token: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 18:24:05,458 - signal_handler - INFO - Extracted signal: Token=5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump, Source=OxyZen Calls, Metrics Count=1
2025-06-12 18:24:05,459 - signal_handler - INFO - Added signal to queue: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump from OxyZen Calls with confidence 0.5, GMGN channel: False
2025-06-12 18:24:05,460 - signal_handler - INFO - Calling direct signal callback for 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 18:24:05,467 - signal_handler - INFO - Direct signal processing completed for 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 18:24:05,468 - signal_handler - INFO - Signal forwarded to bot controller: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump from OxyZen Calls (confidence: 0.50)
2025-06-12 18:24:05,469 - signal_handler - INFO - Retrieved signal from queue: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump from OxyZen Calls
2025-06-12 18:24:05,475 - signal_handler - INFO - [msg_1749732845474_8522] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:24:05

 Token CA: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6p...
2025-06-12 18:24:05,476 - signal_handler - INFO - [msg_1749732845474_8522] Message queued with normal priority
2025-06-12 18:24:08,619 - signal_handler - INFO - [msg_1749732845474_8522] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:24:08,692 - signal_handler - INFO - [msg_1749732845474_8522] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:24:08,790 - signal_handler - INFO - [msg_1749732845474_8522] Message sent directly successfully using entity
2025-06-12 18:24:08,791 - signal_handler - INFO - [msg_1749732845474_8522] Message sent successfully via direct send method
2025-06-12 18:24:08,792 - signal_handler - INFO - [msg_1749732845474_8522] Message sent successfully on attempt 1
2025-06-12 18:24:08,987 - signal_handler - INFO - [msg_1749732848984_7272] [SELL NOTIFICATION]: [GREEN] [SIM BUY EXECUTED] [?] 2025-06-12 18:24:08

 Signal Source: OxyZen Calls
 Token: 5Fxk572o7Jg...
2025-06-12 18:24:08,991 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749732848984_7272.txt
2025-06-12 18:24:08,992 - signal_handler - INFO - [msg_1749732848984_7272] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:24:09,054 - signal_handler - INFO - [msg_1749732848984_7272] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:24:09,233 - signal_handler - INFO - [msg_1749732848984_7272] Message sent directly successfully using entity
2025-06-12 18:24:09,233 - signal_handler - INFO - [msg_1749732848984_7272] Message sent directly successfully
2025-06-12 18:24:09,234 - signal_handler - INFO - [msg_1749732849234_2778] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (SIMULATION) - 5FXK572O

 Timestamp: 2025-06-12 18:24:09
[REPEAT] Tr...
2025-06-12 18:24:09,235 - signal_handler - INFO - [msg_1749732849234_2778] Message queued with normal priority
2025-06-12 18:24:09,237 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.06s (interval: 0.50s)
2025-06-12 18:24:09,295 - signal_handler - INFO - [msg_1749732849234_2778] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:24:09,355 - signal_handler - INFO - [msg_1749732849234_2778] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:24:09,453 - signal_handler - INFO - [msg_1749732849234_2778] Message sent directly successfully using entity
2025-06-12 18:24:09,454 - signal_handler - INFO - [msg_1749732849234_2778] Message sent successfully via direct send method
2025-06-12 18:24:09,455 - signal_handler - INFO - [msg_1749732849234_2778] Message sent successfully on attempt 1
2025-06-12 18:25:12,249 - signal_handler - INFO - Received sell notification: [PURPLE] [SIM SELL [?] STOP LOSS HIT (-26.9% <= -20.0%)] [?] 2025-06-12 18:25:12
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]...
2025-06-12 18:25:12,252 - signal_handler - INFO - [msg_1749732912249_8451] [SELL NOTIFICATION]:  [SIM SELL [?] STOP LOSS HIT (-26.9% <= -20.0%)] [?] 2025-06-12 18:25:12

 Triggered by: OxyZen Call...
2025-06-12 18:25:12,256 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749732912249_8451.txt
2025-06-12 18:25:12,258 - signal_handler - INFO - [msg_1749732912249_8451] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:25:12,349 - signal_handler - INFO - [msg_1749732912249_8451] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:25:12,506 - signal_handler - INFO - [msg_1749732912249_8451] Message sent directly successfully using entity
2025-06-12 18:25:12,507 - signal_handler - INFO - [msg_1749732912249_8451] Message sent directly successfully
2025-06-12 18:25:12,510 - signal_handler - INFO - [msg_1749732912510_6177] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-12 18:25:12
[REPEAT] Trade: SIMULATION

[MONEY...
2025-06-12 18:25:12,512 - signal_handler - INFO - [msg_1749732912510_6177] Message queued with normal priority
2025-06-12 18:25:12,584 - signal_handler - INFO - [msg_1749732912510_6177] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:25:12,664 - signal_handler - INFO - [msg_1749732912510_6177] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:25:12,779 - signal_handler - INFO - [msg_1749732912510_6177] Message sent directly successfully using entity
2025-06-12 18:25:12,780 - signal_handler - INFO - [msg_1749732912510_6177] Message sent successfully via direct send method
2025-06-12 18:25:12,781 - signal_handler - INFO - [msg_1749732912510_6177] Message sent successfully on attempt 1
2025-06-12 18:25:45,877 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$12.7K(+259.4%)**
*...
2025-06-12 18:25:45,879 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 18:25:45,880 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 18:25:45,881 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 18:25:45,882 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:25:45,883 - signal_handler - INFO - Found contract address with 'pump' suffix: CWZUzGUhuFjrkxwZTTfsKRDjWYp8VH77UvMFLh7Xpump
2025-06-12 18:25:45,885 - signal_handler - INFO - Found contract address: yUu9K1DvWnqk5ymEBh6Aeud9JPEbP61Ua7hmp6ko5XN
2025-06-12 18:25:45,886 - signal_handler - INFO - Using 'pump' address as highest priority: CWZUzGUhuFjrkxwZTTfsKRDjWYp8VH77UvMFLh7Xpump
2025-06-12 18:25:45,886 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: CWZUzGUhuFjrkxwZTTfsKRDjWYp8VH77UvMFLh7Xpump
2025-06-12 18:25:45,888 - signal_handler - INFO - Detected GMGN format
2025-06-12 18:25:45,889 - signal_handler - INFO - Found token symbol: 12
2025-06-12 18:25:45,889 - signal_handler - INFO - Found FDV: 12.7K - 12.70 (+259.4%)
2025-06-12 18:25:45,890 - signal_handler - INFO - Detected GMGN channel signal for token: CWZUzGUhuFjrkxwZTTfsKRDjWYp8VH77UvMFLh7Xpump
2025-06-12 18:25:45,890 - signal_handler - INFO - Extracted signal: Token=CWZUzGUhuFjrkxwZTTfsKRDjWYp8VH77UvMFLh7Xpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 18:25:45,891 - signal_handler - INFO - Added signal to queue: CWZUzGUhuFjrkxwZTTfsKRDjWYp8VH77UvMFLh7Xpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 18:25:45,891 - signal_handler - INFO - Calling direct signal callback for CWZUzGUhuFjrkxwZTTfsKRDjWYp8VH77UvMFLh7Xpump
2025-06-12 18:25:45,895 - signal_handler - INFO - Direct signal processing completed for CWZUzGUhuFjrkxwZTTfsKRDjWYp8VH77UvMFLh7Xpump
2025-06-12 18:25:45,896 - signal_handler - INFO - Signal forwarded to bot controller: CWZUzGUhuFjrkxwZTTfsKRDjWYp8VH77UvMFLh7Xpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 18:25:45,897 - signal_handler - INFO - Retrieved signal from queue: CWZUzGUhuFjrkxwZTTfsKRDjWYp8VH77UvMFLh7Xpump from solana signal alert - gmgn
2025-06-12 18:25:45,899 - signal_handler - INFO - [msg_1749732945898_7288] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:25:45

 Token CA: CWZUzGUhuFjrkxwZTTfsKRDjWYp8VH77UvMFLh7Xp...
2025-06-12 18:25:45,900 - signal_handler - INFO - [msg_1749732945898_7288] Message queued with normal priority
2025-06-12 18:25:53,109 - signal_handler - INFO - [msg_1749732953108_2023] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] HIGH WHALE RISK] [?] 2025-06-12 18:25:53

 Token: CWZUzGUhuFjrkxwZTTfsKRDj...
2025-06-12 18:25:53,109 - signal_handler - INFO - [msg_1749732953108_2023] Message queued with normal priority
2025-06-12 18:25:53,110 - signal_handler - INFO - [msg_1749732945898_7288] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:25:53,319 - signal_handler - INFO - [msg_1749732945898_7288] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:25:53,420 - signal_handler - INFO - [msg_1749732945898_7288] Message sent directly successfully using entity
2025-06-12 18:25:53,421 - signal_handler - INFO - [msg_1749732945898_7288] Message sent successfully via direct send method
2025-06-12 18:25:53,422 - signal_handler - INFO - [msg_1749732945898_7288] Message sent successfully on attempt 1
2025-06-12 18:25:53,423 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 18:25:53,928 - signal_handler - INFO - [msg_1749732953108_2023] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:25:54,025 - signal_handler - INFO - [msg_1749732953108_2023] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:25:54,145 - signal_handler - INFO - [msg_1749732953108_2023] Message sent directly successfully using entity
2025-06-12 18:25:54,147 - signal_handler - INFO - [msg_1749732953108_2023] Message sent successfully via direct send method
2025-06-12 18:25:54,148 - signal_handler - INFO - [msg_1749732953108_2023] Message sent successfully on attempt 1
2025-06-12 18:27:14,558 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$9374.3888(+202.2%)...
2025-06-12 18:27:14,560 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 18:27:14,561 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 18:27:14,561 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 18:27:14,562 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:27:14,564 - signal_handler - INFO - Found contract address with 'pump' suffix: 5zmc4nEL43mtmSguNcpuhaUhn1LyFsXs45VRdhf4pump
2025-06-12 18:27:14,565 - signal_handler - INFO - Found contract address: BjvdSrcuf9M6VMGVcgRwqQCAHKSq43fuW1j2LbNUC7AB
2025-06-12 18:27:14,566 - signal_handler - INFO - Using 'pump' address as highest priority: 5zmc4nEL43mtmSguNcpuhaUhn1LyFsXs45VRdhf4pump
2025-06-12 18:27:14,567 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 5zmc4nEL43mtmSguNcpuhaUhn1LyFsXs45VRdhf4pump
2025-06-12 18:27:14,569 - signal_handler - INFO - Detected GMGN format
2025-06-12 18:27:14,570 - signal_handler - INFO - Found token symbol: 9374
2025-06-12 18:27:14,570 - signal_handler - INFO - Found FDV: 9374.3888 - 9.37K (+202.2%)
2025-06-12 18:27:14,571 - signal_handler - INFO - Detected GMGN channel signal for token: 5zmc4nEL43mtmSguNcpuhaUhn1LyFsXs45VRdhf4pump
2025-06-12 18:27:14,571 - signal_handler - INFO - Extracted signal: Token=5zmc4nEL43mtmSguNcpuhaUhn1LyFsXs45VRdhf4pump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 18:27:14,572 - signal_handler - INFO - Added signal to queue: 5zmc4nEL43mtmSguNcpuhaUhn1LyFsXs45VRdhf4pump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 18:27:14,572 - signal_handler - INFO - Calling direct signal callback for 5zmc4nEL43mtmSguNcpuhaUhn1LyFsXs45VRdhf4pump
2025-06-12 18:27:14,576 - signal_handler - INFO - Direct signal processing completed for 5zmc4nEL43mtmSguNcpuhaUhn1LyFsXs45VRdhf4pump
2025-06-12 18:27:14,576 - signal_handler - INFO - Signal forwarded to bot controller: 5zmc4nEL43mtmSguNcpuhaUhn1LyFsXs45VRdhf4pump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 18:27:14,577 - signal_handler - INFO - Retrieved signal from queue: 5zmc4nEL43mtmSguNcpuhaUhn1LyFsXs45VRdhf4pump from solana signal alert - gmgn
2025-06-12 18:27:14,578 - signal_handler - INFO - [msg_1749733034578_6898] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:27:14

 Token CA: 5zmc4nEL43mtmSguNcpuhaUhn1LyFsXs45VRdhf4p...
2025-06-12 18:27:14,579 - signal_handler - INFO - [msg_1749733034578_6898] Message queued with normal priority
2025-06-12 18:27:21,559 - signal_handler - INFO - [msg_1749733041559_7854] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW LIQUIDITY] [?] 2025-06-12 18:27:21

 Token: 5zmc4nEL43mtmSguNcpuhaUhn1...
2025-06-12 18:27:21,560 - signal_handler - INFO - [msg_1749733041559_7854] Message queued with normal priority
2025-06-12 18:27:21,561 - signal_handler - INFO - [msg_1749733034578_6898] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:27:21,618 - signal_handler - INFO - [msg_1749733034578_6898] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:27:21,732 - signal_handler - INFO - [msg_1749733034578_6898] Message sent directly successfully using entity
2025-06-12 18:27:21,733 - signal_handler - INFO - [msg_1749733034578_6898] Message sent successfully via direct send method
2025-06-12 18:27:21,734 - signal_handler - INFO - [msg_1749733034578_6898] Message sent successfully on attempt 1
2025-06-12 18:27:21,735 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 18:27:22,245 - signal_handler - INFO - [msg_1749733041559_7854] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:27:22,308 - signal_handler - INFO - [msg_1749733041559_7854] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:27:22,438 - signal_handler - INFO - [msg_1749733041559_7854] Message sent directly successfully using entity
2025-06-12 18:27:22,439 - signal_handler - INFO - [msg_1749733041559_7854] Message sent successfully via direct send method
2025-06-12 18:27:22,440 - signal_handler - INFO - [msg_1749733041559_7854] Message sent successfully on attempt 1
2025-06-12 18:28:56,737 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$9651.8795(+210.7%)...
2025-06-12 18:28:56,741 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 18:28:56,742 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 18:28:56,743 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 18:28:56,744 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:28:56,746 - signal_handler - INFO - Found contract address with 'pump' suffix: 4Z68obT8JnRSwS8YcV2HSxV7Dk598xCTFV9EZx6vpump
2025-06-12 18:28:56,747 - signal_handler - INFO - Found contract address: J7PeVDSQ5cLJ98HpqrCL45XBmtjaCoqBS6XKtZLMKMaV
2025-06-12 18:28:56,749 - signal_handler - INFO - Using 'pump' address as highest priority: 4Z68obT8JnRSwS8YcV2HSxV7Dk598xCTFV9EZx6vpump
2025-06-12 18:28:56,749 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 4Z68obT8JnRSwS8YcV2HSxV7Dk598xCTFV9EZx6vpump
2025-06-12 18:28:56,750 - signal_handler - INFO - Detected GMGN format
2025-06-12 18:28:56,750 - signal_handler - INFO - Found token symbol: 9651
2025-06-12 18:28:56,751 - signal_handler - INFO - Found FDV: 9651.8795 - 9.65K (+210.7%)
2025-06-12 18:28:56,751 - signal_handler - INFO - Detected GMGN channel signal for token: 4Z68obT8JnRSwS8YcV2HSxV7Dk598xCTFV9EZx6vpump
2025-06-12 18:28:56,752 - signal_handler - INFO - Extracted signal: Token=4Z68obT8JnRSwS8YcV2HSxV7Dk598xCTFV9EZx6vpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 18:28:56,753 - signal_handler - INFO - Added signal to queue: 4Z68obT8JnRSwS8YcV2HSxV7Dk598xCTFV9EZx6vpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 18:28:56,753 - signal_handler - INFO - Calling direct signal callback for 4Z68obT8JnRSwS8YcV2HSxV7Dk598xCTFV9EZx6vpump
2025-06-12 18:28:56,757 - signal_handler - INFO - Direct signal processing completed for 4Z68obT8JnRSwS8YcV2HSxV7Dk598xCTFV9EZx6vpump
2025-06-12 18:28:56,757 - signal_handler - INFO - Signal forwarded to bot controller: 4Z68obT8JnRSwS8YcV2HSxV7Dk598xCTFV9EZx6vpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 18:28:56,758 - signal_handler - INFO - Retrieved signal from queue: 4Z68obT8JnRSwS8YcV2HSxV7Dk598xCTFV9EZx6vpump from solana signal alert - gmgn
2025-06-12 18:28:56,760 - signal_handler - INFO - [msg_1749733136759_7597] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:28:56

 Token CA: 4Z68obT8JnRSwS8YcV2HSxV7Dk598xCTFV9EZx6vp...
2025-06-12 18:28:56,760 - signal_handler - INFO - [msg_1749733136759_7597] Message queued with normal priority
2025-06-12 18:29:03,870 - signal_handler - INFO - [msg_1749733143870_3956] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW LIQUIDITY] [?] 2025-06-12 18:29:03

 Token: 4Z68obT8JnRSwS8YcV2HSxV7Dk...
2025-06-12 18:29:03,871 - signal_handler - INFO - [msg_1749733143870_3956] Message queued with normal priority
2025-06-12 18:29:03,872 - signal_handler - INFO - [msg_1749733136759_7597] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:29:03,930 - signal_handler - INFO - [msg_1749733136759_7597] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:29:04,042 - signal_handler - INFO - [msg_1749733136759_7597] Message sent directly successfully using entity
2025-06-12 18:29:04,043 - signal_handler - INFO - [msg_1749733136759_7597] Message sent successfully via direct send method
2025-06-12 18:29:04,044 - signal_handler - INFO - [msg_1749733136759_7597] Message sent successfully on attempt 1
2025-06-12 18:29:04,044 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 18:29:04,561 - signal_handler - INFO - [msg_1749733143870_3956] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:29:04,619 - signal_handler - INFO - [msg_1749733143870_3956] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:29:04,705 - signal_handler - INFO - [msg_1749733143870_3956] Message sent directly successfully using entity
2025-06-12 18:29:04,705 - signal_handler - INFO - [msg_1749733143870_3956] Message sent successfully via direct send method
2025-06-12 18:29:04,706 - signal_handler - INFO - [msg_1749733143870_3956] Message sent successfully on attempt 1
2025-06-12 18:30:42,881 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$10.3K(+213.2%)**
*...
2025-06-12 18:30:42,883 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 18:30:42,884 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 18:30:42,885 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 18:30:42,886 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:30:42,887 - signal_handler - INFO - Found contract address with 'pump' suffix: 22JFQSHJfJejb46soKBn1F5ioXXsEPffwZs8LmpQpump
2025-06-12 18:30:42,889 - signal_handler - INFO - Found contract address: 8oNAGdy8P1GNYzK8YSW2V9umPABTzCSWrPXBRvTFctNq
2025-06-12 18:30:42,889 - signal_handler - INFO - Using 'pump' address as highest priority: 22JFQSHJfJejb46soKBn1F5ioXXsEPffwZs8LmpQpump
2025-06-12 18:30:42,890 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 22JFQSHJfJejb46soKBn1F5ioXXsEPffwZs8LmpQpump
2025-06-12 18:30:42,891 - signal_handler - INFO - Detected GMGN format
2025-06-12 18:30:42,892 - signal_handler - INFO - Found token symbol: 10
2025-06-12 18:30:42,892 - signal_handler - INFO - Found FDV: 10.3K - 10.30 (+213.2%)
2025-06-12 18:30:42,893 - signal_handler - INFO - Detected GMGN channel signal for token: 22JFQSHJfJejb46soKBn1F5ioXXsEPffwZs8LmpQpump
2025-06-12 18:30:42,893 - signal_handler - INFO - Extracted signal: Token=22JFQSHJfJejb46soKBn1F5ioXXsEPffwZs8LmpQpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 18:30:42,894 - signal_handler - INFO - Added signal to queue: 22JFQSHJfJejb46soKBn1F5ioXXsEPffwZs8LmpQpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 18:30:42,894 - signal_handler - INFO - Calling direct signal callback for 22JFQSHJfJejb46soKBn1F5ioXXsEPffwZs8LmpQpump
2025-06-12 18:30:42,897 - signal_handler - INFO - Direct signal processing completed for 22JFQSHJfJejb46soKBn1F5ioXXsEPffwZs8LmpQpump
2025-06-12 18:30:42,898 - signal_handler - INFO - Signal forwarded to bot controller: 22JFQSHJfJejb46soKBn1F5ioXXsEPffwZs8LmpQpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 18:30:42,899 - signal_handler - INFO - Retrieved signal from queue: 22JFQSHJfJejb46soKBn1F5ioXXsEPffwZs8LmpQpump from solana signal alert - gmgn
2025-06-12 18:30:42,900 - signal_handler - INFO - [msg_1749733242900_6075] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:30:42

 Token CA: 22JFQSHJfJejb46soKBn1F5ioXXsEPffwZs8LmpQp...
2025-06-12 18:30:42,901 - signal_handler - INFO - [msg_1749733242900_6075] Message queued with normal priority
2025-06-12 18:30:50,108 - signal_handler - INFO - [msg_1749733250108_7523] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW LIQUIDITY] [?] 2025-06-12 18:30:50

 Token: 22JFQSHJfJejb46soKBn1F5ioX...
2025-06-12 18:30:50,108 - signal_handler - INFO - [msg_1749733250108_7523] Message queued with normal priority
2025-06-12 18:30:50,109 - signal_handler - INFO - [msg_1749733242900_6075] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:30:50,173 - signal_handler - INFO - [msg_1749733242900_6075] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:30:50,349 - signal_handler - INFO - [msg_1749733242900_6075] Message sent directly successfully using entity
2025-06-12 18:30:50,350 - signal_handler - INFO - [msg_1749733242900_6075] Message sent successfully via direct send method
2025-06-12 18:30:50,351 - signal_handler - INFO - [msg_1749733242900_6075] Message sent successfully on attempt 1
2025-06-12 18:30:50,352 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 18:30:50,858 - signal_handler - INFO - [msg_1749733250108_7523] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:30:50,917 - signal_handler - INFO - [msg_1749733250108_7523] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:30:51,024 - signal_handler - INFO - [msg_1749733250108_7523] Message sent directly successfully using entity
2025-06-12 18:30:51,025 - signal_handler - INFO - [msg_1749733250108_7523] Message sent successfully via direct send method
2025-06-12 18:30:51,026 - signal_handler - INFO - [msg_1749733250108_7523] Message sent successfully on attempt 1
2025-06-12 18:31:46,098 - signal_handler - INFO - Received message from channel -1002177594166:  **Achievement Unlocked**: x3! 

@Zen_call made a **x3+** call on [Catulu ](https://t.me/spydefi_bot...
2025-06-12 18:31:46,103 - signal_handler - INFO - Setting channel_identifier to 'OxyZen Calls' based on chat_id -1002177594166
2025-06-12 18:31:46,104 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:31:46,104 - signal_handler - INFO - Found contract address with 'pump' suffix: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 18:31:46,105 - signal_handler - INFO - Using 'pump' address as highest priority: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 18:31:46,105 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 18:31:46,106 - signal_handler - INFO - Detected signal from OxyZen Calls for token: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 18:31:46,106 - signal_handler - INFO - Extracted signal: Token=5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump, Source=OxyZen Calls, Metrics Count=1
2025-06-12 18:31:46,107 - signal_handler - INFO - Using shorter cooldown (1s) for pump token: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 18:31:46,107 - signal_handler - INFO - Token 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump cooldown expired. Processing signal.
2025-06-12 18:31:46,107 - signal_handler - INFO - Added signal to queue: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump from OxyZen Calls with confidence 0.5, GMGN channel: False
2025-06-12 18:31:46,108 - signal_handler - INFO - Calling direct signal callback for 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 18:31:46,111 - signal_handler - INFO - Direct signal processing completed for 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 18:31:46,111 - signal_handler - INFO - Signal forwarded to bot controller: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump from OxyZen Calls (confidence: 0.50)
2025-06-12 18:31:46,112 - signal_handler - INFO - Retrieved signal from queue: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump from OxyZen Calls
2025-06-12 18:31:46,113 - signal_handler - INFO - [msg_1749733306113_2964] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:31:46

 Token CA: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6p...
2025-06-12 18:31:46,114 - signal_handler - INFO - [msg_1749733306113_2964] Message queued with normal priority
2025-06-12 18:31:46,155 - signal_handler - INFO - [msg_1749733306113_2964] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:31:46,209 - signal_handler - INFO - [msg_1749733306113_2964] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:31:46,320 - signal_handler - INFO - [msg_1749733306113_2964] Message sent directly successfully using entity
2025-06-12 18:31:46,321 - signal_handler - INFO - [msg_1749733306113_2964] Message sent successfully via direct send method
2025-06-12 18:31:46,322 - signal_handler - INFO - [msg_1749733306113_2964] Message sent successfully on attempt 1
2025-06-12 18:32:51,271 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$12.2K(+266.6%)**
*...
2025-06-12 18:32:51,272 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 18:32:51,273 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 18:32:51,274 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 18:32:51,274 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:32:51,276 - signal_handler - INFO - Found contract address with 'pump' suffix: 2NKHpnCKhkrnVpciyeTbvDV6UoXZCjQKhz15Hns4pump
2025-06-12 18:32:51,277 - signal_handler - INFO - Found contract address: ChLnQ7WPaDNejo7Yd5Mp9f6p8Axc8x5yzGzpDv7s8jPT
2025-06-12 18:32:51,278 - signal_handler - INFO - Using 'pump' address as highest priority: 2NKHpnCKhkrnVpciyeTbvDV6UoXZCjQKhz15Hns4pump
2025-06-12 18:32:51,279 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 2NKHpnCKhkrnVpciyeTbvDV6UoXZCjQKhz15Hns4pump
2025-06-12 18:32:51,281 - signal_handler - INFO - Detected GMGN format
2025-06-12 18:32:51,281 - signal_handler - INFO - Found token symbol: 12
2025-06-12 18:32:51,282 - signal_handler - INFO - Found FDV: 12.2K - 12.20 (+266.6%)
2025-06-12 18:32:51,283 - signal_handler - INFO - Detected GMGN channel signal for token: 2NKHpnCKhkrnVpciyeTbvDV6UoXZCjQKhz15Hns4pump
2025-06-12 18:32:51,285 - signal_handler - INFO - Extracted signal: Token=2NKHpnCKhkrnVpciyeTbvDV6UoXZCjQKhz15Hns4pump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 18:32:51,286 - signal_handler - INFO - Signal processing health: 8 received, 8 processed, 0 dropped (100.0% success rate)
2025-06-12 18:32:51,287 - signal_handler - INFO - Added signal to queue: 2NKHpnCKhkrnVpciyeTbvDV6UoXZCjQKhz15Hns4pump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 18:32:51,288 - signal_handler - INFO - Calling direct signal callback for 2NKHpnCKhkrnVpciyeTbvDV6UoXZCjQKhz15Hns4pump
2025-06-12 18:32:51,292 - signal_handler - INFO - Direct signal processing completed for 2NKHpnCKhkrnVpciyeTbvDV6UoXZCjQKhz15Hns4pump
2025-06-12 18:32:51,292 - signal_handler - INFO - Signal forwarded to bot controller: 2NKHpnCKhkrnVpciyeTbvDV6UoXZCjQKhz15Hns4pump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 18:32:51,293 - signal_handler - INFO - Retrieved signal from queue: 2NKHpnCKhkrnVpciyeTbvDV6UoXZCjQKhz15Hns4pump from solana signal alert - gmgn
2025-06-12 18:32:51,295 - signal_handler - INFO - [msg_1749733371295_6965] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:32:51

 Token CA: 2NKHpnCKhkrnVpciyeTbvDV6UoXZCjQKhz15Hns4p...
2025-06-12 18:32:51,295 - signal_handler - INFO - [msg_1749733371295_6965] Message queued with normal priority
2025-06-12 18:32:58,205 - signal_handler - INFO - [msg_1749733371295_6965] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:32:58,374 - signal_handler - INFO - [msg_1749733371295_6965] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:32:58,488 - signal_handler - INFO - [msg_1749733371295_6965] Message sent directly successfully using entity
2025-06-12 18:32:58,489 - signal_handler - INFO - [msg_1749733371295_6965] Message sent successfully via direct send method
2025-06-12 18:32:58,490 - signal_handler - INFO - [msg_1749733371295_6965] Message sent successfully on attempt 1
2025-06-12 18:32:58,700 - signal_handler - INFO - [msg_1749733378698_4728] [SELL NOTIFICATION]: [GREEN] [SIM BUY EXECUTED] [?] 2025-06-12 18:32:58

 Signal Source: solana signal alert - gmgn
 Toke...
2025-06-12 18:32:58,705 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749733378698_4728.txt
2025-06-12 18:32:58,706 - signal_handler - INFO - [msg_1749733378698_4728] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:32:58,764 - signal_handler - INFO - [msg_1749733378698_4728] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:32:58,880 - signal_handler - INFO - [msg_1749733378698_4728] Message sent directly successfully using entity
2025-06-12 18:32:58,880 - signal_handler - INFO - [msg_1749733378698_4728] Message sent directly successfully
2025-06-12 18:32:58,881 - signal_handler - INFO - [msg_1749733378881_3775] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (SIMULATION) - 2NKHPNCK

 Timestamp: 2025-06-12 18:32:58
[REPEAT] Tr...
2025-06-12 18:32:58,882 - signal_handler - INFO - [msg_1749733378881_3775] Message queued with normal priority
2025-06-12 18:32:58,931 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.06s (interval: 0.50s)
2025-06-12 18:32:58,993 - signal_handler - INFO - [msg_1749733378881_3775] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:32:59,045 - signal_handler - INFO - [msg_1749733378881_3775] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:32:59,146 - signal_handler - INFO - [msg_1749733378881_3775] Message sent directly successfully using entity
2025-06-12 18:32:59,147 - signal_handler - INFO - [msg_1749733378881_3775] Message sent successfully via direct send method
2025-06-12 18:32:59,148 - signal_handler - INFO - [msg_1749733378881_3775] Message sent successfully on attempt 1
2025-06-12 18:33:16,440 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$10.2K(+220.6%)**
*...
2025-06-12 18:33:16,441 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 18:33:16,442 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 18:33:16,443 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 18:33:16,444 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:33:16,445 - signal_handler - INFO - Found contract address with 'pump' suffix: 9cE3gTT7K9RRSRUS7uFM88mDp2c65WeNEe8rpL2gpump
2025-06-12 18:33:16,446 - signal_handler - INFO - Found contract address: 6ELo88JW1bcfr7XP7TEc6QWAFGM7nwDGz5G6bmxuM2Hv
2025-06-12 18:33:16,447 - signal_handler - INFO - Using 'pump' address as highest priority: 9cE3gTT7K9RRSRUS7uFM88mDp2c65WeNEe8rpL2gpump
2025-06-12 18:33:16,449 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 9cE3gTT7K9RRSRUS7uFM88mDp2c65WeNEe8rpL2gpump
2025-06-12 18:33:16,450 - signal_handler - INFO - Detected GMGN format
2025-06-12 18:33:16,451 - signal_handler - INFO - Found token symbol: 10
2025-06-12 18:33:16,452 - signal_handler - INFO - Found FDV: 10.2K - 10.20 (+220.6%)
2025-06-12 18:33:16,453 - signal_handler - INFO - Detected GMGN channel signal for token: 9cE3gTT7K9RRSRUS7uFM88mDp2c65WeNEe8rpL2gpump
2025-06-12 18:33:16,454 - signal_handler - INFO - Extracted signal: Token=9cE3gTT7K9RRSRUS7uFM88mDp2c65WeNEe8rpL2gpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 18:33:16,455 - signal_handler - INFO - Added signal to queue: 9cE3gTT7K9RRSRUS7uFM88mDp2c65WeNEe8rpL2gpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 18:33:16,455 - signal_handler - INFO - Calling direct signal callback for 9cE3gTT7K9RRSRUS7uFM88mDp2c65WeNEe8rpL2gpump
2025-06-12 18:33:16,461 - signal_handler - INFO - Direct signal processing completed for 9cE3gTT7K9RRSRUS7uFM88mDp2c65WeNEe8rpL2gpump
2025-06-12 18:33:16,462 - signal_handler - INFO - Signal forwarded to bot controller: 9cE3gTT7K9RRSRUS7uFM88mDp2c65WeNEe8rpL2gpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 18:33:16,462 - signal_handler - INFO - Retrieved signal from queue: 9cE3gTT7K9RRSRUS7uFM88mDp2c65WeNEe8rpL2gpump from solana signal alert - gmgn
2025-06-12 18:33:16,464 - signal_handler - INFO - [msg_1749733396464_7837] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:33:16

 Token CA: 9cE3gTT7K9RRSRUS7uFM88mDp2c65WeNEe8rpL2gp...
2025-06-12 18:33:16,465 - signal_handler - INFO - [msg_1749733396464_7837] Message queued with normal priority
2025-06-12 18:33:23,626 - signal_handler - INFO - [msg_1749733403625_1005] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW LIQUIDITY] [?] 2025-06-12 18:33:23

 Token: 9cE3gTT7K9RRSRUS7uFM88mDp2...
2025-06-12 18:33:23,627 - signal_handler - INFO - [msg_1749733403625_1005] Message queued with normal priority
2025-06-12 18:33:23,628 - signal_handler - INFO - [msg_1749733396464_7837] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:33:24,273 - signal_handler - INFO - [msg_1749733396464_7837] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:33:24,382 - signal_handler - INFO - [msg_1749733396464_7837] Message sent directly successfully using entity
2025-06-12 18:33:24,383 - signal_handler - INFO - [msg_1749733396464_7837] Message sent successfully via direct send method
2025-06-12 18:33:24,384 - signal_handler - INFO - [msg_1749733396464_7837] Message sent successfully on attempt 1
2025-06-12 18:33:24,385 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 18:33:30,193 - signal_handler - INFO - Received sell notification: [PURPLE] [SIM SELL [?] TP1 HIT (26.6% >= 20.0%)] [?] 2025-06-12 18:33:30
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]
[?] Trig...
2025-06-12 18:33:30,197 - signal_handler - INFO - [msg_1749733410193_1896] [SELL NOTIFICATION]:  [SIM SELL [?] TP1 HIT (26.6% >= 20.0%)] [?] 2025-06-12 18:33:30

 Triggered by: solana signal alert...
2025-06-12 18:33:30,201 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749733410193_1896.txt
2025-06-12 18:33:30,202 - signal_handler - INFO - [msg_1749733410193_1896] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:33:30,205 - signal_handler - INFO - [msg_1749733403625_1005] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:33:30,289 - signal_handler - INFO - [msg_1749733410193_1896] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:33:30,308 - signal_handler - INFO - [msg_1749733403625_1005] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:33:30,454 - signal_handler - INFO - [msg_1749733410193_1896] Message sent directly successfully using entity
2025-06-12 18:33:30,455 - signal_handler - INFO - [msg_1749733410193_1896] Message sent directly successfully
2025-06-12 18:33:30,457 - signal_handler - INFO - [msg_1749733410457_4685] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-12 18:33:30
[REPEAT] Trade: SIMULATION

[MONEY...
2025-06-12 18:33:30,459 - signal_handler - INFO - [msg_1749733410457_4685] Message queued with normal priority
2025-06-12 18:33:30,496 - signal_handler - INFO - [msg_1749733403625_1005] Message sent directly successfully using entity
2025-06-12 18:33:30,497 - signal_handler - INFO - [msg_1749733403625_1005] Message sent successfully via direct send method
2025-06-12 18:33:30,498 - signal_handler - INFO - [msg_1749733403625_1005] Message sent successfully on attempt 1
2025-06-12 18:33:30,499 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 18:33:31,007 - signal_handler - INFO - [msg_1749733410457_4685] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:33:31,132 - signal_handler - INFO - [msg_1749733410457_4685] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:33:31,248 - signal_handler - INFO - [msg_1749733410457_4685] Message sent directly successfully using entity
2025-06-12 18:33:31,249 - signal_handler - INFO - [msg_1749733410457_4685] Message sent successfully via direct send method
2025-06-12 18:33:31,249 - signal_handler - INFO - [msg_1749733410457_4685] Message sent successfully on attempt 1
2025-06-12 18:35:01,067 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$11.5K(+245.9%)**
*...
2025-06-12 18:35:01,069 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 18:35:01,069 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 18:35:01,070 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 18:35:01,070 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:35:01,071 - signal_handler - INFO - Found contract address with 'pump' suffix: J6mF9rMmUFj2rAaRSmq4ZeaxFakqTXE6YJwmbisvpump
2025-06-12 18:35:01,071 - signal_handler - INFO - Found contract address: exDgswf1PdHgdcs3Jros5gEYjWCaxGP1ZES49J1XH7V
2025-06-12 18:35:01,071 - signal_handler - INFO - Using 'pump' address as highest priority: J6mF9rMmUFj2rAaRSmq4ZeaxFakqTXE6YJwmbisvpump
2025-06-12 18:35:01,072 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: J6mF9rMmUFj2rAaRSmq4ZeaxFakqTXE6YJwmbisvpump
2025-06-12 18:35:01,073 - signal_handler - INFO - Detected GMGN format
2025-06-12 18:35:01,073 - signal_handler - INFO - Found token symbol: 11
2025-06-12 18:35:01,073 - signal_handler - INFO - Found FDV: 11.5K - 11.50 (+245.9%)
2025-06-12 18:35:01,074 - signal_handler - INFO - Detected GMGN channel signal for token: J6mF9rMmUFj2rAaRSmq4ZeaxFakqTXE6YJwmbisvpump
2025-06-12 18:35:01,074 - signal_handler - INFO - Extracted signal: Token=J6mF9rMmUFj2rAaRSmq4ZeaxFakqTXE6YJwmbisvpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 18:35:01,074 - signal_handler - INFO - Added signal to queue: J6mF9rMmUFj2rAaRSmq4ZeaxFakqTXE6YJwmbisvpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 18:35:01,075 - signal_handler - INFO - Calling direct signal callback for J6mF9rMmUFj2rAaRSmq4ZeaxFakqTXE6YJwmbisvpump
2025-06-12 18:35:01,078 - signal_handler - INFO - Direct signal processing completed for J6mF9rMmUFj2rAaRSmq4ZeaxFakqTXE6YJwmbisvpump
2025-06-12 18:35:01,078 - signal_handler - INFO - Signal forwarded to bot controller: J6mF9rMmUFj2rAaRSmq4ZeaxFakqTXE6YJwmbisvpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 18:35:01,079 - signal_handler - INFO - Retrieved signal from queue: J6mF9rMmUFj2rAaRSmq4ZeaxFakqTXE6YJwmbisvpump from solana signal alert - gmgn
2025-06-12 18:35:01,080 - signal_handler - INFO - [msg_1749733501080_3969] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:35:01

 Token CA: J6mF9rMmUFj2rAaRSmq4ZeaxFakqTXE6YJwmbisvp...
2025-06-12 18:35:01,080 - signal_handler - INFO - [msg_1749733501080_3969] Message queued with normal priority
2025-06-12 18:35:07,914 - signal_handler - INFO - [msg_1749733501080_3969] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:35:08,082 - signal_handler - INFO - [msg_1749733501080_3969] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:35:08,186 - signal_handler - INFO - [msg_1749733501080_3969] Message sent directly successfully using entity
2025-06-12 18:35:08,187 - signal_handler - INFO - [msg_1749733501080_3969] Message sent successfully via direct send method
2025-06-12 18:35:08,187 - signal_handler - INFO - [msg_1749733501080_3969] Message sent successfully on attempt 1
2025-06-12 18:35:08,376 - signal_handler - INFO - [msg_1749733508373_8448] [SELL NOTIFICATION]: [GREEN] [SIM BUY EXECUTED] [?] 2025-06-12 18:35:08

 Signal Source: solana signal alert - gmgn
 Toke...
2025-06-12 18:35:08,380 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749733508373_8448.txt
2025-06-12 18:35:08,382 - signal_handler - INFO - [msg_1749733508373_8448] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:35:08,438 - signal_handler - INFO - [msg_1749733508373_8448] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:35:08,552 - signal_handler - INFO - [msg_1749733508373_8448] Message sent directly successfully using entity
2025-06-12 18:35:08,553 - signal_handler - INFO - [msg_1749733508373_8448] Message sent directly successfully
2025-06-12 18:35:08,555 - signal_handler - INFO - [msg_1749733508554_7033] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (SIMULATION) - J6MF9RMM

 Timestamp: 2025-06-12 18:35:08
[REPEAT] Tr...
2025-06-12 18:35:08,557 - signal_handler - INFO - [msg_1749733508554_7033] Message queued with normal priority
2025-06-12 18:35:08,616 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.07s (interval: 0.50s)
2025-06-12 18:35:08,694 - signal_handler - INFO - [msg_1749733508554_7033] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:35:08,794 - signal_handler - INFO - [msg_1749733508554_7033] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:35:08,957 - signal_handler - INFO - [msg_1749733508554_7033] Message sent directly successfully using entity
2025-06-12 18:35:08,958 - signal_handler - INFO - [msg_1749733508554_7033] Message sent successfully via direct send method
2025-06-12 18:35:08,959 - signal_handler - INFO - [msg_1749733508554_7033] Message sent successfully on attempt 1
2025-06-12 18:35:40,433 - signal_handler - INFO - Received sell notification: [PURPLE] [SIM SELL [?] TP1 HIT (51.5% >= 20.0%)] [?] 2025-06-12 18:35:40
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]
[?] Trig...
2025-06-12 18:35:40,504 - signal_handler - INFO - [msg_1749733540433_9691] [SELL NOTIFICATION]:  [SIM SELL [?] TP1 HIT (51.5% >= 20.0%)] [?] 2025-06-12 18:35:40

 Triggered by: solana signal alert...
2025-06-12 18:35:40,538 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749733540433_9691.txt
2025-06-12 18:35:40,545 - signal_handler - INFO - [msg_1749733540433_9691] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:35:40,888 - signal_handler - INFO - [msg_1749733540433_9691] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:35:41,071 - signal_handler - INFO - [msg_1749733540433_9691] Message sent directly successfully using entity
2025-06-12 18:35:41,080 - signal_handler - INFO - [msg_1749733540433_9691] Message sent directly successfully
2025-06-12 18:35:41,091 - signal_handler - INFO - [msg_1749733541091_7105] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-12 18:35:41
[REPEAT] Trade: SIMULATION

[MONEY...
2025-06-12 18:35:41,097 - signal_handler - INFO - [msg_1749733541091_7105] Message queued with normal priority
2025-06-12 18:35:41,140 - signal_handler - INFO - [msg_1749733541091_7105] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:35:41,204 - signal_handler - INFO - [msg_1749733541091_7105] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:35:41,315 - signal_handler - INFO - [msg_1749733541091_7105] Message sent directly successfully using entity
2025-06-12 18:35:41,351 - signal_handler - INFO - [msg_1749733541091_7105] Message sent successfully via direct send method
2025-06-12 18:35:41,352 - signal_handler - INFO - [msg_1749733541091_7105] Message sent successfully on attempt 1
2025-06-12 18:36:26,395 - signal_handler - INFO - Received message from channel -1002177594166:  **Update **  $CATULU**

 95K**  **514K Now 465K

****[DIAMOND][DIAMOND][DIAMOND]************   5X  ...
2025-06-12 18:36:26,397 - signal_handler - INFO - Setting channel_identifier to 'OxyZen Calls' based on chat_id -1002177594166
2025-06-12 18:36:26,398 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:36:26,399 - signal_handler - INFO - No token addresses found in message
2025-06-12 18:36:26,406 - signal_handler - WARNING - Could not extract token address from message: [SEE_NO_EVIL] **Update **[SEE_NO_EVIL]  [COIN]$CATULU**

 95K** [RIGHT_ARROW] **514K Now 465K

****[DIAMOND][DIAMOND][DIAMOND]************   5X   ****[DIAMOND][DIAMOND][DIAMOND]***********... (processing time: 0.001s)
2025-06-12 18:36:26,408 - signal_handler - DEBUG - No token address found in message from OxyZen Calls: [SEE_NO_EVIL] **Update **[SEE_NO_EVIL]  [COIN]$CATULU**

 95K** [RIGHT_ARROW] **514K Now 465K

****[DIAMOND][DIAMOND][DIAMOND]************   5X   ****[DIAMOND][DIAMOND][DIAMOND]***********...
2025-06-12 18:41:32,460 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$10.5K(+222%)**
**[...
2025-06-12 18:41:32,462 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 18:41:32,463 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 18:41:32,464 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 18:41:32,465 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:41:32,466 - signal_handler - INFO - Found contract address with 'pump' suffix: 7hcVLkbR98aMXNRJP4FiQvThoijDxVXG3DKtQHBdpump
2025-06-12 18:41:32,467 - signal_handler - INFO - Found contract address: 8ZSGJ54nzjoSq8zfKU9wPVdsCbff7MfKTMaM7oxCcjBz
2025-06-12 18:41:32,468 - signal_handler - INFO - Using 'pump' address as highest priority: 7hcVLkbR98aMXNRJP4FiQvThoijDxVXG3DKtQHBdpump
2025-06-12 18:41:32,469 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 7hcVLkbR98aMXNRJP4FiQvThoijDxVXG3DKtQHBdpump
2025-06-12 18:41:32,471 - signal_handler - INFO - Detected GMGN format
2025-06-12 18:41:32,472 - signal_handler - INFO - Found token symbol: 10
2025-06-12 18:41:32,472 - signal_handler - INFO - Found FDV: 10.5K - 10.50 (+222%)
2025-06-12 18:41:32,473 - signal_handler - INFO - Detected GMGN channel signal for token: 7hcVLkbR98aMXNRJP4FiQvThoijDxVXG3DKtQHBdpump
2025-06-12 18:41:32,474 - signal_handler - INFO - Extracted signal: Token=7hcVLkbR98aMXNRJP4FiQvThoijDxVXG3DKtQHBdpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 18:41:32,475 - signal_handler - INFO - Added signal to queue: 7hcVLkbR98aMXNRJP4FiQvThoijDxVXG3DKtQHBdpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 18:41:32,476 - signal_handler - INFO - Calling direct signal callback for 7hcVLkbR98aMXNRJP4FiQvThoijDxVXG3DKtQHBdpump
2025-06-12 18:41:32,481 - signal_handler - INFO - Direct signal processing completed for 7hcVLkbR98aMXNRJP4FiQvThoijDxVXG3DKtQHBdpump
2025-06-12 18:41:32,483 - signal_handler - INFO - Signal forwarded to bot controller: 7hcVLkbR98aMXNRJP4FiQvThoijDxVXG3DKtQHBdpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 18:41:32,484 - signal_handler - INFO - Retrieved signal from queue: 7hcVLkbR98aMXNRJP4FiQvThoijDxVXG3DKtQHBdpump from solana signal alert - gmgn
2025-06-12 18:41:32,486 - signal_handler - INFO - [msg_1749733892485_9399] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:41:32

 Token CA: 7hcVLkbR98aMXNRJP4FiQvThoijDxVXG3DKtQHBdp...
2025-06-12 18:41:32,486 - signal_handler - INFO - [msg_1749733892485_9399] Message queued with normal priority
2025-06-12 18:41:39,674 - signal_handler - INFO - [msg_1749733899674_8718] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW LIQUIDITY] [?] 2025-06-12 18:41:39

 Token: 7hcVLkbR98aMXNRJP4FiQvThoi...
2025-06-12 18:41:39,675 - signal_handler - INFO - [msg_1749733899674_8718] Message queued with normal priority
2025-06-12 18:41:39,677 - signal_handler - INFO - [msg_1749733892485_9399] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:41:39,691 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$10.3K(+225%)**
**[...
2025-06-12 18:41:39,692 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 18:41:39,693 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 18:41:39,693 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 18:41:39,694 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:41:39,695 - signal_handler - INFO - Found contract address with 'pump' suffix: 8sMjp7oRWRN7rx***************************ump
2025-06-12 18:41:39,695 - signal_handler - INFO - Found contract address: uAkFxdjr6toAsZ4VCyErh4Kamf9L7CWUFGCRXZQDATi
2025-06-12 18:41:39,696 - signal_handler - INFO - Using 'pump' address as highest priority: 8sMjp7oRWRN7rx***************************ump
2025-06-12 18:41:39,696 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 8sMjp7oRWRN7rx***************************ump
2025-06-12 18:41:39,697 - signal_handler - INFO - Detected GMGN format
2025-06-12 18:41:39,698 - signal_handler - INFO - Found token symbol: 10
2025-06-12 18:41:39,698 - signal_handler - INFO - Found FDV: 10.3K - 10.30 (+225%)
2025-06-12 18:41:39,699 - signal_handler - INFO - Detected GMGN channel signal for token: 8sMjp7oRWRN7rx***************************ump
2025-06-12 18:41:39,699 - signal_handler - INFO - Extracted signal: Token=8sMjp7oRWRN7rx***************************ump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 18:41:39,700 - signal_handler - INFO - Added signal to queue: 8sMjp7oRWRN7rx***************************ump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 18:41:39,700 - signal_handler - INFO - Calling direct signal callback for 8sMjp7oRWRN7rx***************************ump
2025-06-12 18:41:39,704 - signal_handler - INFO - Direct signal processing completed for 8sMjp7oRWRN7rx***************************ump
2025-06-12 18:41:39,705 - signal_handler - INFO - Signal forwarded to bot controller: 8sMjp7oRWRN7rx***************************ump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 18:41:39,705 - signal_handler - INFO - Retrieved signal from queue: 8sMjp7oRWRN7rx***************************ump from solana signal alert - gmgn
2025-06-12 18:41:39,707 - signal_handler - INFO - [msg_1749733899707_6293] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:41:39

 Token CA: 8sMjp7oRWRN7rx***************************...
2025-06-12 18:41:39,708 - signal_handler - INFO - [msg_1749733899707_6293] Message queued with normal priority
2025-06-12 18:41:46,637 - signal_handler - INFO - [msg_1749733892485_9399] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:41:46,734 - signal_handler - INFO - [msg_1749733892485_9399] Message sent directly successfully using entity
2025-06-12 18:41:46,735 - signal_handler - INFO - [msg_1749733892485_9399] Message sent successfully via direct send method
2025-06-12 18:41:46,735 - signal_handler - INFO - [msg_1749733892485_9399] Message sent successfully on attempt 1
2025-06-12 18:41:46,736 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 18:41:46,982 - signal_handler - INFO - [msg_1749733906980_4187] [SELL NOTIFICATION]: [GREEN] [SIM BUY EXECUTED] [?] 2025-06-12 18:41:46

 Signal Source: solana signal alert - gmgn
 Toke...
2025-06-12 18:41:46,987 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749733906980_4187.txt
2025-06-12 18:41:46,988 - signal_handler - INFO - [msg_1749733906980_4187] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:41:47,048 - signal_handler - INFO - [msg_1749733906980_4187] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:41:47,157 - signal_handler - INFO - [msg_1749733906980_4187] Message sent directly successfully using entity
2025-06-12 18:41:47,158 - signal_handler - INFO - [msg_1749733906980_4187] Message sent directly successfully
2025-06-12 18:41:47,158 - signal_handler - INFO - [msg_1749733907158_6840] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (SIMULATION) - 8SMJP7OR

 Timestamp: 2025-06-12 18:41:47
[REPEAT] Tr...
2025-06-12 18:41:47,159 - signal_handler - INFO - [msg_1749733907158_6840] Message queued with normal priority
2025-06-12 18:41:47,240 - signal_handler - INFO - [msg_1749733899674_8718] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:41:47,320 - signal_handler - INFO - [msg_1749733899674_8718] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:41:47,424 - signal_handler - INFO - [msg_1749733899674_8718] Message sent directly successfully using entity
2025-06-12 18:41:47,425 - signal_handler - INFO - [msg_1749733899674_8718] Message sent successfully via direct send method
2025-06-12 18:41:47,426 - signal_handler - INFO - [msg_1749733899674_8718] Message sent successfully on attempt 1
2025-06-12 18:41:47,427 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 18:41:47,935 - signal_handler - INFO - [msg_1749733899707_6293] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:41:47,998 - signal_handler - INFO - [msg_1749733899707_6293] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:41:48,108 - signal_handler - INFO - [msg_1749733899707_6293] Message sent directly successfully using entity
2025-06-12 18:41:48,110 - signal_handler - INFO - [msg_1749733899707_6293] Message sent successfully via direct send method
2025-06-12 18:41:48,110 - signal_handler - INFO - [msg_1749733899707_6293] Message sent successfully on attempt 1
2025-06-12 18:41:48,112 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 18:41:48,614 - signal_handler - INFO - [msg_1749733907158_6840] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:41:48,676 - signal_handler - INFO - [msg_1749733907158_6840] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:41:48,787 - signal_handler - INFO - [msg_1749733907158_6840] Message sent directly successfully using entity
2025-06-12 18:41:48,788 - signal_handler - INFO - [msg_1749733907158_6840] Message sent successfully via direct send method
2025-06-12 18:41:48,789 - signal_handler - INFO - [msg_1749733907158_6840] Message sent successfully on attempt 1
2025-06-12 18:43:25,055 - signal_handler - INFO - Received sell notification: [PURPLE] [SIM SELL [?] TP1 HIT (28.2% >= 20.0%)] [?] 2025-06-12 18:43:25
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]
[?] Trig...
2025-06-12 18:43:25,058 - signal_handler - INFO - [msg_1749734005055_8623] [SELL NOTIFICATION]:  [SIM SELL [?] TP1 HIT (28.2% >= 20.0%)] [?] 2025-06-12 18:43:25

 Triggered by: solana signal alert...
2025-06-12 18:43:25,062 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749734005055_8623.txt
2025-06-12 18:43:25,064 - signal_handler - INFO - [msg_1749734005055_8623] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:43:25,129 - signal_handler - INFO - [msg_1749734005055_8623] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:43:25,287 - signal_handler - INFO - [msg_1749734005055_8623] Message sent directly successfully using entity
2025-06-12 18:43:25,287 - signal_handler - INFO - [msg_1749734005055_8623] Message sent directly successfully
2025-06-12 18:43:25,289 - signal_handler - INFO - [msg_1749734005288_7134] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-12 18:43:25
[REPEAT] Trade: SIMULATION

[MONEY...
2025-06-12 18:43:25,289 - signal_handler - INFO - [msg_1749734005288_7134] Message queued with normal priority
2025-06-12 18:43:25,291 - signal_handler - INFO - [msg_1749734005288_7134] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:43:25,394 - signal_handler - INFO - [msg_1749734005288_7134] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:43:25,560 - signal_handler - INFO - [msg_1749734005288_7134] Message sent directly successfully using entity
2025-06-12 18:43:25,560 - signal_handler - INFO - [msg_1749734005288_7134] Message sent successfully via direct send method
2025-06-12 18:43:25,561 - signal_handler - INFO - [msg_1749734005288_7134] Message sent successfully on attempt 1
2025-06-12 18:44:23,888 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$13.7K(+301.4%)**
*...
2025-06-12 18:44:23,889 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 18:44:23,890 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 18:44:23,891 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 18:44:23,892 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:44:23,893 - signal_handler - INFO - Found contract address with 'pump' suffix: 2HmCeAx1KULhn7GhErHazC8p9wJZ6RpG7Y5cEyt2pump
2025-06-12 18:44:23,895 - signal_handler - INFO - Found contract address: 13Pbk9262xyWUHnYhEQEdCPAz6wVkEZV7beEA3M4UDnU
2025-06-12 18:44:23,896 - signal_handler - INFO - Using 'pump' address as highest priority: 2HmCeAx1KULhn7GhErHazC8p9wJZ6RpG7Y5cEyt2pump
2025-06-12 18:44:23,897 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 2HmCeAx1KULhn7GhErHazC8p9wJZ6RpG7Y5cEyt2pump
2025-06-12 18:44:23,898 - signal_handler - INFO - Detected GMGN format
2025-06-12 18:44:23,899 - signal_handler - INFO - Found token symbol: 13
2025-06-12 18:44:23,900 - signal_handler - INFO - Found FDV: 13.7K - 13.70 (+301.4%)
2025-06-12 18:44:23,900 - signal_handler - INFO - Detected GMGN channel signal for token: 2HmCeAx1KULhn7GhErHazC8p9wJZ6RpG7Y5cEyt2pump
2025-06-12 18:44:23,901 - signal_handler - INFO - Extracted signal: Token=2HmCeAx1KULhn7GhErHazC8p9wJZ6RpG7Y5cEyt2pump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 18:44:23,901 - signal_handler - INFO - Signal processing health: 13 received, 13 processed, 0 dropped (100.0% success rate)
2025-06-12 18:44:23,902 - signal_handler - INFO - Added signal to queue: 2HmCeAx1KULhn7GhErHazC8p9wJZ6RpG7Y5cEyt2pump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 18:44:23,902 - signal_handler - INFO - Calling direct signal callback for 2HmCeAx1KULhn7GhErHazC8p9wJZ6RpG7Y5cEyt2pump
2025-06-12 18:44:23,906 - signal_handler - INFO - Direct signal processing completed for 2HmCeAx1KULhn7GhErHazC8p9wJZ6RpG7Y5cEyt2pump
2025-06-12 18:44:23,907 - signal_handler - INFO - Signal forwarded to bot controller: 2HmCeAx1KULhn7GhErHazC8p9wJZ6RpG7Y5cEyt2pump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 18:44:23,907 - signal_handler - INFO - Retrieved signal from queue: 2HmCeAx1KULhn7GhErHazC8p9wJZ6RpG7Y5cEyt2pump from solana signal alert - gmgn
2025-06-12 18:44:23,909 - signal_handler - INFO - [msg_1749734063909_6419] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:44:23

 Token CA: 2HmCeAx1KULhn7GhErHazC8p9wJZ6RpG7Y5cEyt2p...
2025-06-12 18:44:23,910 - signal_handler - INFO - [msg_1749734063909_6419] Message queued with normal priority
2025-06-12 18:44:31,018 - signal_handler - INFO - [msg_1749734063909_6419] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:44:31,156 - signal_handler - INFO - [msg_1749734063909_6419] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:44:31,252 - signal_handler - INFO - [msg_1749734063909_6419] Message sent directly successfully using entity
2025-06-12 18:44:31,252 - signal_handler - INFO - [msg_1749734063909_6419] Message sent successfully via direct send method
2025-06-12 18:44:31,252 - signal_handler - INFO - [msg_1749734063909_6419] Message sent successfully on attempt 1
2025-06-12 18:44:32,151 - signal_handler - INFO - [msg_1749734072148_1804] [SELL NOTIFICATION]: [GREEN] [SIM BUY EXECUTED] [?] 2025-06-12 18:44:32

 Signal Source: solana signal alert - gmgn
 Toke...
2025-06-12 18:44:32,155 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749734072148_1804.txt
2025-06-12 18:44:32,157 - signal_handler - INFO - [msg_1749734072148_1804] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:44:32,215 - signal_handler - INFO - [msg_1749734072148_1804] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:44:32,335 - signal_handler - INFO - [msg_1749734072148_1804] Message sent directly successfully using entity
2025-06-12 18:44:32,336 - signal_handler - INFO - [msg_1749734072148_1804] Message sent directly successfully
2025-06-12 18:44:32,337 - signal_handler - INFO - [msg_1749734072336_3080] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (SIMULATION) - 2HMCEAX1

 Timestamp: 2025-06-12 18:44:32
[REPEAT] Tr...
2025-06-12 18:44:32,338 - signal_handler - INFO - [msg_1749734072336_3080] Message queued with normal priority
2025-06-12 18:44:32,342 - signal_handler - INFO - [msg_1749734072336_3080] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:44:32,399 - signal_handler - INFO - [msg_1749734072336_3080] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:44:32,502 - signal_handler - INFO - [msg_1749734072336_3080] Message sent directly successfully using entity
2025-06-12 18:44:32,505 - signal_handler - INFO - [msg_1749734072336_3080] Message sent successfully via direct send method
2025-06-12 18:44:32,506 - signal_handler - INFO - [msg_1749734072336_3080] Message sent successfully on attempt 1
2025-06-12 18:46:06,309 - signal_handler - INFO - Received sell notification: [PURPLE] [SIM SELL [?] STOP LOSS HIT (-23.2% <= -20.0%)] [?] 2025-06-12 18:46:06
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]...
2025-06-12 18:46:06,312 - signal_handler - INFO - [msg_1749734166309_6494] [SELL NOTIFICATION]:  [SIM SELL [?] STOP LOSS HIT (-23.2% <= -20.0%)] [?] 2025-06-12 18:46:06

 Triggered by: solana sign...
2025-06-12 18:46:06,317 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749734166309_6494.txt
2025-06-12 18:46:06,318 - signal_handler - INFO - [msg_1749734166309_6494] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:46:06,377 - signal_handler - INFO - [msg_1749734166309_6494] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:46:06,525 - signal_handler - INFO - [msg_1749734166309_6494] Message sent directly successfully using entity
2025-06-12 18:46:06,526 - signal_handler - INFO - [msg_1749734166309_6494] Message sent directly successfully
2025-06-12 18:46:06,528 - signal_handler - INFO - [msg_1749734166528_1658] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-12 18:46:06
[REPEAT] Trade: SIMULATION

[MONEY...
2025-06-12 18:46:06,529 - signal_handler - INFO - [msg_1749734166528_1658] Message queued with normal priority
2025-06-12 18:46:06,531 - signal_handler - INFO - [msg_1749734166528_1658] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:46:06,588 - signal_handler - INFO - [msg_1749734166528_1658] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:46:06,699 - signal_handler - INFO - [msg_1749734166528_1658] Message sent directly successfully using entity
2025-06-12 18:46:06,700 - signal_handler - INFO - [msg_1749734166528_1658] Message sent successfully via direct send method
2025-06-12 18:46:06,701 - signal_handler - INFO - [msg_1749734166528_1658] Message sent successfully on attempt 1
2025-06-12 18:47:18,412 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$16.8K(+373.9%)**
*...
2025-06-12 18:47:18,413 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 18:47:18,414 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 18:47:18,415 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 18:47:18,416 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:47:18,417 - signal_handler - INFO - Found contract address with 'pump' suffix: 76i3DDuDJMjkV2rATZGHKwrwAQHekXB1YM6EcLt8pump
2025-06-12 18:47:18,419 - signal_handler - INFO - Found contract address: DeGQJaZ57uT1P1EGmRhN25SSmyuJbxNh3JrC9PdeB6oe
2025-06-12 18:47:18,419 - signal_handler - INFO - Using 'pump' address as highest priority: 76i3DDuDJMjkV2rATZGHKwrwAQHekXB1YM6EcLt8pump
2025-06-12 18:47:18,420 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 76i3DDuDJMjkV2rATZGHKwrwAQHekXB1YM6EcLt8pump
2025-06-12 18:47:18,422 - signal_handler - INFO - Detected GMGN format
2025-06-12 18:47:18,423 - signal_handler - INFO - Found token symbol: 16
2025-06-12 18:47:18,424 - signal_handler - INFO - Found FDV: 16.8K - 16.80 (+373.9%)
2025-06-12 18:47:18,425 - signal_handler - INFO - Detected GMGN channel signal for token: 76i3DDuDJMjkV2rATZGHKwrwAQHekXB1YM6EcLt8pump
2025-06-12 18:47:18,425 - signal_handler - INFO - Extracted signal: Token=76i3DDuDJMjkV2rATZGHKwrwAQHekXB1YM6EcLt8pump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 18:47:18,426 - signal_handler - INFO - Added signal to queue: 76i3DDuDJMjkV2rATZGHKwrwAQHekXB1YM6EcLt8pump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 18:47:18,427 - signal_handler - INFO - Calling direct signal callback for 76i3DDuDJMjkV2rATZGHKwrwAQHekXB1YM6EcLt8pump
2025-06-12 18:47:18,435 - signal_handler - INFO - Direct signal processing completed for 76i3DDuDJMjkV2rATZGHKwrwAQHekXB1YM6EcLt8pump
2025-06-12 18:47:18,436 - signal_handler - INFO - Signal forwarded to bot controller: 76i3DDuDJMjkV2rATZGHKwrwAQHekXB1YM6EcLt8pump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 18:47:18,437 - signal_handler - INFO - Retrieved signal from queue: 76i3DDuDJMjkV2rATZGHKwrwAQHekXB1YM6EcLt8pump from solana signal alert - gmgn
2025-06-12 18:47:18,440 - signal_handler - INFO - [msg_1749734238439_8596] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:47:18

 Token CA: 76i3DDuDJMjkV2rATZGHKwrwAQHekXB1YM6EcLt8p...
2025-06-12 18:47:18,440 - signal_handler - INFO - [msg_1749734238439_8596] Message queued with normal priority
2025-06-12 18:47:25,413 - signal_handler - INFO - [msg_1749734245412_9770] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] HIGH WHALE RISK] [?] 2025-06-12 18:47:25

 Token: 76i3DDuDJMjkV2rATZGHKwrw...
2025-06-12 18:47:25,414 - signal_handler - INFO - [msg_1749734245412_9770] Message queued with normal priority
2025-06-12 18:47:25,415 - signal_handler - INFO - [msg_1749734238439_8596] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:47:25,556 - signal_handler - INFO - [msg_1749734238439_8596] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:47:25,665 - signal_handler - INFO - [msg_1749734238439_8596] Message sent directly successfully using entity
2025-06-12 18:47:25,666 - signal_handler - INFO - [msg_1749734238439_8596] Message sent successfully via direct send method
2025-06-12 18:47:25,668 - signal_handler - INFO - [msg_1749734238439_8596] Message sent successfully on attempt 1
2025-06-12 18:47:25,668 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 18:47:26,170 - signal_handler - INFO - [msg_1749734245412_9770] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:47:26,228 - signal_handler - INFO - [msg_1749734245412_9770] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:47:26,339 - signal_handler - INFO - [msg_1749734245412_9770] Message sent directly successfully using entity
2025-06-12 18:47:26,340 - signal_handler - INFO - [msg_1749734245412_9770] Message sent successfully via direct send method
2025-06-12 18:47:26,341 - signal_handler - INFO - [msg_1749734245412_9770] Message sent successfully on attempt 1
2025-06-12 18:48:13,495 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$9806.3884(+211.2%)...
2025-06-12 18:48:13,497 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 18:48:13,497 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 18:48:13,498 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 18:48:13,499 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:48:13,500 - signal_handler - INFO - Found contract address with 'pump' suffix in CA line: Kpey4U6Vik1A7UMjUJTPztrZHBvW8SLFdsSYjJpump
2025-06-12 18:48:13,501 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: Kpey4U6Vik1A7UMjUJTPztrZHBvW8SLFdsSYjJpump
2025-06-12 18:48:13,503 - signal_handler - INFO - Detected GMGN format
2025-06-12 18:48:13,503 - signal_handler - INFO - Found token symbol: 9806
2025-06-12 18:48:13,504 - signal_handler - INFO - Found FDV: 9806.3884 - 9.81K (+211.2%)
2025-06-12 18:48:13,506 - signal_handler - INFO - Detected GMGN channel signal for token: Kpey4U6Vik1A7UMjUJTPztrZHBvW8SLFdsSYjJpump
2025-06-12 18:48:13,507 - signal_handler - INFO - Extracted signal: Token=Kpey4U6Vik1A7UMjUJTPztrZHBvW8SLFdsSYjJpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 18:48:13,508 - signal_handler - INFO - Added signal to queue: Kpey4U6Vik1A7UMjUJTPztrZHBvW8SLFdsSYjJpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 18:48:13,509 - signal_handler - INFO - Calling direct signal callback for Kpey4U6Vik1A7UMjUJTPztrZHBvW8SLFdsSYjJpump
2025-06-12 18:48:13,517 - signal_handler - INFO - Direct signal processing completed for Kpey4U6Vik1A7UMjUJTPztrZHBvW8SLFdsSYjJpump
2025-06-12 18:48:13,519 - signal_handler - INFO - Signal forwarded to bot controller: Kpey4U6Vik1A7UMjUJTPztrZHBvW8SLFdsSYjJpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 18:48:13,519 - signal_handler - INFO - Retrieved signal from queue: Kpey4U6Vik1A7UMjUJTPztrZHBvW8SLFdsSYjJpump from solana signal alert - gmgn
2025-06-12 18:48:13,522 - signal_handler - INFO - [msg_1749734293521_1018] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:48:13

 Token CA: Kpey4U6Vik1A7UMjUJTPztrZHBvW8SLFdsSYjJpum...
2025-06-12 18:48:13,522 - signal_handler - INFO - [msg_1749734293521_1018] Message queued with normal priority
2025-06-12 18:48:15,539 - signal_handler - INFO - [msg_1749734295539_9874] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] TOKEN NOT FOUND] [?] 2025-06-12 18:48:15

 Token: Kpey4U6Vik1A7UMjUJTPztrZ...
2025-06-12 18:48:15,540 - signal_handler - INFO - [msg_1749734295539_9874] Message queued with normal priority
2025-06-12 18:48:15,541 - signal_handler - INFO - [msg_1749734293521_1018] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:48:15,597 - signal_handler - INFO - [msg_1749734293521_1018] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:48:15,704 - signal_handler - INFO - [msg_1749734293521_1018] Message sent directly successfully using entity
2025-06-12 18:48:15,705 - signal_handler - INFO - [msg_1749734293521_1018] Message sent successfully via direct send method
2025-06-12 18:48:15,706 - signal_handler - INFO - [msg_1749734293521_1018] Message sent successfully on attempt 1
2025-06-12 18:48:15,706 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 18:48:16,209 - signal_handler - INFO - [msg_1749734295539_9874] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:48:16,268 - signal_handler - INFO - [msg_1749734295539_9874] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:48:16,382 - signal_handler - INFO - [msg_1749734295539_9874] Message sent directly successfully using entity
2025-06-12 18:48:16,383 - signal_handler - INFO - [msg_1749734295539_9874] Message sent successfully via direct send method
2025-06-12 18:48:16,384 - signal_handler - INFO - [msg_1749734295539_9874] Message sent successfully on attempt 1
2025-06-12 18:51:00,955 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$11.2K(+215.8%)**
*...
2025-06-12 18:51:00,957 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 18:51:00,957 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 18:51:00,958 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 18:51:00,959 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:51:00,961 - signal_handler - INFO - Found contract address with 'pump' suffix: bojrzPRmBRqVJoTMEUG7aBraRb14EfRrdjDaQDGpump
2025-06-12 18:51:00,962 - signal_handler - INFO - Found contract address: DR67AuAuK9LPxoVikGTyDBYPFFekfxzb7p9ABRWBFaTu
2025-06-12 18:51:00,963 - signal_handler - INFO - Using 'pump' address as highest priority: bojrzPRmBRqVJoTMEUG7aBraRb14EfRrdjDaQDGpump
2025-06-12 18:51:00,964 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: bojrzPRmBRqVJoTMEUG7aBraRb14EfRrdjDaQDGpump
2025-06-12 18:51:00,967 - signal_handler - INFO - Detected GMGN format
2025-06-12 18:51:00,968 - signal_handler - INFO - Found token symbol: 11
2025-06-12 18:51:00,969 - signal_handler - INFO - Found FDV: 11.2K - 11.20 (+215.8%)
2025-06-12 18:51:00,970 - signal_handler - INFO - Detected GMGN channel signal for token: bojrzPRmBRqVJoTMEUG7aBraRb14EfRrdjDaQDGpump
2025-06-12 18:51:00,971 - signal_handler - INFO - Extracted signal: Token=bojrzPRmBRqVJoTMEUG7aBraRb14EfRrdjDaQDGpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 18:51:00,973 - signal_handler - INFO - Added signal to queue: bojrzPRmBRqVJoTMEUG7aBraRb14EfRrdjDaQDGpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 18:51:00,974 - signal_handler - INFO - Calling direct signal callback for bojrzPRmBRqVJoTMEUG7aBraRb14EfRrdjDaQDGpump
2025-06-12 18:51:00,982 - signal_handler - INFO - Direct signal processing completed for bojrzPRmBRqVJoTMEUG7aBraRb14EfRrdjDaQDGpump
2025-06-12 18:51:00,983 - signal_handler - INFO - Signal forwarded to bot controller: bojrzPRmBRqVJoTMEUG7aBraRb14EfRrdjDaQDGpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 18:51:00,983 - signal_handler - INFO - Retrieved signal from queue: bojrzPRmBRqVJoTMEUG7aBraRb14EfRrdjDaQDGpump from solana signal alert - gmgn
2025-06-12 18:51:00,985 - signal_handler - INFO - [msg_1749734460985_2249] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:51:00

 Token CA: bojrzPRmBRqVJoTMEUG7aBraRb14EfRrdjDaQDGpu...
2025-06-12 18:51:00,986 - signal_handler - INFO - [msg_1749734460985_2249] Message queued with normal priority
2025-06-12 18:51:07,840 - signal_handler - INFO - [msg_1749734460985_2249] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:51:07,983 - signal_handler - INFO - [msg_1749734460985_2249] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:51:08,112 - signal_handler - INFO - [msg_1749734460985_2249] Message sent directly successfully using entity
2025-06-12 18:51:08,113 - signal_handler - INFO - [msg_1749734460985_2249] Message sent successfully via direct send method
2025-06-12 18:51:08,114 - signal_handler - INFO - [msg_1749734460985_2249] Message sent successfully on attempt 1
2025-06-12 18:51:08,298 - signal_handler - INFO - [msg_1749734468295_2660] [SELL NOTIFICATION]: [GREEN] [SIM BUY EXECUTED] [?] 2025-06-12 18:51:08

 Signal Source: solana signal alert - gmgn
 Toke...
2025-06-12 18:51:08,302 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749734468295_2660.txt
2025-06-12 18:51:08,303 - signal_handler - INFO - [msg_1749734468295_2660] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:51:08,363 - signal_handler - INFO - [msg_1749734468295_2660] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:51:08,463 - signal_handler - INFO - [msg_1749734468295_2660] Message sent directly successfully using entity
2025-06-12 18:51:08,464 - signal_handler - INFO - [msg_1749734468295_2660] Message sent directly successfully
2025-06-12 18:51:08,464 - signal_handler - INFO - [msg_1749734468464_4043] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (SIMULATION) - BOJRZPRM

 Timestamp: 2025-06-12 18:51:08
[REPEAT] Tr...
2025-06-12 18:51:08,464 - signal_handler - INFO - [msg_1749734468464_4043] Message queued with normal priority
2025-06-12 18:51:08,535 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.08s (interval: 0.50s)
2025-06-12 18:51:08,627 - signal_handler - INFO - [msg_1749734468464_4043] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:51:08,690 - signal_handler - INFO - [msg_1749734468464_4043] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:51:08,798 - signal_handler - INFO - [msg_1749734468464_4043] Message sent directly successfully using entity
2025-06-12 18:51:08,799 - signal_handler - INFO - [msg_1749734468464_4043] Message sent successfully via direct send method
2025-06-12 18:51:08,801 - signal_handler - INFO - [msg_1749734468464_4043] Message sent successfully on attempt 1
2025-06-12 18:51:39,371 - signal_handler - INFO - Received sell notification: [PURPLE] [SIM SELL [?] TP1 HIT (36.4% >= 20.0%)] [?] 2025-06-12 18:51:39
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]
[?] Trig...
2025-06-12 18:51:39,372 - signal_handler - INFO - [msg_1749734499371_2887] [SELL NOTIFICATION]:  [SIM SELL [?] TP1 HIT (36.4% >= 20.0%)] [?] 2025-06-12 18:51:39

 Triggered by: solana signal alert...
2025-06-12 18:51:39,374 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749734499371_2887.txt
2025-06-12 18:51:39,374 - signal_handler - INFO - [msg_1749734499371_2887] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:51:39,519 - signal_handler - INFO - [msg_1749734499371_2887] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:51:39,747 - signal_handler - INFO - [msg_1749734499371_2887] Message sent directly successfully using entity
2025-06-12 18:51:39,749 - signal_handler - INFO - [msg_1749734499371_2887] Message sent directly successfully
2025-06-12 18:51:39,751 - signal_handler - INFO - [msg_1749734499751_5700] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-12 18:51:39
[REPEAT] Trade: SIMULATION

[MONEY...
2025-06-12 18:51:39,754 - signal_handler - INFO - [msg_1749734499751_5700] Message queued with normal priority
2025-06-12 18:51:39,805 - signal_handler - INFO - [msg_1749734499751_5700] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:51:39,980 - signal_handler - INFO - [msg_1749734499751_5700] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:51:40,154 - signal_handler - INFO - [msg_1749734499751_5700] Message sent directly successfully using entity
2025-06-12 18:51:40,155 - signal_handler - INFO - [msg_1749734499751_5700] Message sent successfully via direct send method
2025-06-12 18:51:40,156 - signal_handler - INFO - [msg_1749734499751_5700] Message sent successfully on attempt 1
2025-06-12 18:51:51,478 - signal_handler - INFO - Received message from channel -1002017857449: 6sqLK7Uy3uMNJpYXytDGCZFezeGJQzgoXC2RKx32pump...
2025-06-12 18:51:51,480 - signal_handler - INFO - Setting channel_identifier to 'PEPE CALLS' based on chat_id -1002017857449
2025-06-12 18:51:51,481 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:51:51,482 - signal_handler - INFO - Found contract address with 'pump' suffix: 6sqLK7Uy3uMNJpYXytDGCZFezeGJQzgoXC2RKx32pump
2025-06-12 18:51:51,483 - signal_handler - INFO - Using 'pump' address as highest priority: 6sqLK7Uy3uMNJpYXytDGCZFezeGJQzgoXC2RKx32pump
2025-06-12 18:51:51,484 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 6sqLK7Uy3uMNJpYXytDGCZFezeGJQzgoXC2RKx32pump
2025-06-12 18:51:51,485 - signal_handler - INFO - Detected signal from PEPE CALLS for token: 6sqLK7Uy3uMNJpYXytDGCZFezeGJQzgoXC2RKx32pump
2025-06-12 18:51:51,486 - signal_handler - INFO - Extracted signal: Token=6sqLK7Uy3uMNJpYXytDGCZFezeGJQzgoXC2RKx32pump, Source=PEPE CALLS, Metrics Count=1
2025-06-12 18:51:51,487 - signal_handler - INFO - Added signal to queue: 6sqLK7Uy3uMNJpYXytDGCZFezeGJQzgoXC2RKx32pump from PEPE CALLS with confidence 0.5, GMGN channel: False
2025-06-12 18:51:51,488 - signal_handler - INFO - Calling direct signal callback for 6sqLK7Uy3uMNJpYXytDGCZFezeGJQzgoXC2RKx32pump
2025-06-12 18:51:51,496 - signal_handler - INFO - Direct signal processing completed for 6sqLK7Uy3uMNJpYXytDGCZFezeGJQzgoXC2RKx32pump
2025-06-12 18:51:51,497 - signal_handler - INFO - Signal forwarded to bot controller: 6sqLK7Uy3uMNJpYXytDGCZFezeGJQzgoXC2RKx32pump from PEPE CALLS (confidence: 0.50)
2025-06-12 18:51:51,498 - signal_handler - INFO - Retrieved signal from queue: 6sqLK7Uy3uMNJpYXytDGCZFezeGJQzgoXC2RKx32pump from PEPE CALLS
2025-06-12 18:51:51,501 - signal_handler - INFO - [msg_1749734511501_7966] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:51:51

 Token CA: 6sqLK7Uy3uMNJpYXytDGCZFezeGJQzgoXC2RKx32p...
2025-06-12 18:51:51,502 - signal_handler - INFO - [msg_1749734511501_7966] Message queued with normal priority
2025-06-12 18:51:54,619 - signal_handler - INFO - [msg_1749734514619_9682] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] HIGH WHALE RISK] [?] 2025-06-12 18:51:54

 Token: 6sqLK7Uy3uMNJpYXytDGCZFe...
2025-06-12 18:51:54,620 - signal_handler - INFO - [msg_1749734514619_9682] Message queued with normal priority
2025-06-12 18:51:54,620 - signal_handler - INFO - [msg_1749734511501_7966] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:51:54,677 - signal_handler - INFO - [msg_1749734511501_7966] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:51:54,792 - signal_handler - INFO - [msg_1749734511501_7966] Message sent directly successfully using entity
2025-06-12 18:51:54,793 - signal_handler - INFO - [msg_1749734511501_7966] Message sent successfully via direct send method
2025-06-12 18:51:54,794 - signal_handler - INFO - [msg_1749734511501_7966] Message sent successfully on attempt 1
2025-06-12 18:51:54,795 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 18:51:55,304 - signal_handler - INFO - [msg_1749734514619_9682] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:51:55,362 - signal_handler - INFO - [msg_1749734514619_9682] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:51:55,467 - signal_handler - INFO - [msg_1749734514619_9682] Message sent directly successfully using entity
2025-06-12 18:51:55,468 - signal_handler - INFO - [msg_1749734514619_9682] Message sent successfully via direct send method
2025-06-12 18:51:55,469 - signal_handler - INFO - [msg_1749734514619_9682] Message sent successfully on attempt 1
2025-06-12 18:51:55,801 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$14.5K(+315.3%)**
*...
2025-06-12 18:51:55,802 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 18:51:55,803 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 18:51:55,803 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 18:51:55,804 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:51:55,806 - signal_handler - INFO - Found contract address with 'pump' suffix: FSmUKrPGEy7Lg8tqwTN8BUEYSuQ1FyHB2HiHpPPCpump
2025-06-12 18:51:55,807 - signal_handler - INFO - Found contract address: 8MUsZdugDmyZLqwaAFqUTUNQ6UYHfon1QD3JL2vgA5mZ
2025-06-12 18:51:55,808 - signal_handler - INFO - Using 'pump' address as highest priority: FSmUKrPGEy7Lg8tqwTN8BUEYSuQ1FyHB2HiHpPPCpump
2025-06-12 18:51:55,809 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: FSmUKrPGEy7Lg8tqwTN8BUEYSuQ1FyHB2HiHpPPCpump
2025-06-12 18:51:55,810 - signal_handler - INFO - Detected GMGN format
2025-06-12 18:51:55,811 - signal_handler - INFO - Found token symbol: 14
2025-06-12 18:51:55,812 - signal_handler - INFO - Found FDV: 14.5K - 14.50 (+315.3%)
2025-06-12 18:51:55,813 - signal_handler - INFO - Detected GMGN channel signal for token: FSmUKrPGEy7Lg8tqwTN8BUEYSuQ1FyHB2HiHpPPCpump
2025-06-12 18:51:55,814 - signal_handler - INFO - Extracted signal: Token=FSmUKrPGEy7Lg8tqwTN8BUEYSuQ1FyHB2HiHpPPCpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 18:51:55,815 - signal_handler - INFO - Added signal to queue: FSmUKrPGEy7Lg8tqwTN8BUEYSuQ1FyHB2HiHpPPCpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 18:51:55,816 - signal_handler - INFO - Calling direct signal callback for FSmUKrPGEy7Lg8tqwTN8BUEYSuQ1FyHB2HiHpPPCpump
2025-06-12 18:51:55,821 - signal_handler - INFO - Direct signal processing completed for FSmUKrPGEy7Lg8tqwTN8BUEYSuQ1FyHB2HiHpPPCpump
2025-06-12 18:51:55,822 - signal_handler - INFO - Signal forwarded to bot controller: FSmUKrPGEy7Lg8tqwTN8BUEYSuQ1FyHB2HiHpPPCpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 18:51:55,822 - signal_handler - INFO - Retrieved signal from queue: FSmUKrPGEy7Lg8tqwTN8BUEYSuQ1FyHB2HiHpPPCpump from solana signal alert - gmgn
2025-06-12 18:51:55,824 - signal_handler - INFO - [msg_1749734515823_5335] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:51:55

 Token CA: FSmUKrPGEy7Lg8tqwTN8BUEYSuQ1FyHB2HiHpPPCp...
2025-06-12 18:51:55,824 - signal_handler - INFO - [msg_1749734515823_5335] Message queued with normal priority
2025-06-12 18:52:02,874 - signal_handler - INFO - [msg_1749734522874_1904] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] HIGH WHALE RISK] [?] 2025-06-12 18:52:02

 Token: FSmUKrPGEy7Lg8tqwTN8BUEY...
2025-06-12 18:52:02,875 - signal_handler - INFO - [msg_1749734522874_1904] Message queued with normal priority
2025-06-12 18:52:02,876 - signal_handler - INFO - [msg_1749734515823_5335] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:52:03,045 - signal_handler - INFO - [msg_1749734515823_5335] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:52:03,141 - signal_handler - INFO - [msg_1749734515823_5335] Message sent directly successfully using entity
2025-06-12 18:52:03,142 - signal_handler - INFO - [msg_1749734515823_5335] Message sent successfully via direct send method
2025-06-12 18:52:03,142 - signal_handler - INFO - [msg_1749734515823_5335] Message sent successfully on attempt 1
2025-06-12 18:52:03,143 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 18:52:03,655 - signal_handler - INFO - [msg_1749734522874_1904] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:52:03,713 - signal_handler - INFO - [msg_1749734522874_1904] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:52:03,814 - signal_handler - INFO - [msg_1749734522874_1904] Message sent directly successfully using entity
2025-06-12 18:52:03,815 - signal_handler - INFO - [msg_1749734522874_1904] Message sent successfully via direct send method
2025-06-12 18:52:03,816 - signal_handler - INFO - [msg_1749734522874_1904] Message sent successfully on attempt 1
2025-06-12 18:52:30,590 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$9588.4733(+200.9%)...
2025-06-12 18:52:30,590 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 18:52:30,591 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 18:52:30,591 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 18:52:30,592 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:52:30,592 - signal_handler - INFO - Found contract address with 'pump' suffix: ********************************************
2025-06-12 18:52:30,593 - signal_handler - INFO - Found contract address: AReSJSGMKT1MU5bq4Zd8qQv6JBmnqYZcPxyNz1mZQJNY
2025-06-12 18:52:30,593 - signal_handler - INFO - Using 'pump' address as highest priority: ********************************************
2025-06-12 18:52:30,594 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: ********************************************
2025-06-12 18:52:30,594 - signal_handler - INFO - Detected GMGN format
2025-06-12 18:52:30,595 - signal_handler - INFO - Found token symbol: 9588
2025-06-12 18:52:30,595 - signal_handler - INFO - Found FDV: 9588.4733 - 9.59K (+200.9%)
2025-06-12 18:52:30,596 - signal_handler - INFO - Detected GMGN channel signal for token: ********************************************
2025-06-12 18:52:30,596 - signal_handler - INFO - Extracted signal: Token=********************************************, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 18:52:30,597 - signal_handler - INFO - Added signal to queue: ******************************************** from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 18:52:30,597 - signal_handler - INFO - Calling direct signal callback for ********************************************
2025-06-12 18:52:30,600 - signal_handler - INFO - Direct signal processing completed for ********************************************
2025-06-12 18:52:30,601 - signal_handler - INFO - Signal forwarded to bot controller: ******************************************** from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 18:52:30,601 - signal_handler - INFO - Retrieved signal from queue: ******************************************** from solana signal alert - gmgn
2025-06-12 18:52:30,603 - signal_handler - INFO - [msg_1749734550603_4935] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:52:30

 Token CA: 3PeXoJe1esaBvtPupw3XUfS8rKTDFbzE9mJZSFWTp...
2025-06-12 18:52:30,604 - signal_handler - INFO - [msg_1749734550603_4935] Message queued with normal priority
2025-06-12 18:52:37,408 - signal_handler - INFO - [msg_1749734557408_2007] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW LIQUIDITY] [?] 2025-06-12 18:52:37

 Token: 3PeXoJe1esaBvtPupw3XUfS8rK...
2025-06-12 18:52:37,410 - signal_handler - INFO - [msg_1749734557408_2007] Message queued with normal priority
2025-06-12 18:52:37,411 - signal_handler - INFO - [msg_1749734550603_4935] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:52:37,475 - signal_handler - INFO - [msg_1749734550603_4935] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:52:37,615 - signal_handler - INFO - [msg_1749734550603_4935] Message sent directly successfully using entity
2025-06-12 18:52:37,616 - signal_handler - INFO - [msg_1749734550603_4935] Message sent successfully via direct send method
2025-06-12 18:52:37,617 - signal_handler - INFO - [msg_1749734550603_4935] Message sent successfully on attempt 1
2025-06-12 18:52:37,617 - signal_handler - INFO - Message queue processor active with 1 messages pending
2025-06-12 18:52:37,618 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 18:52:38,120 - signal_handler - INFO - [msg_1749734557408_2007] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:52:38,187 - signal_handler - INFO - [msg_1749734557408_2007] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:52:38,300 - signal_handler - INFO - [msg_1749734557408_2007] Message sent directly successfully using entity
2025-06-12 18:52:38,301 - signal_handler - INFO - [msg_1749734557408_2007] Message sent successfully via direct send method
2025-06-12 18:52:38,302 - signal_handler - INFO - [msg_1749734557408_2007] Message sent successfully on attempt 1
2025-06-12 18:52:50,523 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$11K(+247.3%)**
**[...
2025-06-12 18:52:50,525 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 18:52:50,526 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 18:52:50,527 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 18:52:50,529 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:52:50,532 - signal_handler - INFO - Found contract address: 6GCoT2E5oUD2vUfyFGSzavqk7soiqG8E2w5aCm7oJbcQ
2025-06-12 18:52:50,533 - signal_handler - INFO - Found contract address: Bz1zbuyMhPC7qg4zx6eeBcVEDUCzC9gK5AAzvuNU56YQ
2025-06-12 18:52:50,535 - signal_handler - INFO - Found 2 unique token addresses
2025-06-12 18:52:50,535 - signal_handler - INFO - Token address: 6GCoT2E5oUD2vUfyFGSzavqk7soiqG8E2w5aCm7oJbcQ
2025-06-12 18:52:50,536 - signal_handler - INFO - Token address: Bz1zbuyMhPC7qg4zx6eeBcVEDUCzC9gK5AAzvuNU56YQ
2025-06-12 18:52:50,538 - signal_handler - INFO - Detected GMGN format
2025-06-12 18:52:50,539 - signal_handler - INFO - Found token symbol: 11K
2025-06-12 18:52:50,540 - signal_handler - INFO - Found token full name: +247.3%
2025-06-12 18:52:50,540 - signal_handler - INFO - Found FDV: 11K - 11.00K (+247.3%)
2025-06-12 18:52:50,541 - signal_handler - INFO - Detected GMGN channel signal for token: 6GCoT2E5oUD2vUfyFGSzavqk7soiqG8E2w5aCm7oJbcQ
2025-06-12 18:52:50,541 - signal_handler - INFO - Extracted signal: Token=6GCoT2E5oUD2vUfyFGSzavqk7soiqG8E2w5aCm7oJbcQ, Source=solana signal alert - gmgn, Metrics Count=6
2025-06-12 18:52:50,541 - signal_handler - INFO - Added signal to queue: 6GCoT2E5oUD2vUfyFGSzavqk7soiqG8E2w5aCm7oJbcQ from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 18:52:50,542 - signal_handler - INFO - Calling direct signal callback for 6GCoT2E5oUD2vUfyFGSzavqk7soiqG8E2w5aCm7oJbcQ
2025-06-12 18:52:50,545 - signal_handler - INFO - Direct signal processing completed for 6GCoT2E5oUD2vUfyFGSzavqk7soiqG8E2w5aCm7oJbcQ
2025-06-12 18:52:50,545 - signal_handler - INFO - Signal forwarded to bot controller: 6GCoT2E5oUD2vUfyFGSzavqk7soiqG8E2w5aCm7oJbcQ from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 18:52:50,546 - signal_handler - INFO - Retrieved signal from queue: 6GCoT2E5oUD2vUfyFGSzavqk7soiqG8E2w5aCm7oJbcQ from solana signal alert - gmgn
2025-06-12 18:52:50,547 - signal_handler - INFO - [msg_1749734570547_1470] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:52:50

 Token CA: 6GCoT2E5oUD2vUfyFGSzavqk7soiqG8E2w5aCm7oJ...
2025-06-12 18:52:50,548 - signal_handler - INFO - [msg_1749734570547_1470] Message queued with normal priority
2025-06-12 18:52:57,420 - signal_handler - INFO - [msg_1749734577419_3041] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW LIQUIDITY] [?] 2025-06-12 18:52:57

 Token: 6GCoT2E5oUD2vUfyFGSzavqk7s...
2025-06-12 18:52:57,421 - signal_handler - INFO - [msg_1749734577419_3041] Message queued with normal priority
2025-06-12 18:52:57,423 - signal_handler - INFO - [msg_1749734570547_1470] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:52:57,481 - signal_handler - INFO - [msg_1749734570547_1470] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:52:57,581 - signal_handler - INFO - [msg_1749734570547_1470] Message sent directly successfully using entity
2025-06-12 18:52:57,583 - signal_handler - INFO - [msg_1749734570547_1470] Message sent successfully via direct send method
2025-06-12 18:52:57,584 - signal_handler - INFO - [msg_1749734570547_1470] Message sent successfully on attempt 1
2025-06-12 18:52:57,585 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 18:52:58,098 - signal_handler - INFO - [msg_1749734577419_3041] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:52:58,162 - signal_handler - INFO - [msg_1749734577419_3041] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:52:58,278 - signal_handler - INFO - [msg_1749734577419_3041] Message sent directly successfully using entity
2025-06-12 18:52:58,279 - signal_handler - INFO - [msg_1749734577419_3041] Message sent successfully via direct send method
2025-06-12 18:52:58,280 - signal_handler - INFO - [msg_1749734577419_3041] Message sent successfully on attempt 1
2025-06-12 18:53:22,468 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$12.2K(+262.8%)**
*...
2025-06-12 18:53:22,469 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 18:53:22,470 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 18:53:22,471 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 18:53:22,472 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:53:22,473 - signal_handler - INFO - Found contract address with 'pump' suffix: CmV46bfZANn1h2KR1ECXs8dNhG1qNxZcaU6eE7iUpump
2025-06-12 18:53:22,474 - signal_handler - INFO - Found contract address: Bqy3nCHK8mM1iYLYmeW45ghLxpYPjspPdkP1VLM6nXct
2025-06-12 18:53:22,475 - signal_handler - INFO - Using 'pump' address as highest priority: CmV46bfZANn1h2KR1ECXs8dNhG1qNxZcaU6eE7iUpump
2025-06-12 18:53:22,476 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: CmV46bfZANn1h2KR1ECXs8dNhG1qNxZcaU6eE7iUpump
2025-06-12 18:53:22,478 - signal_handler - INFO - Detected GMGN format
2025-06-12 18:53:22,479 - signal_handler - INFO - Found token symbol: 12
2025-06-12 18:53:22,480 - signal_handler - INFO - Found FDV: 12.2K - 12.20 (+262.8%)
2025-06-12 18:53:22,481 - signal_handler - INFO - Detected GMGN channel signal for token: CmV46bfZANn1h2KR1ECXs8dNhG1qNxZcaU6eE7iUpump
2025-06-12 18:53:22,482 - signal_handler - INFO - Extracted signal: Token=CmV46bfZANn1h2KR1ECXs8dNhG1qNxZcaU6eE7iUpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 18:53:22,483 - signal_handler - INFO - Added signal to queue: CmV46bfZANn1h2KR1ECXs8dNhG1qNxZcaU6eE7iUpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 18:53:22,484 - signal_handler - INFO - Calling direct signal callback for CmV46bfZANn1h2KR1ECXs8dNhG1qNxZcaU6eE7iUpump
2025-06-12 18:53:22,490 - signal_handler - INFO - Direct signal processing completed for CmV46bfZANn1h2KR1ECXs8dNhG1qNxZcaU6eE7iUpump
2025-06-12 18:53:22,491 - signal_handler - INFO - Signal forwarded to bot controller: CmV46bfZANn1h2KR1ECXs8dNhG1qNxZcaU6eE7iUpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 18:53:22,491 - signal_handler - INFO - Retrieved signal from queue: CmV46bfZANn1h2KR1ECXs8dNhG1qNxZcaU6eE7iUpump from solana signal alert - gmgn
2025-06-12 18:53:22,494 - signal_handler - INFO - [msg_1749734602494_4786] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:53:22

 Token CA: CmV46bfZANn1h2KR1ECXs8dNhG1qNxZcaU6eE7iUp...
2025-06-12 18:53:22,495 - signal_handler - INFO - [msg_1749734602494_4786] Message queued with normal priority
2025-06-12 18:53:29,313 - signal_handler - INFO - [msg_1749734609312_6649] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] HIGH WHALE RISK] [?] 2025-06-12 18:53:29

 Token: CmV46bfZANn1h2KR1ECXs8dN...
2025-06-12 18:53:29,314 - signal_handler - INFO - [msg_1749734609312_6649] Message queued with normal priority
2025-06-12 18:53:29,316 - signal_handler - INFO - [msg_1749734602494_4786] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:53:29,375 - signal_handler - INFO - [msg_1749734602494_4786] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:53:29,483 - signal_handler - INFO - [msg_1749734602494_4786] Message sent directly successfully using entity
2025-06-12 18:53:29,485 - signal_handler - INFO - [msg_1749734602494_4786] Message sent successfully via direct send method
2025-06-12 18:53:29,486 - signal_handler - INFO - [msg_1749734602494_4786] Message sent successfully on attempt 1
2025-06-12 18:53:29,487 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 18:53:30,001 - signal_handler - INFO - [msg_1749734609312_6649] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:53:30,086 - signal_handler - INFO - [msg_1749734609312_6649] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:53:30,199 - signal_handler - INFO - [msg_1749734609312_6649] Message sent directly successfully using entity
2025-06-12 18:53:30,200 - signal_handler - INFO - [msg_1749734609312_6649] Message sent successfully via direct send method
2025-06-12 18:53:30,201 - signal_handler - INFO - [msg_1749734609312_6649] Message sent successfully on attempt 1
2025-06-12 18:56:28,868 - signal_handler - INFO - Received message from channel -1002017173747: [ROCKET] NEW SOL HIGH VOLUME TOKEN [ROCKET]

            [TARGET] Token Symbol: $EVERYTHING
        ...
2025-06-12 18:56:28,870 - signal_handler - INFO - Setting channel_identifier to 'SOL HIGH VOLUME ALERT | Bordga' based on chat_id -1002017173747
2025-06-12 18:56:28,871 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:56:28,872 - signal_handler - INFO - Found contract address with 'pump' suffix: BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump
2025-06-12 18:56:28,873 - signal_handler - INFO - Found contract address: cx4tpbyqewxahji4ab6rzox28pmaq12pn4mzqrja7juz
2025-06-12 18:56:28,874 - signal_handler - INFO - Using 'pump' address as highest priority: BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump
2025-06-12 18:56:28,875 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump
2025-06-12 18:56:28,876 - signal_handler - INFO - Detected High Volume Alert format
2025-06-12 18:56:28,877 - signal_handler - INFO - Found token symbol: EVERYTHING
2025-06-12 18:56:28,878 - signal_handler - INFO - Found market cap: 133K - 133.00K
2025-06-12 18:56:28,879 - signal_handler - INFO - Found age: 41 minutes
2025-06-12 18:56:28,880 - signal_handler - INFO - Found top holders: 19.57%
2025-06-12 18:56:28,880 - signal_handler - INFO - Found risk rating: Good
2025-06-12 18:56:28,881 - signal_handler - INFO - Detected signal from SOL HIGH VOLUME ALERT | Bordga for token: BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump
2025-06-12 18:56:28,881 - signal_handler - INFO - Extracted signal: Token=BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump, Source=SOL HIGH VOLUME ALERT | Bordga, Metrics Count=7
2025-06-12 18:56:28,882 - signal_handler - INFO - Signal processing health: 22 received, 22 processed, 0 dropped (100.0% success rate)
2025-06-12 18:56:28,883 - signal_handler - INFO - Added signal to queue: BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump from SOL HIGH VOLUME ALERT | Bordga with confidence 0.5, GMGN channel: False
2025-06-12 18:56:28,883 - signal_handler - INFO - Calling direct signal callback for BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump
2025-06-12 18:56:28,887 - signal_handler - INFO - Direct signal processing completed for BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump
2025-06-12 18:56:28,887 - signal_handler - INFO - Signal forwarded to bot controller: BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump from SOL HIGH VOLUME ALERT | Bordga (confidence: 0.50)
2025-06-12 18:56:28,888 - signal_handler - INFO - Retrieved signal from queue: BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump from SOL HIGH VOLUME ALERT | Bordga
2025-06-12 18:56:28,890 - signal_handler - INFO - [msg_1749734788890_9780] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:56:28

 Token CA: BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcp...
2025-06-12 18:56:28,891 - signal_handler - INFO - [msg_1749734788890_9780] Message queued with normal priority
2025-06-12 18:56:32,072 - signal_handler - INFO - [msg_1749734788890_9780] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:56:32,222 - signal_handler - INFO - [msg_1749734788890_9780] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:56:32,350 - signal_handler - INFO - [msg_1749734788890_9780] Message sent directly successfully using entity
2025-06-12 18:56:32,351 - signal_handler - INFO - [msg_1749734788890_9780] Message sent successfully via direct send method
2025-06-12 18:56:32,352 - signal_handler - INFO - [msg_1749734788890_9780] Message sent successfully on attempt 1
2025-06-12 18:56:33,215 - signal_handler - INFO - [msg_1749734793212_8087] [SELL NOTIFICATION]: [GREEN] [SIM BUY EXECUTED] [?] 2025-06-12 18:56:33

 Signal Source: SOL HIGH VOLUME ALERT | Bordga
 ...
2025-06-12 18:56:33,219 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749734793212_8087.txt
2025-06-12 18:56:33,221 - signal_handler - INFO - [msg_1749734793212_8087] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:56:33,287 - signal_handler - INFO - [msg_1749734793212_8087] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:56:33,421 - signal_handler - INFO - [msg_1749734793212_8087] Message sent directly successfully using entity
2025-06-12 18:56:33,422 - signal_handler - INFO - [msg_1749734793212_8087] Message sent directly successfully
2025-06-12 18:56:33,424 - signal_handler - INFO - [msg_1749734793423_9572] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (SIMULATION) - BSFHYL5J

 Timestamp: 2025-06-12 18:56:33
[REPEAT] Tr...
2025-06-12 18:56:33,425 - signal_handler - INFO - [msg_1749734793423_9572] Message queued with normal priority
2025-06-12 18:56:33,428 - signal_handler - INFO - [msg_1749734793423_9572] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:56:33,495 - signal_handler - INFO - [msg_1749734793423_9572] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:56:33,625 - signal_handler - INFO - [msg_1749734793423_9572] Message sent directly successfully using entity
2025-06-12 18:56:33,626 - signal_handler - INFO - [msg_1749734793423_9572] Message sent successfully via direct send method
2025-06-12 18:56:33,627 - signal_handler - INFO - [msg_1749734793423_9572] Message sent successfully on attempt 1
2025-06-12 18:58:10,012 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$13.7K(+289.1%)**
*...
2025-06-12 18:58:10,012 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 18:58:10,013 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 18:58:10,013 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 18:58:10,014 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:58:10,015 - signal_handler - INFO - Found contract address with 'pump' suffix: 4dxUv1U4tYPPJGia5GMnkhKTEhBRHjPizV7dRq4Vpump
2025-06-12 18:58:10,015 - signal_handler - INFO - Found contract address: 6yUEc3nZPs12WnDXJwSDyPBUWktnz2tYgAyU5KpK74zK
2025-06-12 18:58:10,016 - signal_handler - INFO - Using 'pump' address as highest priority: 4dxUv1U4tYPPJGia5GMnkhKTEhBRHjPizV7dRq4Vpump
2025-06-12 18:58:10,016 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 4dxUv1U4tYPPJGia5GMnkhKTEhBRHjPizV7dRq4Vpump
2025-06-12 18:58:10,017 - signal_handler - INFO - Detected GMGN format
2025-06-12 18:58:10,018 - signal_handler - INFO - Found token symbol: 13
2025-06-12 18:58:10,018 - signal_handler - INFO - Found FDV: 13.7K - 13.70 (+289.1%)
2025-06-12 18:58:10,019 - signal_handler - INFO - Detected GMGN channel signal for token: 4dxUv1U4tYPPJGia5GMnkhKTEhBRHjPizV7dRq4Vpump
2025-06-12 18:58:10,019 - signal_handler - INFO - Extracted signal: Token=4dxUv1U4tYPPJGia5GMnkhKTEhBRHjPizV7dRq4Vpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 18:58:10,020 - signal_handler - INFO - Added signal to queue: 4dxUv1U4tYPPJGia5GMnkhKTEhBRHjPizV7dRq4Vpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 18:58:10,020 - signal_handler - INFO - Calling direct signal callback for 4dxUv1U4tYPPJGia5GMnkhKTEhBRHjPizV7dRq4Vpump
2025-06-12 18:58:10,026 - signal_handler - INFO - Direct signal processing completed for 4dxUv1U4tYPPJGia5GMnkhKTEhBRHjPizV7dRq4Vpump
2025-06-12 18:58:10,026 - signal_handler - INFO - Signal forwarded to bot controller: 4dxUv1U4tYPPJGia5GMnkhKTEhBRHjPizV7dRq4Vpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 18:58:10,027 - signal_handler - INFO - Retrieved signal from queue: 4dxUv1U4tYPPJGia5GMnkhKTEhBRHjPizV7dRq4Vpump from solana signal alert - gmgn
2025-06-12 18:58:10,029 - signal_handler - INFO - [msg_1749734890029_6150] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 18:58:10

 Token CA: 4dxUv1U4tYPPJGia5GMnkhKTEhBRHjPizV7dRq4Vp...
2025-06-12 18:58:10,030 - signal_handler - INFO - [msg_1749734890029_6150] Message queued with normal priority
2025-06-12 18:58:16,995 - signal_handler - INFO - [msg_1749734896994_6657] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] HIGH WHALE RISK] [?] 2025-06-12 18:58:16

 Token: 4dxUv1U4tYPPJGia5GMnkhKT...
2025-06-12 18:58:16,996 - signal_handler - INFO - [msg_1749734896994_6657] Message queued with normal priority
2025-06-12 18:58:16,998 - signal_handler - INFO - [msg_1749734890029_6150] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:58:17,141 - signal_handler - INFO - [msg_1749734890029_6150] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:58:17,258 - signal_handler - INFO - [msg_1749734890029_6150] Message sent directly successfully using entity
2025-06-12 18:58:17,259 - signal_handler - INFO - [msg_1749734890029_6150] Message sent successfully via direct send method
2025-06-12 18:58:17,260 - signal_handler - INFO - [msg_1749734890029_6150] Message sent successfully on attempt 1
2025-06-12 18:58:17,261 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 18:58:17,771 - signal_handler - INFO - [msg_1749734896994_6657] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:58:17,830 - signal_handler - INFO - [msg_1749734896994_6657] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:58:17,952 - signal_handler - INFO - [msg_1749734896994_6657] Message sent directly successfully using entity
2025-06-12 18:58:17,953 - signal_handler - INFO - [msg_1749734896994_6657] Message sent successfully via direct send method
2025-06-12 18:58:17,954 - signal_handler - INFO - [msg_1749734896994_6657] Message sent successfully on attempt 1
2025-06-12 19:00:04,142 - signal_handler - INFO - Received message from channel -1002177594166:  **Achievement Unlocked**: x8! 

@Zen_call made a **x8+** call on [Catulu ](https://t.me/spydefi_bot...
2025-06-12 19:00:04,143 - signal_handler - INFO - Setting channel_identifier to 'OxyZen Calls' based on chat_id -1002177594166
2025-06-12 19:00:04,144 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 19:00:04,145 - signal_handler - INFO - Found contract address with 'pump' suffix: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 19:00:04,146 - signal_handler - INFO - Using 'pump' address as highest priority: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 19:00:04,147 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 19:00:04,149 - signal_handler - INFO - Detected signal from OxyZen Calls for token: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 19:00:04,150 - signal_handler - INFO - Extracted signal: Token=5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump, Source=OxyZen Calls, Metrics Count=1
2025-06-12 19:00:04,151 - signal_handler - INFO - Using shorter cooldown (1s) for pump token: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 19:00:04,152 - signal_handler - INFO - Token 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump cooldown expired. Processing signal.
2025-06-12 19:00:04,153 - signal_handler - INFO - Added signal to queue: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump from OxyZen Calls with confidence 0.5, GMGN channel: False
2025-06-12 19:00:04,154 - signal_handler - INFO - Calling direct signal callback for 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 19:00:04,156 - signal_handler - INFO - Direct signal processing completed for 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 19:00:04,157 - signal_handler - INFO - Signal forwarded to bot controller: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump from OxyZen Calls (confidence: 0.50)
2025-06-12 19:00:04,159 - signal_handler - INFO - Retrieved signal from queue: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump from OxyZen Calls
2025-06-12 19:01:51,805 - signal_handler - INFO - Received message from channel -1002177594166:  **Achievement Unlocked**: Pump it up! 

@Zen_call made a **x10+** call on [Catulu ](https://t.me/sp...
2025-06-12 19:01:51,807 - signal_handler - INFO - Setting channel_identifier to 'OxyZen Calls' based on chat_id -1002177594166
2025-06-12 19:01:51,808 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 19:01:51,809 - signal_handler - INFO - Found contract address with 'pump' suffix: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 19:01:51,810 - signal_handler - INFO - Using 'pump' address as highest priority: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 19:01:51,812 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 19:01:51,813 - signal_handler - INFO - Detected signal from OxyZen Calls for token: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 19:01:51,814 - signal_handler - INFO - Extracted signal: Token=5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump, Source=OxyZen Calls, Metrics Count=1
2025-06-12 19:01:51,815 - signal_handler - INFO - Using shorter cooldown (1s) for pump token: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 19:01:51,816 - signal_handler - INFO - Token 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump cooldown expired. Processing signal.
2025-06-12 19:01:51,817 - signal_handler - INFO - Added signal to queue: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump from OxyZen Calls with confidence 0.5, GMGN channel: False
2025-06-12 19:01:51,819 - signal_handler - INFO - Calling direct signal callback for 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 19:01:51,821 - signal_handler - INFO - Direct signal processing completed for 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump
2025-06-12 19:01:51,822 - signal_handler - INFO - Signal forwarded to bot controller: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump from OxyZen Calls (confidence: 0.50)
2025-06-12 19:01:51,823 - signal_handler - INFO - Retrieved signal from queue: 5Fxk572o7JgkNhv4VbHt7DKfvKiBpapGr3fxjGd6pump from OxyZen Calls
2025-06-12 19:03:11,500 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$11.8K(+243.2%)**
*...
2025-06-12 19:03:11,501 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 19:03:11,502 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 19:03:11,503 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 19:03:11,504 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 19:03:11,506 - signal_handler - INFO - Found contract address with 'pump' suffix: 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump
2025-06-12 19:03:11,508 - signal_handler - INFO - Found contract address: 9CEASshtv1NTq5hRjANvZKrDMqy2mHwrrZiPRAAZtCUr
2025-06-12 19:03:11,509 - signal_handler - INFO - Using 'pump' address as highest priority: 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump
2025-06-12 19:03:11,510 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump
2025-06-12 19:03:11,510 - signal_handler - INFO - Detected GMGN format
2025-06-12 19:03:11,511 - signal_handler - INFO - Found token symbol: 11
2025-06-12 19:03:11,511 - signal_handler - INFO - Found FDV: 11.8K - 11.80 (+243.2%)
2025-06-12 19:03:11,512 - signal_handler - INFO - Detected GMGN channel signal for token: 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump
2025-06-12 19:03:11,513 - signal_handler - INFO - Extracted signal: Token=8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 19:03:11,513 - signal_handler - INFO - Added signal to queue: 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 19:03:11,514 - signal_handler - INFO - Calling direct signal callback for 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump
2025-06-12 19:03:11,518 - signal_handler - INFO - Direct signal processing completed for 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump
2025-06-12 19:03:11,519 - signal_handler - INFO - Signal forwarded to bot controller: 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 19:03:11,519 - signal_handler - INFO - Retrieved signal from queue: 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump from solana signal alert - gmgn
2025-06-12 19:03:11,522 - signal_handler - INFO - [msg_1749735191521_5179] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 19:03:11

 Token CA: 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKp...
2025-06-12 19:03:11,522 - signal_handler - INFO - [msg_1749735191521_5179] Message queued with normal priority
2025-06-12 19:03:18,503 - signal_handler - INFO - Message queue processor active with 1 messages pending
2025-06-12 19:03:18,503 - signal_handler - INFO - [msg_1749735191521_5179] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:03:19,443 - signal_handler - INFO - [msg_1749735191521_5179] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:03:19,560 - signal_handler - INFO - [msg_1749735191521_5179] Message sent directly successfully using entity
2025-06-12 19:03:19,561 - signal_handler - INFO - [msg_1749735191521_5179] Message sent successfully via direct send method
2025-06-12 19:03:19,561 - signal_handler - INFO - [msg_1749735191521_5179] Message sent successfully on attempt 1
2025-06-12 19:03:19,813 - signal_handler - INFO - [msg_1749735199811_6171] [SELL NOTIFICATION]: [GREEN] [SIM BUY EXECUTED] [?] 2025-06-12 19:03:19

 Signal Source: solana signal alert - gmgn
 Toke...
2025-06-12 19:03:19,818 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749735199811_6171.txt
2025-06-12 19:03:19,819 - signal_handler - INFO - [msg_1749735199811_6171] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:03:19,886 - signal_handler - INFO - [msg_1749735199811_6171] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:03:20,046 - signal_handler - INFO - [msg_1749735199811_6171] Message sent directly successfully using entity
2025-06-12 19:03:20,047 - signal_handler - INFO - [msg_1749735199811_6171] Message sent directly successfully
2025-06-12 19:03:20,048 - signal_handler - INFO - [msg_1749735200048_8817] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (SIMULATION) - 8QFX1Z7M

 Timestamp: 2025-06-12 19:03:20
[REPEAT] Tr...
2025-06-12 19:03:20,049 - signal_handler - INFO - [msg_1749735200048_8817] Message queued with normal priority
2025-06-12 19:03:20,101 - signal_handler - INFO - [msg_1749735200048_8817] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:03:20,168 - signal_handler - INFO - [msg_1749735200048_8817] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:03:20,297 - signal_handler - INFO - [msg_1749735200048_8817] Message sent directly successfully using entity
2025-06-12 19:03:20,298 - signal_handler - INFO - [msg_1749735200048_8817] Message sent successfully via direct send method
2025-06-12 19:03:20,299 - signal_handler - INFO - [msg_1749735200048_8817] Message sent successfully on attempt 1
2025-06-12 19:04:54,141 - signal_handler - INFO - Received message from channel -1002017173747: [ROCKET] NEW SOL HIGH VOLUME TOKEN [ROCKET]

            [TARGET] Token Symbol: $NALOR
             ...
2025-06-12 19:04:54,142 - signal_handler - INFO - Setting channel_identifier to 'SOL HIGH VOLUME ALERT | Bordga' based on chat_id -1002017173747
2025-06-12 19:04:54,143 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 19:04:54,144 - signal_handler - INFO - Found contract address with 'pump' suffix: 2cv2zFxfnR1Row8h1vBru8FM2HpFePB4a28PxmA6pump
2025-06-12 19:04:54,145 - signal_handler - INFO - Found contract address: 2y6bdvl7uuu9x2acrjuntbghbfeyrfym9qa5c2c8qbs5
2025-06-12 19:04:54,145 - signal_handler - INFO - Using 'pump' address as highest priority: 2cv2zFxfnR1Row8h1vBru8FM2HpFePB4a28PxmA6pump
2025-06-12 19:04:54,146 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 2cv2zFxfnR1Row8h1vBru8FM2HpFePB4a28PxmA6pump
2025-06-12 19:04:54,146 - signal_handler - INFO - Detected High Volume Alert format
2025-06-12 19:04:54,147 - signal_handler - INFO - Found token symbol: NALOR
2025-06-12 19:04:54,147 - signal_handler - INFO - Found market cap: 83K - 83.00K
2025-06-12 19:04:54,148 - signal_handler - INFO - Found age: 3 minutes
2025-06-12 19:04:54,148 - signal_handler - INFO - Found top holders: 17.76%
2025-06-12 19:04:54,149 - signal_handler - INFO - Found risk rating: Good
2025-06-12 19:04:54,149 - signal_handler - INFO - Detected signal from SOL HIGH VOLUME ALERT | Bordga for token: 2cv2zFxfnR1Row8h1vBru8FM2HpFePB4a28PxmA6pump
2025-06-12 19:04:54,150 - signal_handler - INFO - Extracted signal: Token=2cv2zFxfnR1Row8h1vBru8FM2HpFePB4a28PxmA6pump, Source=SOL HIGH VOLUME ALERT | Bordga, Metrics Count=7
2025-06-12 19:04:54,150 - signal_handler - INFO - Added signal to queue: 2cv2zFxfnR1Row8h1vBru8FM2HpFePB4a28PxmA6pump from SOL HIGH VOLUME ALERT | Bordga with confidence 0.5, GMGN channel: False
2025-06-12 19:04:54,151 - signal_handler - INFO - Calling direct signal callback for 2cv2zFxfnR1Row8h1vBru8FM2HpFePB4a28PxmA6pump
2025-06-12 19:04:54,155 - signal_handler - INFO - Direct signal processing completed for 2cv2zFxfnR1Row8h1vBru8FM2HpFePB4a28PxmA6pump
2025-06-12 19:04:54,156 - signal_handler - INFO - Signal forwarded to bot controller: 2cv2zFxfnR1Row8h1vBru8FM2HpFePB4a28PxmA6pump from SOL HIGH VOLUME ALERT | Bordga (confidence: 0.50)
2025-06-12 19:04:54,157 - signal_handler - INFO - Retrieved signal from queue: 2cv2zFxfnR1Row8h1vBru8FM2HpFePB4a28PxmA6pump from SOL HIGH VOLUME ALERT | Bordga
2025-06-12 19:04:54,160 - signal_handler - INFO - [msg_1749735294159_9561] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 19:04:54

 Token CA: 2cv2zFxfnR1Row8h1vBru8FM2HpFePB4a28PxmA6p...
2025-06-12 19:04:54,161 - signal_handler - INFO - [msg_1749735294159_9561] Message queued with normal priority
2025-06-12 19:04:57,291 - signal_handler - INFO - [msg_1749735294159_9561] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:04:58,139 - signal_handler - INFO - [msg_1749735294159_9561] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:04:58,251 - signal_handler - INFO - [msg_1749735294159_9561] Message sent directly successfully using entity
2025-06-12 19:04:58,251 - signal_handler - INFO - [msg_1749735294159_9561] Message sent successfully via direct send method
2025-06-12 19:04:58,252 - signal_handler - INFO - [msg_1749735294159_9561] Message sent successfully on attempt 1
2025-06-12 19:05:00,369 - signal_handler - INFO - Received sell notification: [PURPLE] [SIM SELL [?] TP1 HIT (29.0% >= 20.0%)] [?] 2025-06-12 19:05:00
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]
[?] Trig...
2025-06-12 19:05:00,372 - signal_handler - INFO - [msg_1749735300368_2745] [SELL NOTIFICATION]:  [SIM SELL [?] TP1 HIT (29.0% >= 20.0%)] [?] 2025-06-12 19:05:00

 Triggered by: SOL HIGH VOLUME ALE...
2025-06-12 19:05:00,376 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749735300368_2745.txt
2025-06-12 19:05:00,378 - signal_handler - INFO - [msg_1749735300368_2745] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:05:00,438 - signal_handler - INFO - [msg_1749735300368_2745] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:05:00,551 - signal_handler - INFO - [msg_1749735300368_2745] Message sent directly successfully using entity
2025-06-12 19:05:00,551 - signal_handler - INFO - [msg_1749735300368_2745] Message sent directly successfully
2025-06-12 19:05:00,552 - signal_handler - INFO - [msg_1749735300552_1956] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-12 19:05:00
[REPEAT] Trade: SIMULATION

[MONEY...
2025-06-12 19:05:00,553 - signal_handler - INFO - [msg_1749735300552_1956] Message queued with normal priority
2025-06-12 19:05:01,598 - signal_handler - INFO - [msg_1749735300552_1956] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:05:01,611 - signal_handler - INFO - [msg_1749735301609_1963] [SELL NOTIFICATION]: [GREEN] [SIM BUY EXECUTED] [?] 2025-06-12 19:05:01

 Signal Source: SOL HIGH VOLUME ALERT | Bordga
 ...
2025-06-12 19:05:01,616 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749735301609_1963.txt
2025-06-12 19:05:01,617 - signal_handler - INFO - [msg_1749735301609_1963] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:05:01,677 - signal_handler - INFO - [msg_1749735300552_1956] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:05:01,690 - signal_handler - INFO - [msg_1749735301609_1963] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:05:01,784 - signal_handler - INFO - [msg_1749735300552_1956] Message sent directly successfully using entity
2025-06-12 19:05:01,784 - signal_handler - INFO - [msg_1749735300552_1956] Message sent successfully via direct send method
2025-06-12 19:05:01,785 - signal_handler - INFO - [msg_1749735300552_1956] Message sent successfully on attempt 1
2025-06-12 19:05:01,817 - signal_handler - INFO - [msg_1749735301609_1963] Message sent directly successfully using entity
2025-06-12 19:05:01,818 - signal_handler - INFO - [msg_1749735301609_1963] Message sent directly successfully
2025-06-12 19:05:01,819 - signal_handler - INFO - [msg_1749735301819_7500] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (SIMULATION) - 2CV2ZFXF

 Timestamp: 2025-06-12 19:05:01
[REPEAT] Tr...
2025-06-12 19:05:01,820 - signal_handler - INFO - [msg_1749735301819_7500] Message queued with normal priority
2025-06-12 19:05:01,889 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.40s (interval: 0.50s)
2025-06-12 19:05:02,290 - signal_handler - INFO - [msg_1749735301819_7500] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:05:02,371 - signal_handler - INFO - [msg_1749735301819_7500] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:05:02,472 - signal_handler - INFO - [msg_1749735301819_7500] Message sent directly successfully using entity
2025-06-12 19:05:02,472 - signal_handler - INFO - [msg_1749735301819_7500] Message sent successfully via direct send method
2025-06-12 19:05:02,473 - signal_handler - INFO - [msg_1749735301819_7500] Message sent successfully on attempt 1
2025-06-12 19:05:25,849 - signal_handler - INFO - Received sell notification: [PURPLE] [SIM SELL [?] TP1 HIT (47.0% >= 20.0%)] [?] 2025-06-12 19:05:25
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]
[?] Trig...
2025-06-12 19:05:25,853 - signal_handler - INFO - [msg_1749735325849_9473] [SELL NOTIFICATION]:  [SIM SELL [?] TP1 HIT (47.0% >= 20.0%)] [?] 2025-06-12 19:05:25

 Triggered by: solana signal alert...
2025-06-12 19:05:25,857 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749735325849_9473.txt
2025-06-12 19:05:25,859 - signal_handler - INFO - [msg_1749735325849_9473] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:05:26,019 - signal_handler - INFO - [msg_1749735325849_9473] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:05:26,222 - signal_handler - INFO - [msg_1749735325849_9473] Message sent directly successfully using entity
2025-06-12 19:05:26,223 - signal_handler - INFO - [msg_1749735325849_9473] Message sent directly successfully
2025-06-12 19:05:26,225 - signal_handler - INFO - [msg_1749735326225_7433] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-12 19:05:26
[REPEAT] Trade: SIMULATION

[MONEY...
2025-06-12 19:05:26,226 - signal_handler - INFO - [msg_1749735326225_7433] Message queued with normal priority
2025-06-12 19:05:26,315 - signal_handler - INFO - Message queue processor active with 1 messages pending
2025-06-12 19:05:26,316 - signal_handler - INFO - [msg_1749735326225_7433] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:05:26,426 - signal_handler - INFO - [msg_1749735326225_7433] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:05:26,610 - signal_handler - INFO - [msg_1749735326225_7433] Message sent directly successfully using entity
2025-06-12 19:05:26,611 - signal_handler - INFO - [msg_1749735326225_7433] Message sent successfully via direct send method
2025-06-12 19:05:26,611 - signal_handler - INFO - [msg_1749735326225_7433] Message sent successfully on attempt 1
2025-06-12 19:06:54,563 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$13.3K(+265.5%)**
*...
2025-06-12 19:06:54,564 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 19:06:54,565 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 19:06:54,566 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 19:06:54,567 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 19:06:54,568 - signal_handler - INFO - Found contract address with 'pump' suffix: CUk5B8ay3xJKiiKBe2rjYqjL6p9LkmYfqGaWBUdtpump
2025-06-12 19:06:54,569 - signal_handler - INFO - Found contract address: 3HSZJXYjWJd9eXa4qgD3xYzq273usCdgFUtkVNNUY5MF
2025-06-12 19:06:54,570 - signal_handler - INFO - Using 'pump' address as highest priority: CUk5B8ay3xJKiiKBe2rjYqjL6p9LkmYfqGaWBUdtpump
2025-06-12 19:06:54,571 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: CUk5B8ay3xJKiiKBe2rjYqjL6p9LkmYfqGaWBUdtpump
2025-06-12 19:06:54,573 - signal_handler - INFO - Detected GMGN format
2025-06-12 19:06:54,573 - signal_handler - INFO - Found token symbol: 13
2025-06-12 19:06:54,574 - signal_handler - INFO - Found FDV: 13.3K - 13.30 (+265.5%)
2025-06-12 19:06:54,575 - signal_handler - INFO - Detected GMGN channel signal for token: CUk5B8ay3xJKiiKBe2rjYqjL6p9LkmYfqGaWBUdtpump
2025-06-12 19:06:54,576 - signal_handler - INFO - Extracted signal: Token=CUk5B8ay3xJKiiKBe2rjYqjL6p9LkmYfqGaWBUdtpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 19:06:54,577 - signal_handler - INFO - Signal processing health: 28 received, 28 processed, 0 dropped (100.0% success rate)
2025-06-12 19:06:54,579 - signal_handler - INFO - Added signal to queue: CUk5B8ay3xJKiiKBe2rjYqjL6p9LkmYfqGaWBUdtpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 19:06:54,580 - signal_handler - INFO - Calling direct signal callback for CUk5B8ay3xJKiiKBe2rjYqjL6p9LkmYfqGaWBUdtpump
2025-06-12 19:06:54,587 - signal_handler - INFO - Direct signal processing completed for CUk5B8ay3xJKiiKBe2rjYqjL6p9LkmYfqGaWBUdtpump
2025-06-12 19:06:54,588 - signal_handler - INFO - Signal forwarded to bot controller: CUk5B8ay3xJKiiKBe2rjYqjL6p9LkmYfqGaWBUdtpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 19:06:54,589 - signal_handler - INFO - Retrieved signal from queue: CUk5B8ay3xJKiiKBe2rjYqjL6p9LkmYfqGaWBUdtpump from solana signal alert - gmgn
2025-06-12 19:06:54,591 - signal_handler - INFO - [msg_1749735414591_5812] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 19:06:54

 Token CA: CUk5B8ay3xJKiiKBe2rjYqjL6p9LkmYfqGaWBUdtp...
2025-06-12 19:06:54,592 - signal_handler - INFO - [msg_1749735414591_5812] Message queued with normal priority
2025-06-12 19:07:01,579 - signal_handler - INFO - [msg_1749735421579_3934] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW LIQUIDITY] [?] 2025-06-12 19:07:01

 Token: CUk5B8ay3xJKiiKBe2rjYqjL6p...
2025-06-12 19:07:01,579 - signal_handler - INFO - [msg_1749735421579_3934] Message queued with normal priority
2025-06-12 19:07:01,580 - signal_handler - INFO - [msg_1749735414591_5812] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:07:01,723 - signal_handler - INFO - [msg_1749735414591_5812] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:07:01,843 - signal_handler - INFO - [msg_1749735414591_5812] Message sent directly successfully using entity
2025-06-12 19:07:01,845 - signal_handler - INFO - [msg_1749735414591_5812] Message sent successfully via direct send method
2025-06-12 19:07:01,847 - signal_handler - INFO - [msg_1749735414591_5812] Message sent successfully on attempt 1
2025-06-12 19:07:01,847 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 19:07:02,363 - signal_handler - INFO - [msg_1749735421579_3934] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:07:02,423 - signal_handler - INFO - [msg_1749735421579_3934] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:07:02,533 - signal_handler - INFO - [msg_1749735421579_3934] Message sent directly successfully using entity
2025-06-12 19:07:02,535 - signal_handler - INFO - [msg_1749735421579_3934] Message sent successfully via direct send method
2025-06-12 19:07:02,536 - signal_handler - INFO - [msg_1749735421579_3934] Message sent successfully on attempt 1
2025-06-12 19:07:44,804 - signal_handler - INFO - Received sell notification: [PURPLE] [SIM SELL [?] STOP LOSS HIT (-20.9% <= -20.0%)] [?] 2025-06-12 19:07:44
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]...
2025-06-12 19:07:44,807 - signal_handler - INFO - [msg_1749735464804_1262] [SELL NOTIFICATION]:  [SIM SELL [?] STOP LOSS HIT (-20.9% <= -20.0%)] [?] 2025-06-12 19:07:44

 Triggered by: SOL HIGH VO...
2025-06-12 19:07:44,811 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749735464804_1262.txt
2025-06-12 19:07:44,813 - signal_handler - INFO - [msg_1749735464804_1262] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:07:44,884 - signal_handler - INFO - [msg_1749735464804_1262] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:07:45,018 - signal_handler - INFO - [msg_1749735464804_1262] Message sent directly successfully using entity
2025-06-12 19:07:45,019 - signal_handler - INFO - [msg_1749735464804_1262] Message sent directly successfully
2025-06-12 19:07:45,020 - signal_handler - INFO - [msg_1749735465020_5984] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-12 19:07:45
[REPEAT] Trade: SIMULATION

[MONEY...
2025-06-12 19:07:45,021 - signal_handler - INFO - [msg_1749735465020_5984] Message queued with normal priority
2025-06-12 19:07:45,041 - signal_handler - INFO - [msg_1749735465020_5984] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:07:45,098 - signal_handler - INFO - [msg_1749735465020_5984] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:07:45,209 - signal_handler - INFO - [msg_1749735465020_5984] Message sent directly successfully using entity
2025-06-12 19:07:45,210 - signal_handler - INFO - [msg_1749735465020_5984] Message sent successfully via direct send method
2025-06-12 19:07:45,211 - signal_handler - INFO - [msg_1749735465020_5984] Message sent successfully on attempt 1
2025-06-12 19:11:11,253 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$10.2K(+216.5%)**
*...
2025-06-12 19:11:11,254 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 19:11:11,255 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 19:11:11,256 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 19:11:11,257 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 19:11:11,259 - signal_handler - INFO - Found contract address with 'pump' suffix: 9bdBmzqLkWojreaz5VuxkdEyaTsZEuP6zABZ9KFopump
2025-06-12 19:11:11,260 - signal_handler - INFO - Found contract address: H4VAZzcmGfDoafu6sqELMc4pWkuC7C397Ndq8pkC5fDR
2025-06-12 19:11:11,261 - signal_handler - INFO - Using 'pump' address as highest priority: 9bdBmzqLkWojreaz5VuxkdEyaTsZEuP6zABZ9KFopump
2025-06-12 19:11:11,261 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 9bdBmzqLkWojreaz5VuxkdEyaTsZEuP6zABZ9KFopump
2025-06-12 19:11:11,263 - signal_handler - INFO - Detected GMGN format
2025-06-12 19:11:11,264 - signal_handler - INFO - Found token symbol: 10
2025-06-12 19:11:11,265 - signal_handler - INFO - Found FDV: 10.2K - 10.20 (+216.5%)
2025-06-12 19:11:11,266 - signal_handler - INFO - Detected GMGN channel signal for token: 9bdBmzqLkWojreaz5VuxkdEyaTsZEuP6zABZ9KFopump
2025-06-12 19:11:11,267 - signal_handler - INFO - Extracted signal: Token=9bdBmzqLkWojreaz5VuxkdEyaTsZEuP6zABZ9KFopump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 19:11:11,268 - signal_handler - INFO - Added signal to queue: 9bdBmzqLkWojreaz5VuxkdEyaTsZEuP6zABZ9KFopump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 19:11:11,269 - signal_handler - INFO - Calling direct signal callback for 9bdBmzqLkWojreaz5VuxkdEyaTsZEuP6zABZ9KFopump
2025-06-12 19:11:11,273 - signal_handler - INFO - Direct signal processing completed for 9bdBmzqLkWojreaz5VuxkdEyaTsZEuP6zABZ9KFopump
2025-06-12 19:11:11,274 - signal_handler - INFO - Signal forwarded to bot controller: 9bdBmzqLkWojreaz5VuxkdEyaTsZEuP6zABZ9KFopump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 19:11:11,274 - signal_handler - INFO - Retrieved signal from queue: 9bdBmzqLkWojreaz5VuxkdEyaTsZEuP6zABZ9KFopump from solana signal alert - gmgn
2025-06-12 19:11:11,276 - signal_handler - INFO - [msg_1749735671276_1094] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 19:11:11

 Token CA: 9bdBmzqLkWojreaz5VuxkdEyaTsZEuP6zABZ9KFop...
2025-06-12 19:11:11,277 - signal_handler - INFO - [msg_1749735671276_1094] Message queued with normal priority
2025-06-12 19:11:18,538 - signal_handler - INFO - [msg_1749735678538_3024] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW LIQUIDITY] [?] 2025-06-12 19:11:18

 Token: 9bdBmzqLkWojreaz5VuxkdEyaT...
2025-06-12 19:11:18,539 - signal_handler - INFO - [msg_1749735678538_3024] Message queued with normal priority
2025-06-12 19:11:18,540 - signal_handler - INFO - [msg_1749735671276_1094] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:11:18,598 - signal_handler - INFO - [msg_1749735671276_1094] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:11:18,714 - signal_handler - INFO - [msg_1749735671276_1094] Message sent directly successfully using entity
2025-06-12 19:11:18,715 - signal_handler - INFO - [msg_1749735671276_1094] Message sent successfully via direct send method
2025-06-12 19:11:18,717 - signal_handler - INFO - [msg_1749735671276_1094] Message sent successfully on attempt 1
2025-06-12 19:11:18,717 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 19:11:19,226 - signal_handler - INFO - [msg_1749735678538_3024] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:11:19,300 - signal_handler - INFO - [msg_1749735678538_3024] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:11:19,439 - signal_handler - INFO - [msg_1749735678538_3024] Message sent directly successfully using entity
2025-06-12 19:11:19,440 - signal_handler - INFO - [msg_1749735678538_3024] Message sent successfully via direct send method
2025-06-12 19:11:19,441 - signal_handler - INFO - [msg_1749735678538_3024] Message sent successfully on attempt 1
2025-06-12 19:12:08,213 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$11.2K(+228%)**
**[...
2025-06-12 19:12:08,215 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 19:12:08,216 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 19:12:08,216 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 19:12:08,217 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 19:12:08,219 - signal_handler - INFO - Found contract address with 'pump' suffix: 8FhCi7HRtWbUL8BeSyn1TKQxgEoDc1bXh9ZHXDMApump
2025-06-12 19:12:08,220 - signal_handler - INFO - Found contract address: CyGCFFof7GKB6go4uQVzX9Hq8gJZoNsJMKEeR1pPdLf
2025-06-12 19:12:08,221 - signal_handler - INFO - Using 'pump' address as highest priority: 8FhCi7HRtWbUL8BeSyn1TKQxgEoDc1bXh9ZHXDMApump
2025-06-12 19:12:08,222 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 8FhCi7HRtWbUL8BeSyn1TKQxgEoDc1bXh9ZHXDMApump
2025-06-12 19:12:08,224 - signal_handler - INFO - Detected GMGN format
2025-06-12 19:12:08,225 - signal_handler - INFO - Found token symbol: 11
2025-06-12 19:12:08,226 - signal_handler - INFO - Found FDV: 11.2K - 11.20 (+228%)
2025-06-12 19:12:08,227 - signal_handler - INFO - Detected GMGN channel signal for token: 8FhCi7HRtWbUL8BeSyn1TKQxgEoDc1bXh9ZHXDMApump
2025-06-12 19:12:08,227 - signal_handler - INFO - Extracted signal: Token=8FhCi7HRtWbUL8BeSyn1TKQxgEoDc1bXh9ZHXDMApump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 19:12:08,228 - signal_handler - INFO - Added signal to queue: 8FhCi7HRtWbUL8BeSyn1TKQxgEoDc1bXh9ZHXDMApump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 19:12:08,229 - signal_handler - INFO - Calling direct signal callback for 8FhCi7HRtWbUL8BeSyn1TKQxgEoDc1bXh9ZHXDMApump
2025-06-12 19:12:08,236 - signal_handler - INFO - Direct signal processing completed for 8FhCi7HRtWbUL8BeSyn1TKQxgEoDc1bXh9ZHXDMApump
2025-06-12 19:12:08,237 - signal_handler - INFO - Signal forwarded to bot controller: 8FhCi7HRtWbUL8BeSyn1TKQxgEoDc1bXh9ZHXDMApump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 19:12:08,238 - signal_handler - INFO - Retrieved signal from queue: 8FhCi7HRtWbUL8BeSyn1TKQxgEoDc1bXh9ZHXDMApump from solana signal alert - gmgn
2025-06-12 19:12:08,242 - signal_handler - INFO - [msg_1749735728241_2855] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 19:12:08

 Token CA: 8FhCi7HRtWbUL8BeSyn1TKQxgEoDc1bXh9ZHXDMAp...
2025-06-12 19:12:08,242 - signal_handler - INFO - [msg_1749735728241_2855] Message queued with normal priority
2025-06-12 19:12:15,744 - signal_handler - INFO - [msg_1749735735744_9327] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW LIQUIDITY] [?] 2025-06-12 19:12:15

 Token: 8FhCi7HRtWbUL8BeSyn1TKQxgE...
2025-06-12 19:12:15,745 - signal_handler - INFO - [msg_1749735735744_9327] Message queued with normal priority
2025-06-12 19:12:15,746 - signal_handler - INFO - [msg_1749735728241_2855] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:12:15,802 - signal_handler - INFO - [msg_1749735728241_2855] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:12:15,908 - signal_handler - INFO - [msg_1749735728241_2855] Message sent directly successfully using entity
2025-06-12 19:12:15,909 - signal_handler - INFO - [msg_1749735728241_2855] Message sent successfully via direct send method
2025-06-12 19:12:15,910 - signal_handler - INFO - [msg_1749735728241_2855] Message sent successfully on attempt 1
2025-06-12 19:12:15,910 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 19:12:16,421 - signal_handler - INFO - [msg_1749735735744_9327] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:12:16,477 - signal_handler - INFO - [msg_1749735735744_9327] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:12:16,576 - signal_handler - INFO - [msg_1749735735744_9327] Message sent directly successfully using entity
2025-06-12 19:12:16,577 - signal_handler - INFO - [msg_1749735735744_9327] Message sent successfully via direct send method
2025-06-12 19:12:16,578 - signal_handler - INFO - [msg_1749735735744_9327] Message sent successfully on attempt 1
2025-06-12 19:13:17,082 - signal_handler - INFO - Received message from channel -1002017173747: [STONKS] The $EVERYTHING JUST MADE 2X [STONKS]

        [TARGET] Ticker: $EVERYTHING
         Called...
2025-06-12 19:13:17,084 - signal_handler - INFO - Setting channel_identifier to 'SOL HIGH VOLUME ALERT | Bordga' based on chat_id -1002017173747
2025-06-12 19:13:17,085 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 19:13:17,087 - signal_handler - INFO - Found contract address with 'pump' suffix: BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump
2025-06-12 19:13:17,088 - signal_handler - INFO - Found contract address: cx4tpbyqewxahji4ab6rzox28pmaq12pn4mzqrja7juz
2025-06-12 19:13:17,089 - signal_handler - INFO - Using 'pump' address as highest priority: BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump
2025-06-12 19:13:17,090 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump
2025-06-12 19:13:17,091 - signal_handler - INFO - Detected signal from SOL HIGH VOLUME ALERT | Bordga for token: BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump
2025-06-12 19:13:17,092 - signal_handler - INFO - Extracted signal: Token=BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump, Source=SOL HIGH VOLUME ALERT | Bordga, Metrics Count=1
2025-06-12 19:13:17,092 - signal_handler - INFO - Using shorter cooldown (1s) for pump token: BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump
2025-06-12 19:13:17,093 - signal_handler - INFO - Token BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump cooldown expired. Processing signal.
2025-06-12 19:13:17,094 - signal_handler - INFO - Added signal to queue: BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump from SOL HIGH VOLUME ALERT | Bordga with confidence 0.5, GMGN channel: False
2025-06-12 19:13:17,094 - signal_handler - INFO - Calling direct signal callback for BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump
2025-06-12 19:13:17,098 - signal_handler - INFO - Direct signal processing completed for BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump
2025-06-12 19:13:17,098 - signal_handler - INFO - Signal forwarded to bot controller: BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump from SOL HIGH VOLUME ALERT | Bordga (confidence: 0.50)
2025-06-12 19:13:17,099 - signal_handler - INFO - Retrieved signal from queue: BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcpump from SOL HIGH VOLUME ALERT | Bordga
2025-06-12 19:13:17,101 - signal_handler - INFO - [msg_1749735797100_4804] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 19:13:17

 Token CA: BsfhyL5JpnWEFBKTBx8xhQdAZvLM6pdgRSGXGURcp...
2025-06-12 19:13:17,101 - signal_handler - INFO - [msg_1749735797100_4804] Message queued with normal priority
2025-06-12 19:13:17,137 - signal_handler - INFO - [msg_1749735797100_4804] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:13:17,197 - signal_handler - INFO - [msg_1749735797100_4804] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:13:17,307 - signal_handler - INFO - [msg_1749735797100_4804] Message sent directly successfully using entity
2025-06-12 19:13:17,309 - signal_handler - INFO - [msg_1749735797100_4804] Message sent successfully via direct send method
2025-06-12 19:13:17,310 - signal_handler - INFO - [msg_1749735797100_4804] Message sent successfully on attempt 1
2025-06-12 19:13:20,040 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$9673.0719(+201.1%)...
2025-06-12 19:13:20,041 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 19:13:20,042 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 19:13:20,043 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 19:13:20,044 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 19:13:20,046 - signal_handler - INFO - Found contract address with 'pump' suffix: CJm6zzDThaKDEWXwubBKqaW1q2FcNcYPJiJLm2tZpump
2025-06-12 19:13:20,047 - signal_handler - INFO - Found contract address: F2H1CWW2mRMZJoVMsvhRDpHKSA2TbFtEkmPmt87VSYXh
2025-06-12 19:13:20,048 - signal_handler - INFO - Using 'pump' address as highest priority: CJm6zzDThaKDEWXwubBKqaW1q2FcNcYPJiJLm2tZpump
2025-06-12 19:13:20,049 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: CJm6zzDThaKDEWXwubBKqaW1q2FcNcYPJiJLm2tZpump
2025-06-12 19:13:20,050 - signal_handler - INFO - Detected GMGN format
2025-06-12 19:13:20,051 - signal_handler - INFO - Found token symbol: Aural
2025-06-12 19:13:20,052 - signal_handler - INFO - Found FDV: 9673.0719 - 9.67K (+201.1%)
2025-06-12 19:13:20,053 - signal_handler - INFO - Detected GMGN channel signal for token: CJm6zzDThaKDEWXwubBKqaW1q2FcNcYPJiJLm2tZpump
2025-06-12 19:13:20,054 - signal_handler - INFO - Extracted signal: Token=CJm6zzDThaKDEWXwubBKqaW1q2FcNcYPJiJLm2tZpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 19:13:20,055 - signal_handler - INFO - Added signal to queue: CJm6zzDThaKDEWXwubBKqaW1q2FcNcYPJiJLm2tZpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 19:13:20,056 - signal_handler - INFO - Calling direct signal callback for CJm6zzDThaKDEWXwubBKqaW1q2FcNcYPJiJLm2tZpump
2025-06-12 19:13:20,062 - signal_handler - INFO - Direct signal processing completed for CJm6zzDThaKDEWXwubBKqaW1q2FcNcYPJiJLm2tZpump
2025-06-12 19:13:20,062 - signal_handler - INFO - Signal forwarded to bot controller: CJm6zzDThaKDEWXwubBKqaW1q2FcNcYPJiJLm2tZpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 19:13:20,063 - signal_handler - INFO - Retrieved signal from queue: CJm6zzDThaKDEWXwubBKqaW1q2FcNcYPJiJLm2tZpump from solana signal alert - gmgn
2025-06-12 19:13:20,065 - signal_handler - INFO - [msg_1749735800064_1201] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 19:13:20

 Token CA: CJm6zzDThaKDEWXwubBKqaW1q2FcNcYPJiJLm2tZp...
2025-06-12 19:13:20,065 - signal_handler - INFO - [msg_1749735800064_1201] Message queued with normal priority
2025-06-12 19:13:26,904 - signal_handler - INFO - [msg_1749735806904_7550] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW LIQUIDITY] [?] 2025-06-12 19:13:26

 Token: CJm6zzDThaKDEWXwubBKqaW1q2...
2025-06-12 19:13:26,905 - signal_handler - INFO - [msg_1749735806904_7550] Message queued with normal priority
2025-06-12 19:13:26,906 - signal_handler - INFO - [msg_1749735800064_1201] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:13:26,964 - signal_handler - INFO - [msg_1749735800064_1201] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:13:27,067 - signal_handler - INFO - [msg_1749735800064_1201] Message sent directly successfully using entity
2025-06-12 19:13:27,068 - signal_handler - INFO - [msg_1749735800064_1201] Message sent successfully via direct send method
2025-06-12 19:13:27,069 - signal_handler - INFO - [msg_1749735800064_1201] Message sent successfully on attempt 1
2025-06-12 19:13:27,070 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 19:13:27,576 - signal_handler - INFO - [msg_1749735806904_7550] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:13:27,638 - signal_handler - INFO - [msg_1749735806904_7550] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:13:27,741 - signal_handler - INFO - [msg_1749735806904_7550] Message sent directly successfully using entity
2025-06-12 19:13:27,742 - signal_handler - INFO - [msg_1749735806904_7550] Message sent successfully via direct send method
2025-06-12 19:13:27,743 - signal_handler - INFO - [msg_1749735806904_7550] Message sent successfully on attempt 1
2025-06-12 19:14:48,937 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$31.4K(+229.8%)**
*...
2025-06-12 19:14:48,939 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 19:14:48,940 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 19:14:48,940 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 19:14:48,941 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 19:14:48,943 - signal_handler - INFO - Found contract address with 'pump' suffix: 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump
2025-06-12 19:14:48,944 - signal_handler - INFO - Found contract address: 9CEASshtv1NTq5hRjANvZKrDMqy2mHwrrZiPRAAZtCUr
2025-06-12 19:14:48,945 - signal_handler - INFO - Using 'pump' address as highest priority: 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump
2025-06-12 19:14:48,946 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump
2025-06-12 19:14:48,947 - signal_handler - INFO - Detected GMGN format
2025-06-12 19:14:48,948 - signal_handler - INFO - Found token symbol: 31
2025-06-12 19:14:48,949 - signal_handler - INFO - Found FDV: 31.4K - 31.40 (+229.8%)
2025-06-12 19:14:48,950 - signal_handler - INFO - Detected GMGN channel signal for token: 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump
2025-06-12 19:14:48,950 - signal_handler - INFO - Extracted signal: Token=8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 19:14:48,951 - signal_handler - INFO - Using shorter cooldown (1s) for pump token: 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump
2025-06-12 19:14:48,951 - signal_handler - INFO - Token 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump cooldown expired. Processing signal.
2025-06-12 19:14:48,952 - signal_handler - INFO - Added signal to queue: 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 19:14:48,952 - signal_handler - INFO - Calling direct signal callback for 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump
2025-06-12 19:14:48,956 - signal_handler - INFO - Direct signal processing completed for 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump
2025-06-12 19:14:48,956 - signal_handler - INFO - Signal forwarded to bot controller: 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 19:14:48,957 - signal_handler - INFO - Retrieved signal from queue: 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKpump from solana signal alert - gmgn
2025-06-12 19:14:48,959 - signal_handler - INFO - [msg_1749735888958_6349] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 19:14:48

 Token CA: 8qfx1z7mdfNEE6kMpxW4BoZSD25N4JPyqMgJJoQKp...
2025-06-12 19:14:48,959 - signal_handler - INFO - [msg_1749735888958_6349] Message queued with normal priority
2025-06-12 19:14:48,963 - signal_handler - INFO - [msg_1749735888958_6349] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:14:49,021 - signal_handler - INFO - [msg_1749735888958_6349] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:14:49,147 - signal_handler - INFO - [msg_1749735888958_6349] Message sent directly successfully using entity
2025-06-12 19:14:49,148 - signal_handler - INFO - [msg_1749735888958_6349] Message sent successfully via direct send method
2025-06-12 19:14:49,149 - signal_handler - INFO - [msg_1749735888958_6349] Message sent successfully on attempt 1
2025-06-12 19:15:36,236 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$11.4K(+248%)**
**[...
2025-06-12 19:15:36,237 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 19:15:36,238 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 19:15:36,239 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 19:15:36,240 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 19:15:36,241 - signal_handler - INFO - Found contract address with 'pump' suffix: 5WKEsrh6typkYA2FXQT24iYnCFn7zgoWhjTX2daNpump
2025-06-12 19:15:36,242 - signal_handler - INFO - Found contract address: H6ctQWxrXg6ggNutnnLSJPJwQZgKcHXHnGSBw5JaYEBh
2025-06-12 19:15:36,243 - signal_handler - INFO - Using 'pump' address as highest priority: 5WKEsrh6typkYA2FXQT24iYnCFn7zgoWhjTX2daNpump
2025-06-12 19:15:36,244 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 5WKEsrh6typkYA2FXQT24iYnCFn7zgoWhjTX2daNpump
2025-06-12 19:15:36,246 - signal_handler - INFO - Detected GMGN format
2025-06-12 19:15:36,247 - signal_handler - INFO - Found token symbol: 11
2025-06-12 19:15:36,248 - signal_handler - INFO - Found FDV: 11.4K - 11.40 (+248%)
2025-06-12 19:15:36,249 - signal_handler - INFO - Detected GMGN channel signal for token: 5WKEsrh6typkYA2FXQT24iYnCFn7zgoWhjTX2daNpump
2025-06-12 19:15:36,250 - signal_handler - INFO - Extracted signal: Token=5WKEsrh6typkYA2FXQT24iYnCFn7zgoWhjTX2daNpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 19:15:36,251 - signal_handler - INFO - Added signal to queue: 5WKEsrh6typkYA2FXQT24iYnCFn7zgoWhjTX2daNpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 19:15:36,252 - signal_handler - INFO - Calling direct signal callback for 5WKEsrh6typkYA2FXQT24iYnCFn7zgoWhjTX2daNpump
2025-06-12 19:15:36,259 - signal_handler - INFO - Direct signal processing completed for 5WKEsrh6typkYA2FXQT24iYnCFn7zgoWhjTX2daNpump
2025-06-12 19:15:36,260 - signal_handler - INFO - Signal forwarded to bot controller: 5WKEsrh6typkYA2FXQT24iYnCFn7zgoWhjTX2daNpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 19:15:36,261 - signal_handler - INFO - Retrieved signal from queue: 5WKEsrh6typkYA2FXQT24iYnCFn7zgoWhjTX2daNpump from solana signal alert - gmgn
2025-06-12 19:15:36,262 - signal_handler - INFO - [msg_1749735936262_3247] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 19:15:36

 Token CA: 5WKEsrh6typkYA2FXQT24iYnCFn7zgoWhjTX2daNp...
2025-06-12 19:15:36,263 - signal_handler - INFO - [msg_1749735936262_3247] Message queued with normal priority
2025-06-12 19:15:43,223 - signal_handler - INFO - [msg_1749735936262_3247] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:15:43,384 - signal_handler - INFO - [msg_1749735936262_3247] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:15:43,489 - signal_handler - INFO - [msg_1749735936262_3247] Message sent directly successfully using entity
2025-06-12 19:15:43,490 - signal_handler - INFO - [msg_1749735936262_3247] Message sent successfully via direct send method
2025-06-12 19:15:43,491 - signal_handler - INFO - [msg_1749735936262_3247] Message sent successfully on attempt 1
2025-06-12 19:15:43,724 - signal_handler - INFO - [msg_1749735943721_6446] [SELL NOTIFICATION]: [GREEN] [SIM BUY EXECUTED] [?] 2025-06-12 19:15:43

 Signal Source: solana signal alert - gmgn
 Toke...
2025-06-12 19:15:43,729 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749735943721_6446.txt
2025-06-12 19:15:43,730 - signal_handler - INFO - [msg_1749735943721_6446] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:15:43,785 - signal_handler - INFO - [msg_1749735943721_6446] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:15:43,884 - signal_handler - INFO - [msg_1749735943721_6446] Message sent directly successfully using entity
2025-06-12 19:15:43,885 - signal_handler - INFO - [msg_1749735943721_6446] Message sent directly successfully
2025-06-12 19:15:43,886 - signal_handler - INFO - [msg_1749735943885_8287] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (SIMULATION) - 5WKESRH6

 Timestamp: 2025-06-12 19:15:43
[REPEAT] Tr...
2025-06-12 19:15:43,886 - signal_handler - INFO - [msg_1749735943885_8287] Message queued with normal priority
2025-06-12 19:15:43,922 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.07s (interval: 0.50s)
2025-06-12 19:15:44,000 - signal_handler - INFO - [msg_1749735943885_8287] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:15:44,057 - signal_handler - INFO - [msg_1749735943885_8287] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:15:44,157 - signal_handler - INFO - [msg_1749735943885_8287] Message sent directly successfully using entity
2025-06-12 19:15:44,158 - signal_handler - INFO - [msg_1749735943885_8287] Message sent successfully via direct send method
2025-06-12 19:15:44,159 - signal_handler - INFO - [msg_1749735943885_8287] Message sent successfully on attempt 1
2025-06-12 19:16:47,368 - signal_handler - INFO - Received sell notification: [PURPLE] [SIM SELL [?] STOP LOSS HIT (-24.9% <= -20.0%)] [?] 2025-06-12 19:16:47
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]...
2025-06-12 19:16:47,371 - signal_handler - INFO - [msg_1749736007368_2849] [SELL NOTIFICATION]:  [SIM SELL [?] STOP LOSS HIT (-24.9% <= -20.0%)] [?] 2025-06-12 19:16:47

 Triggered by: solana sign...
2025-06-12 19:16:47,376 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749736007368_2849.txt
2025-06-12 19:16:47,378 - signal_handler - INFO - [msg_1749736007368_2849] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:16:47,463 - signal_handler - INFO - [msg_1749736007368_2849] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:16:47,669 - signal_handler - INFO - [msg_1749736007368_2849] Message sent directly successfully using entity
2025-06-12 19:16:47,670 - signal_handler - INFO - [msg_1749736007368_2849] Message sent directly successfully
2025-06-12 19:16:47,673 - signal_handler - INFO - [msg_1749736007673_6593] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-12 19:16:47
[REPEAT] Trade: SIMULATION

[MONEY...
2025-06-12 19:16:47,675 - signal_handler - INFO - [msg_1749736007673_6593] Message queued with normal priority
2025-06-12 19:16:47,712 - signal_handler - INFO - Message queue processor active with 1 messages pending
2025-06-12 19:16:47,714 - signal_handler - INFO - [msg_1749736007673_6593] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:16:47,771 - signal_handler - INFO - [msg_1749736007673_6593] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:16:47,875 - signal_handler - INFO - [msg_1749736007673_6593] Message sent directly successfully using entity
2025-06-12 19:16:47,876 - signal_handler - INFO - [msg_1749736007673_6593] Message sent successfully via direct send method
2025-06-12 19:16:47,877 - signal_handler - INFO - [msg_1749736007673_6593] Message sent successfully on attempt 1
2025-06-12 19:17:29,016 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$10.8K(+208.6%)**
*...
2025-06-12 19:17:29,017 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 19:17:29,018 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 19:17:29,019 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 19:17:29,020 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 19:17:29,021 - signal_handler - INFO - Found contract address with 'pump' suffix: D3qMHLYspCX9r8zwPwBKPgv18NafGTq5E3cTsJ8Cpump
2025-06-12 19:17:29,022 - signal_handler - INFO - Found contract address: 22ycPr5JvZAPtfYV62RckTqHzP3GdF4Rsez3RgZR1Can
2025-06-12 19:17:29,023 - signal_handler - INFO - Using 'pump' address as highest priority: D3qMHLYspCX9r8zwPwBKPgv18NafGTq5E3cTsJ8Cpump
2025-06-12 19:17:29,024 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: D3qMHLYspCX9r8zwPwBKPgv18NafGTq5E3cTsJ8Cpump
2025-06-12 19:17:29,026 - signal_handler - INFO - Detected GMGN format
2025-06-12 19:17:29,027 - signal_handler - INFO - Found token symbol: 10
2025-06-12 19:17:29,028 - signal_handler - INFO - Found FDV: 10.8K - 10.80 (+208.6%)
2025-06-12 19:17:29,029 - signal_handler - INFO - Detected GMGN channel signal for token: D3qMHLYspCX9r8zwPwBKPgv18NafGTq5E3cTsJ8Cpump
2025-06-12 19:17:29,030 - signal_handler - INFO - Extracted signal: Token=D3qMHLYspCX9r8zwPwBKPgv18NafGTq5E3cTsJ8Cpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 19:17:29,030 - signal_handler - INFO - Signal processing health: 35 received, 35 processed, 0 dropped (100.0% success rate)
2025-06-12 19:17:29,031 - signal_handler - INFO - Added signal to queue: D3qMHLYspCX9r8zwPwBKPgv18NafGTq5E3cTsJ8Cpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 19:17:29,031 - signal_handler - INFO - Calling direct signal callback for D3qMHLYspCX9r8zwPwBKPgv18NafGTq5E3cTsJ8Cpump
2025-06-12 19:17:29,035 - signal_handler - INFO - Direct signal processing completed for D3qMHLYspCX9r8zwPwBKPgv18NafGTq5E3cTsJ8Cpump
2025-06-12 19:17:29,036 - signal_handler - INFO - Signal forwarded to bot controller: D3qMHLYspCX9r8zwPwBKPgv18NafGTq5E3cTsJ8Cpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 19:17:29,036 - signal_handler - INFO - Retrieved signal from queue: D3qMHLYspCX9r8zwPwBKPgv18NafGTq5E3cTsJ8Cpump from solana signal alert - gmgn
2025-06-12 19:17:29,038 - signal_handler - INFO - [msg_1749736049038_3582] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 19:17:29

 Token CA: D3qMHLYspCX9r8zwPwBKPgv18NafGTq5E3cTsJ8Cp...
2025-06-12 19:17:29,039 - signal_handler - INFO - [msg_1749736049038_3582] Message queued with normal priority
2025-06-12 19:17:36,014 - signal_handler - INFO - [msg_1749736049038_3582] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:17:36,457 - signal_handler - INFO - [msg_1749736049038_3582] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:17:36,496 - signal_handler - INFO - [msg_1749736056494_2721] [SELL NOTIFICATION]: [GREEN] [SIM BUY EXECUTED] [?] 2025-06-12 19:17:36

 Signal Source: solana signal alert - gmgn
 Toke...
2025-06-12 19:17:36,501 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749736056494_2721.txt
2025-06-12 19:17:36,502 - signal_handler - INFO - [msg_1749736056494_2721] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:17:36,769 - signal_handler - INFO - [msg_1749736056494_2721] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:17:36,788 - signal_handler - INFO - [msg_1749736049038_3582] Message sent directly successfully using entity
2025-06-12 19:17:36,788 - signal_handler - INFO - [msg_1749736049038_3582] Message sent successfully via direct send method
2025-06-12 19:17:36,789 - signal_handler - INFO - [msg_1749736049038_3582] Message sent successfully on attempt 1
2025-06-12 19:17:36,968 - signal_handler - INFO - [msg_1749736056494_2721] Message sent directly successfully using entity
2025-06-12 19:17:36,969 - signal_handler - INFO - [msg_1749736056494_2721] Message sent directly successfully
2025-06-12 19:17:36,970 - signal_handler - INFO - [msg_1749736056970_1134] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (SIMULATION) - D3QMHLYS

 Timestamp: 2025-06-12 19:17:36
[REPEAT] Tr...
2025-06-12 19:17:36,972 - signal_handler - INFO - [msg_1749736056970_1134] Message queued with normal priority
2025-06-12 19:17:37,011 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.28s (interval: 0.50s)
2025-06-12 19:17:37,304 - signal_handler - INFO - [msg_1749736056970_1134] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:17:37,363 - signal_handler - INFO - [msg_1749736056970_1134] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:17:37,468 - signal_handler - INFO - [msg_1749736056970_1134] Message sent directly successfully using entity
2025-06-12 19:17:37,469 - signal_handler - INFO - [msg_1749736056970_1134] Message sent successfully via direct send method
2025-06-12 19:17:37,469 - signal_handler - INFO - [msg_1749736056970_1134] Message sent successfully on attempt 1
2025-06-12 19:18:25,250 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$28.1K(+204.7%)**
*...
2025-06-12 19:18:25,251 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 19:18:25,251 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 19:18:25,252 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 19:18:25,252 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 19:18:25,253 - signal_handler - INFO - Found contract address with 'pump' suffix: E8k8fT6PCSMK6B6rQbwnXAPvBTANbzpVWa2BFdmmpump
2025-06-12 19:18:25,254 - signal_handler - INFO - Found contract address: EerL5QSpRNKhgSgLN8y9gUheE3gnKK7hdPUvHMswtPC6
2025-06-12 19:18:25,254 - signal_handler - INFO - Using 'pump' address as highest priority: E8k8fT6PCSMK6B6rQbwnXAPvBTANbzpVWa2BFdmmpump
2025-06-12 19:18:25,255 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: E8k8fT6PCSMK6B6rQbwnXAPvBTANbzpVWa2BFdmmpump
2025-06-12 19:18:25,256 - signal_handler - INFO - Detected GMGN format
2025-06-12 19:18:25,256 - signal_handler - INFO - Found token symbol: 28
2025-06-12 19:18:25,257 - signal_handler - INFO - Found FDV: 28.1K - 28.10 (+204.7%)
2025-06-12 19:18:25,257 - signal_handler - INFO - Detected GMGN channel signal for token: E8k8fT6PCSMK6B6rQbwnXAPvBTANbzpVWa2BFdmmpump
2025-06-12 19:18:25,258 - signal_handler - INFO - Extracted signal: Token=E8k8fT6PCSMK6B6rQbwnXAPvBTANbzpVWa2BFdmmpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 19:18:25,258 - signal_handler - INFO - Added signal to queue: E8k8fT6PCSMK6B6rQbwnXAPvBTANbzpVWa2BFdmmpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 19:18:25,259 - signal_handler - INFO - Calling direct signal callback for E8k8fT6PCSMK6B6rQbwnXAPvBTANbzpVWa2BFdmmpump
2025-06-12 19:18:25,263 - signal_handler - INFO - Direct signal processing completed for E8k8fT6PCSMK6B6rQbwnXAPvBTANbzpVWa2BFdmmpump
2025-06-12 19:18:25,264 - signal_handler - INFO - Signal forwarded to bot controller: E8k8fT6PCSMK6B6rQbwnXAPvBTANbzpVWa2BFdmmpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 19:18:25,264 - signal_handler - INFO - Retrieved signal from queue: E8k8fT6PCSMK6B6rQbwnXAPvBTANbzpVWa2BFdmmpump from solana signal alert - gmgn
2025-06-12 19:18:25,266 - signal_handler - INFO - [msg_1749736105266_5976] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 19:18:25

 Token CA: E8k8fT6PCSMK6B6rQbwnXAPvBTANbzpVWa2BFdmmp...
2025-06-12 19:18:25,267 - signal_handler - INFO - [msg_1749736105266_5976] Message queued with normal priority
2025-06-12 19:18:32,030 - signal_handler - INFO - Message queue processor active with 1 messages pending
2025-06-12 19:18:32,030 - signal_handler - INFO - [msg_1749736105266_5976] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:18:32,505 - signal_handler - INFO - [msg_1749736105266_5976] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:18:32,607 - signal_handler - INFO - [msg_1749736105266_5976] Message sent directly successfully using entity
2025-06-12 19:18:32,608 - signal_handler - INFO - [msg_1749736105266_5976] Message sent successfully via direct send method
2025-06-12 19:18:32,609 - signal_handler - INFO - [msg_1749736105266_5976] Message sent successfully on attempt 1
2025-06-12 19:18:38,683 - signal_handler - INFO - Received sell notification: [PURPLE] [SIM SELL [?] STOP LOSS HIT (-70.8% <= -20.0%)] [?] 2025-06-12 19:18:38
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]...
2025-06-12 19:18:38,686 - signal_handler - INFO - [msg_1749736118683_2994] [SELL NOTIFICATION]:  [SIM SELL [?] STOP LOSS HIT (-70.8% <= -20.0%)] [?] 2025-06-12 19:18:38

 Triggered by: solana sign...
2025-06-12 19:18:38,690 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749736118683_2994.txt
2025-06-12 19:18:38,691 - signal_handler - INFO - [msg_1749736118683_2994] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:18:38,754 - signal_handler - INFO - [msg_1749736118683_2994] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:18:38,899 - signal_handler - INFO - [msg_1749736118683_2994] Message sent directly successfully using entity
2025-06-12 19:18:38,899 - signal_handler - INFO - [msg_1749736118683_2994] Message sent directly successfully
2025-06-12 19:18:38,901 - signal_handler - INFO - [msg_1749736118900_1404] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-12 19:18:38
[REPEAT] Trade: SIMULATION

[MONEY...
2025-06-12 19:18:38,901 - signal_handler - INFO - [msg_1749736118900_1404] Message queued with normal priority
2025-06-12 19:18:38,976 - signal_handler - INFO - [msg_1749736118900_1404] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:18:39,048 - signal_handler - INFO - [msg_1749736118900_1404] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:18:39,152 - signal_handler - INFO - [msg_1749736118900_1404] Message sent directly successfully using entity
2025-06-12 19:18:39,153 - signal_handler - INFO - [msg_1749736118900_1404] Message sent successfully via direct send method
2025-06-12 19:18:39,154 - signal_handler - INFO - [msg_1749736118900_1404] Message sent successfully on attempt 1
2025-06-12 19:18:40,068 - signal_handler - INFO - [msg_1749736120066_1190] [SELL NOTIFICATION]: [GREEN] [SIM BUY EXECUTED] [?] 2025-06-12 19:18:40

 Signal Source: solana signal alert - gmgn
 Toke...
2025-06-12 19:18:40,073 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749736120066_1190.txt
2025-06-12 19:18:40,075 - signal_handler - INFO - [msg_1749736120066_1190] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:18:40,132 - signal_handler - INFO - [msg_1749736120066_1190] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:18:40,262 - signal_handler - INFO - [msg_1749736120066_1190] Message sent directly successfully using entity
2025-06-12 19:18:40,263 - signal_handler - INFO - [msg_1749736120066_1190] Message sent directly successfully
2025-06-12 19:18:40,264 - signal_handler - INFO - [msg_1749736120264_4772] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (SIMULATION) - E8K8FT6P

 Timestamp: 2025-06-12 19:18:40
[REPEAT] Tr...
2025-06-12 19:18:40,265 - signal_handler - INFO - [msg_1749736120264_4772] Message queued with normal priority
2025-06-12 19:18:40,351 - signal_handler - INFO - [msg_1749736120264_4772] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:18:40,413 - signal_handler - INFO - [msg_1749736120264_4772] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:18:40,517 - signal_handler - INFO - [msg_1749736120264_4772] Message sent directly successfully using entity
2025-06-12 19:18:40,518 - signal_handler - INFO - [msg_1749736120264_4772] Message sent successfully via direct send method
2025-06-12 19:18:40,519 - signal_handler - INFO - [msg_1749736120264_4772] Message sent successfully on attempt 1
2025-06-12 19:19:36,430 - signal_handler - INFO - Received sell notification: [PURPLE] [SIM SELL [?] STOP LOSS HIT (-30.5% <= -20.0%)] [?] 2025-06-12 19:19:36
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]...
2025-06-12 19:19:36,434 - signal_handler - INFO - [msg_1749736176430_6969] [SELL NOTIFICATION]:  [SIM SELL [?] STOP LOSS HIT (-30.5% <= -20.0%)] [?] 2025-06-12 19:19:36

 Triggered by: solana sign...
2025-06-12 19:19:36,438 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749736176430_6969.txt
2025-06-12 19:19:36,440 - signal_handler - INFO - [msg_1749736176430_6969] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:19:36,565 - signal_handler - INFO - [msg_1749736176430_6969] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:19:36,692 - signal_handler - INFO - [msg_1749736176430_6969] Message sent directly successfully using entity
2025-06-12 19:19:36,693 - signal_handler - INFO - [msg_1749736176430_6969] Message sent directly successfully
2025-06-12 19:19:36,696 - signal_handler - INFO - [msg_1749736176695_7263] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-12 19:19:36
[REPEAT] Trade: SIMULATION

[MONEY...
2025-06-12 19:19:36,697 - signal_handler - INFO - [msg_1749736176695_7263] Message queued with normal priority
2025-06-12 19:19:36,770 - signal_handler - INFO - Message queue processor active with 1 messages pending
2025-06-12 19:19:36,772 - signal_handler - INFO - [msg_1749736176695_7263] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:19:36,840 - signal_handler - INFO - [msg_1749736176695_7263] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:19:36,959 - signal_handler - INFO - [msg_1749736176695_7263] Message sent directly successfully using entity
2025-06-12 19:19:36,960 - signal_handler - INFO - [msg_1749736176695_7263] Message sent successfully via direct send method
2025-06-12 19:19:36,961 - signal_handler - INFO - [msg_1749736176695_7263] Message sent successfully on attempt 1
2025-06-12 19:20:10,454 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$13.2K(+262.9%)**
*...
2025-06-12 19:20:10,455 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 19:20:10,456 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 19:20:10,457 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 19:20:10,458 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 19:20:10,459 - signal_handler - INFO - Found contract address: qRiXzpWgM1XGQXCuLAzWBYVmD5FjwdsUmugVWgeEwMJ
2025-06-12 19:20:10,460 - signal_handler - INFO - Found contract address: CyMKYAowtmJskfjjZYMUM71sKhXGqXykrp2mb1QkxmpH
2025-06-12 19:20:10,461 - signal_handler - INFO - Found 2 unique token addresses
2025-06-12 19:20:10,461 - signal_handler - INFO - Token address: qRiXzpWgM1XGQXCuLAzWBYVmD5FjwdsUmugVWgeEwMJ
2025-06-12 19:20:10,462 - signal_handler - INFO - Token address: CyMKYAowtmJskfjjZYMUM71sKhXGqXykrp2mb1QkxmpH
2025-06-12 19:20:10,462 - signal_handler - INFO - Detected GMGN format
2025-06-12 19:20:10,463 - signal_handler - INFO - Found token symbol: 13
2025-06-12 19:20:10,463 - signal_handler - INFO - Found FDV: 13.2K - 13.20 (+262.9%)
2025-06-12 19:20:10,464 - signal_handler - INFO - Detected GMGN channel signal for token: qRiXzpWgM1XGQXCuLAzWBYVmD5FjwdsUmugVWgeEwMJ
2025-06-12 19:20:10,464 - signal_handler - INFO - Extracted signal: Token=qRiXzpWgM1XGQXCuLAzWBYVmD5FjwdsUmugVWgeEwMJ, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 19:20:10,465 - signal_handler - INFO - Added signal to queue: qRiXzpWgM1XGQXCuLAzWBYVmD5FjwdsUmugVWgeEwMJ from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 19:20:10,465 - signal_handler - INFO - Calling direct signal callback for qRiXzpWgM1XGQXCuLAzWBYVmD5FjwdsUmugVWgeEwMJ
2025-06-12 19:20:10,470 - signal_handler - INFO - Direct signal processing completed for qRiXzpWgM1XGQXCuLAzWBYVmD5FjwdsUmugVWgeEwMJ
2025-06-12 19:20:10,471 - signal_handler - INFO - Signal forwarded to bot controller: qRiXzpWgM1XGQXCuLAzWBYVmD5FjwdsUmugVWgeEwMJ from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 19:20:10,471 - signal_handler - INFO - Retrieved signal from queue: qRiXzpWgM1XGQXCuLAzWBYVmD5FjwdsUmugVWgeEwMJ from solana signal alert - gmgn
2025-06-12 19:20:10,473 - signal_handler - INFO - [msg_1749736210473_6947] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 19:20:10

 Token CA: qRiXzpWgM1XGQXCuLAzWBYVmD5FjwdsUmugVWgeEw...
2025-06-12 19:20:10,474 - signal_handler - INFO - [msg_1749736210473_6947] Message queued with normal priority
2025-06-12 19:20:17,684 - signal_handler - INFO - [msg_1749736210473_6947] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:20:17,814 - signal_handler - INFO - [msg_1749736210473_6947] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:20:17,962 - signal_handler - INFO - [msg_1749736210473_6947] Message sent directly successfully using entity
2025-06-12 19:20:17,963 - signal_handler - INFO - [msg_1749736210473_6947] Message sent successfully via direct send method
2025-06-12 19:20:17,964 - signal_handler - INFO - [msg_1749736210473_6947] Message sent successfully on attempt 1
2025-06-12 19:20:18,051 - signal_handler - INFO - [msg_1749736218050_4137] [SELL NOTIFICATION]: [GREEN] [SIM BUY EXECUTED] [?] 2025-06-12 19:20:18

 Signal Source: solana signal alert - gmgn
 Toke...
2025-06-12 19:20:18,054 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749736218050_4137.txt
2025-06-12 19:20:18,055 - signal_handler - INFO - [msg_1749736218050_4137] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:20:18,146 - signal_handler - INFO - [msg_1749736218050_4137] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:20:18,288 - signal_handler - INFO - [msg_1749736218050_4137] Message sent directly successfully using entity
2025-06-12 19:20:18,289 - signal_handler - INFO - [msg_1749736218050_4137] Message sent directly successfully
2025-06-12 19:20:18,290 - signal_handler - INFO - [msg_1749736218289_5678] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (SIMULATION) - QRIXZPWG

 Timestamp: 2025-06-12 19:20:18
[REPEAT] Tr...
2025-06-12 19:20:18,290 - signal_handler - INFO - [msg_1749736218289_5678] Message queued with normal priority
2025-06-12 19:20:18,292 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.17s (interval: 0.50s)
2025-06-12 19:20:18,467 - signal_handler - INFO - [msg_1749736218289_5678] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:20:18,529 - signal_handler - INFO - [msg_1749736218289_5678] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:20:18,643 - signal_handler - INFO - [msg_1749736218289_5678] Message sent directly successfully using entity
2025-06-12 19:20:18,644 - signal_handler - INFO - [msg_1749736218289_5678] Message sent successfully via direct send method
2025-06-12 19:20:18,645 - signal_handler - INFO - [msg_1749736218289_5678] Message sent successfully on attempt 1
2025-06-12 19:20:31,218 - signal_handler - INFO - [msg_1749736231218_6065] Queuing Telegram message:  PORTFOLIO UPDATE [?] PERIODIC UPDATE

 Timestamp: 2025-06-12 19:20:31
[REPEAT] Trade: SIMULATION

[...
2025-06-12 19:20:31,220 - signal_handler - INFO - [msg_1749736231218_6065] Message queued with normal priority
2025-06-12 19:20:31,303 - signal_handler - INFO - [msg_1749736231218_6065] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:20:31,369 - signal_handler - INFO - [msg_1749736231218_6065] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:20:31,491 - signal_handler - INFO - [msg_1749736231218_6065] Message sent directly successfully using entity
2025-06-12 19:20:31,492 - signal_handler - INFO - [msg_1749736231218_6065] Message sent successfully via direct send method
2025-06-12 19:20:31,493 - signal_handler - INFO - [msg_1749736231218_6065] Message sent successfully on attempt 1
2025-06-12 19:21:10,316 - signal_handler - INFO - Received message from channel -1002093384030: [FIRE] [**Gaytarded](https://t.me/soul_sniper_bot?start=15_8dpVVb5Krz9xknQANdn4Smh48NoqPRzAJgWhmjDCp...
2025-06-12 19:21:10,317 - signal_handler - INFO - Setting channel_identifier to 'Solana Early Trending' based on chat_id -1002093384030
2025-06-12 19:21:10,317 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 19:21:10,318 - signal_handler - INFO - Found contract address with 'pump' suffix in Soul Sniper link: 8dpVVb5Krz9xknQANdn4Smh48NoqPRzAJgWhmjDCpump
2025-06-12 19:21:10,318 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 8dpVVb5Krz9xknQANdn4Smh48NoqPRzAJgWhmjDCpump
2025-06-12 19:21:10,319 - signal_handler - INFO - Detected signal from Solana Early Trending for token: 8dpVVb5Krz9xknQANdn4Smh48NoqPRzAJgWhmjDCpump
2025-06-12 19:21:10,319 - signal_handler - INFO - Extracted signal: Token=8dpVVb5Krz9xknQANdn4Smh48NoqPRzAJgWhmjDCpump, Source=Solana Early Trending, Metrics Count=1
2025-06-12 19:21:10,320 - signal_handler - INFO - Added signal to queue: 8dpVVb5Krz9xknQANdn4Smh48NoqPRzAJgWhmjDCpump from Solana Early Trending with confidence 0.5, GMGN channel: False
2025-06-12 19:21:10,320 - signal_handler - INFO - Calling direct signal callback for 8dpVVb5Krz9xknQANdn4Smh48NoqPRzAJgWhmjDCpump
2025-06-12 19:21:10,323 - signal_handler - INFO - Direct signal processing completed for 8dpVVb5Krz9xknQANdn4Smh48NoqPRzAJgWhmjDCpump
2025-06-12 19:21:10,323 - signal_handler - INFO - Signal forwarded to bot controller: 8dpVVb5Krz9xknQANdn4Smh48NoqPRzAJgWhmjDCpump from Solana Early Trending (confidence: 0.50)
2025-06-12 19:21:10,323 - signal_handler - INFO - Retrieved signal from queue: 8dpVVb5Krz9xknQANdn4Smh48NoqPRzAJgWhmjDCpump from Solana Early Trending
2025-06-12 19:21:10,325 - signal_handler - INFO - [msg_1749736270324_4892] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 19:21:10

 Token CA: 8dpVVb5Krz9xknQANdn4Smh48NoqPRzAJgWhmjDCp...
2025-06-12 19:21:10,325 - signal_handler - INFO - [msg_1749736270324_4892] Message queued with normal priority
2025-06-12 19:21:13,376 - signal_handler - INFO - [msg_1749736270324_4892] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:21:13,882 - signal_handler - INFO - [msg_1749736270324_4892] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:21:19,877 - signal_handler - INFO - Received sell notification: [PURPLE] [SIM SELL [?] TP1 HIT (64.3% >= 20.0%)] [?] 2025-06-12 19:21:19
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]
[?] Trig...
2025-06-12 19:21:19,880 - signal_handler - INFO - [msg_1749736279877_3432] [SELL NOTIFICATION]:  [SIM SELL [?] TP1 HIT (64.3% >= 20.0%)] [?] 2025-06-12 19:21:19

 Triggered by: solana signal alert...
2025-06-12 19:21:19,883 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749736279877_3432.txt
2025-06-12 19:21:19,883 - signal_handler - INFO - [msg_1749736279877_3432] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:21:19,900 - signal_handler - WARNING - [msg_1749736270324_4892] Entity resolution or send failed: , trying direct ID approach
2025-06-12 19:21:19,901 - signal_handler - INFO - [msg_1749736270324_4892] Sending message directly to channel ID: -1002525039395
2025-06-12 19:21:19,942 - signal_handler - INFO - [msg_1749736279877_3432] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:21:19,997 - signal_handler - INFO - [msg_1749736270324_4892] Message sent directly successfully using ID
2025-06-12 19:21:19,998 - signal_handler - INFO - [msg_1749736270324_4892] Message sent successfully via direct send method
2025-06-12 19:21:19,999 - signal_handler - INFO - [msg_1749736270324_4892] Message sent successfully on attempt 1
2025-06-12 19:21:20,064 - signal_handler - INFO - [msg_1749736279877_3432] Message sent directly successfully using entity
2025-06-12 19:21:20,065 - signal_handler - INFO - [msg_1749736279877_3432] Message sent directly successfully
2025-06-12 19:21:20,066 - signal_handler - INFO - [msg_1749736280066_7618] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-12 19:21:20
[REPEAT] Trade: SIMULATION

[MONEY...
2025-06-12 19:21:20,066 - signal_handler - INFO - [msg_1749736280066_7618] Message queued with normal priority
2025-06-12 19:21:20,135 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.36s (interval: 0.50s)
2025-06-12 19:21:20,144 - signal_handler - INFO - [msg_1749736280143_2598] [SELL NOTIFICATION]: [GREEN] [SIM BUY EXECUTED] [?] 2025-06-12 19:21:20

 Signal Source: Solana Early Trending
 Token: 8d...
2025-06-12 19:21:20,147 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749736280143_2598.txt
2025-06-12 19:21:20,148 - signal_handler - INFO - [msg_1749736280143_2598] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:21:20,203 - signal_handler - INFO - [msg_1749736280143_2598] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:21:20,353 - signal_handler - INFO - [msg_1749736280143_2598] Message sent directly successfully using entity
2025-06-12 19:21:20,354 - signal_handler - INFO - [msg_1749736280143_2598] Message sent directly successfully
2025-06-12 19:21:20,355 - signal_handler - INFO - [msg_1749736280355_1717] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (SIMULATION) - 8DPVVB5K

 Timestamp: 2025-06-12 19:21:20
[REPEAT] Tr...
2025-06-12 19:21:20,357 - signal_handler - INFO - [msg_1749736280355_1717] Message queued with normal priority
2025-06-12 19:21:20,503 - signal_handler - INFO - [msg_1749736280066_7618] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:21:20,563 - signal_handler - INFO - [msg_1749736280066_7618] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:21:20,660 - signal_handler - INFO - [msg_1749736280066_7618] Message sent directly successfully using entity
2025-06-12 19:21:20,661 - signal_handler - INFO - [msg_1749736280066_7618] Message sent successfully via direct send method
2025-06-12 19:21:20,663 - signal_handler - INFO - [msg_1749736280066_7618] Message sent successfully on attempt 1
2025-06-12 19:21:20,664 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 19:21:21,166 - signal_handler - INFO - [msg_1749736280355_1717] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:21:21,221 - signal_handler - INFO - [msg_1749736280355_1717] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:21:21,321 - signal_handler - INFO - [msg_1749736280355_1717] Message sent directly successfully using entity
2025-06-12 19:21:21,321 - signal_handler - INFO - [msg_1749736280355_1717] Message sent successfully via direct send method
2025-06-12 19:21:21,322 - signal_handler - INFO - [msg_1749736280355_1717] Message sent successfully on attempt 1
2025-06-12 19:22:17,690 - signal_handler - INFO - Received sell notification: [PURPLE] [SIM SELL [?] STOP LOSS HIT (-80.5% <= -20.0%)] [?] 2025-06-12 19:22:17
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]...
2025-06-12 19:22:17,693 - signal_handler - INFO - [msg_1749736337690_1601] [SELL NOTIFICATION]:  [SIM SELL [?] STOP LOSS HIT (-80.5% <= -20.0%)] [?] 2025-06-12 19:22:17

 Triggered by: Solana Earl...
2025-06-12 19:22:17,697 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749736337690_1601.txt
2025-06-12 19:22:17,699 - signal_handler - INFO - [msg_1749736337690_1601] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:22:17,721 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$9993.4711(+211.2%)...
2025-06-12 19:22:17,722 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 19:22:17,722 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 19:22:17,723 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 19:22:17,723 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 19:22:17,724 - signal_handler - INFO - Found contract address with 'pump' suffix: BiTMvKBhyj6eKyPGtN6QxDGPi3ByKkyfZb65qXSPpump
2025-06-12 19:22:17,724 - signal_handler - INFO - Found contract address: EwUwCTo5ZjTuxcsBn1R47kktkEj9UDi3bn2eGLNyw6g4
2025-06-12 19:22:17,725 - signal_handler - INFO - Using 'pump' address as highest priority: BiTMvKBhyj6eKyPGtN6QxDGPi3ByKkyfZb65qXSPpump
2025-06-12 19:22:17,725 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: BiTMvKBhyj6eKyPGtN6QxDGPi3ByKkyfZb65qXSPpump
2025-06-12 19:22:17,726 - signal_handler - INFO - Detected GMGN format
2025-06-12 19:22:17,726 - signal_handler - INFO - Found token symbol: 9993
2025-06-12 19:22:17,727 - signal_handler - INFO - Found FDV: 9993.4711 - 9.99K (+211.2%)
2025-06-12 19:22:17,727 - signal_handler - INFO - Detected GMGN channel signal for token: BiTMvKBhyj6eKyPGtN6QxDGPi3ByKkyfZb65qXSPpump
2025-06-12 19:22:17,728 - signal_handler - INFO - Extracted signal: Token=BiTMvKBhyj6eKyPGtN6QxDGPi3ByKkyfZb65qXSPpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 19:22:17,728 - signal_handler - INFO - Added signal to queue: BiTMvKBhyj6eKyPGtN6QxDGPi3ByKkyfZb65qXSPpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 19:22:17,729 - signal_handler - INFO - Calling direct signal callback for BiTMvKBhyj6eKyPGtN6QxDGPi3ByKkyfZb65qXSPpump
2025-06-12 19:22:17,732 - signal_handler - INFO - Direct signal processing completed for BiTMvKBhyj6eKyPGtN6QxDGPi3ByKkyfZb65qXSPpump
2025-06-12 19:22:17,733 - signal_handler - INFO - Signal forwarded to bot controller: BiTMvKBhyj6eKyPGtN6QxDGPi3ByKkyfZb65qXSPpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 19:22:17,733 - signal_handler - INFO - Retrieved signal from queue: BiTMvKBhyj6eKyPGtN6QxDGPi3ByKkyfZb65qXSPpump from solana signal alert - gmgn
2025-06-12 19:22:17,735 - signal_handler - INFO - [msg_1749736337734_9079] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 19:22:17

 Token CA: BiTMvKBhyj6eKyPGtN6QxDGPi3ByKkyfZb65qXSPp...
2025-06-12 19:22:17,735 - signal_handler - INFO - [msg_1749736337734_9079] Message queued with normal priority
2025-06-12 19:22:24,554 - signal_handler - INFO - [msg_1749736344553_6743] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW LIQUIDITY] [?] 2025-06-12 19:22:24

 Token: BiTMvKBhyj6eKyPGtN6QxDGPi3...
2025-06-12 19:22:24,556 - signal_handler - INFO - [msg_1749736344553_6743] Message queued with normal priority
2025-06-12 19:22:24,558 - signal_handler - INFO - [msg_1749736337734_9079] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:22:24,566 - signal_handler - INFO - [msg_1749736337690_1601] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:22:24,629 - signal_handler - INFO - [msg_1749736337734_9079] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:22:24,700 - signal_handler - INFO - [msg_1749736337690_1601] Message sent directly successfully using entity
2025-06-12 19:22:24,700 - signal_handler - INFO - [msg_1749736337690_1601] Message sent directly successfully
2025-06-12 19:22:24,702 - signal_handler - INFO - [msg_1749736344701_2192] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-12 19:22:24
[REPEAT] Trade: SIMULATION

[MONEY...
2025-06-12 19:22:24,702 - signal_handler - INFO - [msg_1749736344701_2192] Message queued with normal priority
2025-06-12 19:22:24,729 - signal_handler - INFO - [msg_1749736337734_9079] Message sent directly successfully using entity
2025-06-12 19:22:24,729 - signal_handler - INFO - [msg_1749736337734_9079] Message sent successfully via direct send method
2025-06-12 19:22:24,729 - signal_handler - INFO - [msg_1749736337734_9079] Message sent successfully on attempt 1
2025-06-12 19:22:24,730 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 19:22:25,242 - signal_handler - INFO - [msg_1749736344553_6743] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:22:25,304 - signal_handler - INFO - [msg_1749736344553_6743] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:22:25,414 - signal_handler - INFO - [msg_1749736344553_6743] Message sent directly successfully using entity
2025-06-12 19:22:25,415 - signal_handler - INFO - [msg_1749736344553_6743] Message sent successfully via direct send method
2025-06-12 19:22:25,416 - signal_handler - INFO - [msg_1749736344553_6743] Message sent successfully on attempt 1
2025-06-12 19:22:25,417 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 19:22:25,922 - signal_handler - INFO - [msg_1749736344701_2192] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:22:25,982 - signal_handler - INFO - [msg_1749736344701_2192] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:22:26,080 - signal_handler - INFO - [msg_1749736344701_2192] Message sent directly successfully using entity
2025-06-12 19:22:26,081 - signal_handler - INFO - [msg_1749736344701_2192] Message sent successfully via direct send method
2025-06-12 19:22:26,082 - signal_handler - INFO - [msg_1749736344701_2192] Message sent successfully on attempt 1
2025-06-12 19:24:17,218 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$9905.2717(+222.3%)...
2025-06-12 19:24:17,219 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 19:24:17,220 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 19:24:17,221 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 19:24:17,221 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 19:24:17,223 - signal_handler - INFO - Found contract address with 'pump' suffix: CPV2x6hbXHzV6tQvY9ookxH6FdWvjPqgMBtmLSwBpump
2025-06-12 19:24:17,224 - signal_handler - INFO - Found contract address: 2DMSUuu4GTqtzGKZfyMWsJZFDyCfrWiq5tMPDBgzTbcv
2025-06-12 19:24:17,225 - signal_handler - INFO - Using 'pump' address as highest priority: CPV2x6hbXHzV6tQvY9ookxH6FdWvjPqgMBtmLSwBpump
2025-06-12 19:24:17,226 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: CPV2x6hbXHzV6tQvY9ookxH6FdWvjPqgMBtmLSwBpump
2025-06-12 19:24:17,227 - signal_handler - INFO - Detected GMGN format
2025-06-12 19:24:17,228 - signal_handler - INFO - Found token symbol: 9905
2025-06-12 19:24:17,229 - signal_handler - INFO - Found FDV: 9905.2717 - 9.91K (+222.3%)
2025-06-12 19:24:17,230 - signal_handler - INFO - Detected GMGN channel signal for token: CPV2x6hbXHzV6tQvY9ookxH6FdWvjPqgMBtmLSwBpump
2025-06-12 19:24:17,231 - signal_handler - INFO - Extracted signal: Token=CPV2x6hbXHzV6tQvY9ookxH6FdWvjPqgMBtmLSwBpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 19:24:17,233 - signal_handler - WARNING - LOW SIGNAL VOLUME: GMGN - 0/8 expected this hour
2025-06-12 19:24:17,234 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solbix - 0/2 expected this hour
2025-06-12 19:24:17,234 - signal_handler - INFO - Added signal to queue: CPV2x6hbXHzV6tQvY9ookxH6FdWvjPqgMBtmLSwBpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 19:24:17,235 - signal_handler - INFO - Calling direct signal callback for CPV2x6hbXHzV6tQvY9ookxH6FdWvjPqgMBtmLSwBpump
2025-06-12 19:24:17,240 - signal_handler - INFO - Direct signal processing completed for CPV2x6hbXHzV6tQvY9ookxH6FdWvjPqgMBtmLSwBpump
2025-06-12 19:24:17,241 - signal_handler - INFO - Signal forwarded to bot controller: CPV2x6hbXHzV6tQvY9ookxH6FdWvjPqgMBtmLSwBpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 19:24:17,242 - signal_handler - INFO - Retrieved signal from queue: CPV2x6hbXHzV6tQvY9ookxH6FdWvjPqgMBtmLSwBpump from solana signal alert - gmgn
2025-06-12 19:24:17,243 - signal_handler - INFO - [msg_1749736457243_5852] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 19:24:17

 Token CA: CPV2x6hbXHzV6tQvY9ookxH6FdWvjPqgMBtmLSwBp...
2025-06-12 19:24:17,244 - signal_handler - INFO - [msg_1749736457243_5852] Message queued with normal priority
2025-06-12 19:24:24,100 - signal_handler - INFO - [msg_1749736464099_2071] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] LOW LIQUIDITY] [?] 2025-06-12 19:24:24

 Token: CPV2x6hbXHzV6tQvY9ookxH6Fd...
2025-06-12 19:24:24,101 - signal_handler - INFO - [msg_1749736464099_2071] Message queued with normal priority
2025-06-12 19:24:24,102 - signal_handler - INFO - [msg_1749736457243_5852] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:24:24,164 - signal_handler - INFO - [msg_1749736457243_5852] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:24:24,282 - signal_handler - INFO - [msg_1749736457243_5852] Message sent directly successfully using entity
2025-06-12 19:24:24,283 - signal_handler - INFO - [msg_1749736457243_5852] Message sent successfully via direct send method
2025-06-12 19:24:24,284 - signal_handler - INFO - [msg_1749736457243_5852] Message sent successfully on attempt 1
2025-06-12 19:24:24,285 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 19:24:24,792 - signal_handler - INFO - [msg_1749736464099_2071] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:24:24,857 - signal_handler - INFO - [msg_1749736464099_2071] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:24:24,990 - signal_handler - INFO - [msg_1749736464099_2071] Message sent directly successfully using entity
2025-06-12 19:24:24,991 - signal_handler - INFO - [msg_1749736464099_2071] Message sent successfully via direct send method
2025-06-12 19:24:24,992 - signal_handler - INFO - [msg_1749736464099_2071] Message sent successfully on attempt 1
