2025-06-12 14:20:55,255 - signal_handler - INFO - Direct signal processing completed for D6Xwb3mGP8NVm4f8DuUcw1pgz4Y4QJ9i2yePWZQiEiTv
2025-06-12 14:20:55,255 - signal_handler - INFO - Signal forwarded to bot controller: D6Xwb3mGP8NVm4f8DuUcw1pgz4Y4QJ9i2yePWZQiEiTv from solana signal alert - gmgn (confidence: 0.50)
2025-06-12 14:20:55,255 - signal_handler - INFO - Retrieved signal from queue: D6Xwb3mGP8NVm4f8DuUcw1pgz4Y4QJ9i2yePWZQiEiTv from solana signal alert - gmgn
2025-06-12 14:20:55,256 - signal_handler - INFO - [msg_1749718255256_3081] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-12 14:20:55

 Token CA: D6Xwb3mGP8NVm4f8DuUcw1pgz4Y4QJ9i2yePWZQiE...
2025-06-12 14:20:55,257 - signal_handler - INFO - [msg_1749718255256_3081] Message queued with normal priority
2025-06-12 14:21:03,421 - signal_handler - INFO - [msg_1749718255256_3081] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:21:03,596 - signal_handler - INFO - [msg_1749718255256_3081] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:21:03,731 - signal_handler - INFO - [msg_1749718255256_3081] Message sent directly successfully using entity
2025-06-12 14:21:03,731 - signal_handler - INFO - [msg_1749718255256_3081] Message sent successfully via direct send method
2025-06-12 14:21:03,732 - signal_handler - INFO - [msg_1749718255256_3081] Message sent successfully on attempt 1
2025-06-12 14:21:03,847 - signal_handler - INFO - [msg_1749718263846_4477] [SELL NOTIFICATION]: [GREEN] [SIM BUY EXECUTED] [?] 2025-06-12 14:21:03

 Signal Source: solana signal alert - gmgn
 Toke...
2025-06-12 14:21:03,850 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749718263846_4477.txt
2025-06-12 14:21:03,859 - signal_handler - INFO - [msg_1749718263846_4477] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:21:03,912 - signal_handler - INFO - [msg_1749718263846_4477] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:21:04,015 - signal_handler - INFO - [msg_1749718263846_4477] Message sent directly successfully using entity
2025-06-12 14:21:04,023 - signal_handler - INFO - [msg_1749718263846_4477] Message sent directly successfully
2025-06-12 14:21:04,024 - signal_handler - INFO - [msg_1749718264024_7459] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (SIMULATION) - D6XWB3MG

 Timestamp: 2025-06-12 14:21:04
[REPEAT] Tr...
2025-06-12 14:21:04,025 - signal_handler - INFO - [msg_1749718264024_7459] Message queued with normal priority
2025-06-12 14:21:04,061 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.17s (interval: 0.50s)
2025-06-12 14:21:04,246 - signal_handler - INFO - [msg_1749718264024_7459] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:21:04,298 - signal_handler - INFO - [msg_1749718264024_7459] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:21:04,398 - signal_handler - INFO - [msg_1749718264024_7459] Message sent directly successfully using entity
2025-06-12 14:21:04,398 - signal_handler - INFO - [msg_1749718264024_7459] Message sent successfully via direct send method
2025-06-12 14:21:04,399 - signal_handler - INFO - [msg_1749718264024_7459] Message sent successfully on attempt 1
2025-06-12 14:21:35,958 - signal_handler - INFO - Received sell notification: [PURPLE] [SIM SELL [?] TP1 HIT (25.8% >= 20.0%)] [?] 2025-06-12 14:21:35
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]
[?] Trig...
2025-06-12 14:21:35,959 - signal_handler - INFO - [msg_1749718295958_6887] [SELL NOTIFICATION]:  [SIM SELL [?] TP1 HIT (25.8% >= 20.0%)] [?] 2025-06-12 14:21:35

 Triggered by: solana signal alert...
2025-06-12 14:21:35,960 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749718295958_6887.txt
2025-06-12 14:21:35,961 - signal_handler - INFO - [msg_1749718295958_6887] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:21:36,066 - signal_handler - INFO - [msg_1749718295958_6887] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:21:36,197 - signal_handler - INFO - [msg_1749718295958_6887] Message sent directly successfully using entity
2025-06-12 14:21:36,197 - signal_handler - INFO - [msg_1749718295958_6887] Message sent directly successfully
2025-06-12 14:21:36,198 - signal_handler - INFO - [msg_1749718296198_6871] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-12 14:21:36
[REPEAT] Trade: SIMULATION

[MONEY...
2025-06-12 14:21:36,199 - signal_handler - INFO - [msg_1749718296198_6871] Message queued with normal priority
2025-06-12 14:21:36,289 - signal_handler - INFO - [msg_1749718296198_6871] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:21:36,355 - signal_handler - INFO - [msg_1749718296198_6871] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:21:36,440 - signal_handler - INFO - [msg_1749718296198_6871] Message sent directly successfully using entity
2025-06-12 14:21:36,441 - signal_handler - INFO - [msg_1749718296198_6871] Message sent successfully via direct send method
2025-06-12 14:21:36,441 - signal_handler - INFO - [msg_1749718296198_6871] Message sent successfully on attempt 1
2025-06-12 14:28:34,396 - signal_handler - INFO - Received message from channel -1002017857449: ****** ******** ******** ******** ******** ********

******** ****$ASRR****

 ********  Called 2M5 *...
2025-06-12 14:28:34,397 - signal_handler - INFO - Setting channel_identifier to 'PEPE CALLS' based on chat_id -1002017857449
2025-06-12 14:28:34,397 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 14:28:34,398 - signal_handler - INFO - No token addresses found in message
2025-06-12 14:28:34,400 - signal_handler - WARNING - Could not extract token address from message: **[?]**** ****[?]**** ****[?]**** ****[?]**** ****[?]**** ****[?]****

****[COIN]**** ****$ASRR****

 ****[?]****  Call... (processing time: 0.000s)
2025-06-12 14:28:34,400 - signal_handler - DEBUG - No token address found in message from PEPE CALLS: **[?]**** ****[?]**** ****[?]**** ****[?]**** ****[?]**** ****[?]****

****[COIN]**** ****$ASRR****

 ****[?]****  Call...
