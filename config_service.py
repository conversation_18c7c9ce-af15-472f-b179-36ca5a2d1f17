"""
OPTIMIZED: Centralized Configuration Service
Provides singleton access to configuration with caching and validation
"""

import json
import os
import time
import logging
from typing import Dict, Any, Optional
from threading import Lock

logger = logging.getLogger(__name__)


class ConfigService:
    """
    Singleton configuration service with intelligent caching and validation.
    Eliminates duplicate config loading across the codebase.
    """
    
    _instance: Optional['ConfigService'] = None
    _lock = Lock()
    
    def __new__(cls) -> 'ConfigService':
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._initialized = False
        return cls._instance
    
    def __init__(self):
        if self._initialized:
            return
            
        self._config_cache: Dict[str, Any] = {}
        self._cache_timestamp = 0
        self._cache_duration = 300  # 5 minutes
        self._config_path = "finalconfig.json"
        self._initialized = True
        
        # Load initial config
        self._load_config()
    
    def _load_config(self) -> bool:
        """Load configuration from file with error handling"""
        try:
            if not os.path.exists(self._config_path):
                logger.error(f"Configuration file not found: {self._config_path}")
                return False
                
            with open(self._config_path, 'r') as f:
                self._config_cache = json.load(f)
                
            self._cache_timestamp = time.time()
            logger.debug(f"Configuration loaded successfully from {self._config_path}")
            return True
            
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in configuration file: {e}")
            return False
        except Exception as e:
            logger.error(f"Error loading configuration: {e}")
            return False
    
    def _is_cache_valid(self) -> bool:
        """Check if cached configuration is still valid"""
        return (time.time() - self._cache_timestamp) < self._cache_duration
    
    def get_config(self, reload: bool = False) -> Dict[str, Any]:
        """
        Get complete configuration dictionary
        
        Args:
            reload: Force reload from file
            
        Returns:
            Complete configuration dictionary
        """
        if reload or not self._is_cache_valid():
            self._load_config()
        return self._config_cache.copy()
    
    def get(self, section: str, key: str, default: Any = None) -> Any:
        """
        Get specific configuration value
        
        Args:
            section: Configuration section name
            key: Configuration key name
            default: Default value if not found
            
        Returns:
            Configuration value or default
        """
        if not self._is_cache_valid():
            self._load_config()
            
        return self._config_cache.get(section, {}).get(key, default)
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """
        Get entire configuration section
        
        Args:
            section: Section name
            
        Returns:
            Section dictionary or empty dict
        """
        if not self._is_cache_valid():
            self._load_config()
            
        return self._config_cache.get(section, {}).copy()
    
    def get_trading_settings(self) -> Dict[str, Any]:
        """Get trading settings with validation"""
        return self.get_section('trading_settings')
    
    def get_transaction_settings(self) -> Dict[str, Any]:
        """Get transaction settings for fee calculations"""
        trading_settings = self.get_trading_settings()
        return trading_settings.get('transaction_settings', {})
    
    def get_external_fees(self) -> Dict[str, float]:
        """
        Get external fee configuration for trading
        
        Returns:
            Dictionary with fee configuration
        """
        transaction_settings = self.get_transaction_settings()
        
        return {
            'buy_tip_sol': transaction_settings.get('buy_tip_sol', 0.005),
            'gas_price_sol': transaction_settings.get('gas_price_sol', 0.005),
            'handling_fee_percent': transaction_settings.get('handling_fee_percent', 1.0),
            'platform_fee_percent': transaction_settings.get('platform_fee_percent', 1.0)
        }
    
    def get_api_settings(self) -> Dict[str, Any]:
        """Get API configuration settings"""
        return self.get_section('api_settings')
    
    def get_telegram_settings(self) -> Dict[str, Any]:
        """Get Telegram configuration settings"""
        return self.get_section('telegram_settings')
    
    def reload_config(self) -> bool:
        """Force reload configuration from file"""
        return self._load_config()
    
    def is_valid(self) -> bool:
        """Check if configuration is loaded and valid"""
        return bool(self._config_cache) and self._is_cache_valid()


# OPTIMIZED: Global singleton instance for easy access
config_service = ConfigService()


def get_config_service() -> ConfigService:
    """Get the global configuration service instance"""
    return config_service


# OPTIMIZED: Convenience functions for common operations
def get_trading_config() -> Dict[str, Any]:
    """Quick access to trading configuration"""
    return config_service.get_trading_settings()


def get_external_fees() -> Dict[str, float]:
    """Quick access to external fee configuration"""
    return config_service.get_external_fees()


def get_api_config() -> Dict[str, Any]:
    """Quick access to API configuration"""
    return config_service.get_api_settings()


def reload_all_config() -> bool:
    """Force reload all configuration"""
    return config_service.reload_config()
