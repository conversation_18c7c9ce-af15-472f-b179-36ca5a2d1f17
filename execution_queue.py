import asyncio
import logging
import time
from typing import Dict, Any, List, Optional, Callable, Set
from datetime import datetime

logger = logging.getLogger(__name__)

class TradeExecutionQueue:
    """
    A priority queue for trade execution with support for sell priority.

    This queue ensures that sell orders (especially stop-losses) are processed
    before buy orders, and that orders are processed in priority order.
    """

    def __init__(self, trade_executor, max_queue_size: int = 100, num_workers: int = 2):
        self.trade_executor = trade_executor
        self.max_queue_size = max_queue_size
        self.num_workers = num_workers

        # Create separate queues for buy and sell orders
        self.buy_queue = asyncio.PriorityQueue(maxsize=max_queue_size)
        self.sell_queue = asyncio.PriorityQueue(maxsize=max_queue_size)

        # Worker tasks
        self.workers: List[asyncio.Task] = []
        self.is_running = False

        # Track tokens being processed to prevent duplicates
        self.processing_tokens: Set[str] = set()
        self.token_lock = asyncio.Lock()

        # Statistics
        self.stats = {
            'total_buys_processed': 0,
            'total_sells_processed': 0,
            'successful_buys': 0,
            'successful_sells': 0,
            'failed_buys': 0,
            'failed_sells': 0,
            'avg_buy_time': 0.0,
            'avg_sell_time': 0.0,
            'buy_times': [],
            'sell_times': []
        }

    async def start(self):
        """Start the execution queue workers."""
        if self.is_running:
            logger.warning("Execution queue is already running")
            return

        self.is_running = True
        logger.info(f"Starting execution queue with {self.num_workers} workers")

        # Create and start workers
        for i in range(self.num_workers):
            worker = asyncio.create_task(self._worker_loop(i))
            worker.set_name(f"execution_worker_{i}")
            self.workers.append(worker)
            logger.info(f"Started execution worker {i}")

    async def stop(self):
        """Stop the execution queue workers."""
        if not self.is_running:
            logger.warning("Execution queue is not running")
            return

        logger.info("Stopping execution queue")
        self.is_running = False

        # Cancel all workers
        for worker in self.workers:
            worker.cancel()

        # Wait for all workers to finish
        try:
            await asyncio.gather(*self.workers, return_exceptions=True)
        except asyncio.CancelledError:
            pass

        self.workers = []
        logger.info("Execution queue stopped")

    async def queue_buy(self, token_address: str, amount: float, price: float,
                       slippage_bps: int, is_simulation: bool = False,
                       priority: int = 10, callback: Optional[Callable] = None,
                       event_id: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Queue a buy order for execution.

        Args:
            token_address: The token address to buy
            amount: The amount of SOL to spend
            price: The current price of the token
            slippage_bps: Slippage tolerance in basis points
            is_simulation: Whether this is a simulation
            priority: Priority of the order (lower number = higher priority)
            callback: Optional callback function to call with the result
            event_id: Optional unique event identifier for this buy action
            metadata: Optional metadata to store with the order (signal source, etc.)

        Returns:
            bool: True if the order was queued, False if it was rejected
        """
        if not token_address:
            logger.warning("Cannot queue buy for empty token address")
            return False

        # Check if token is already being processed
        # Note: This processing_tokens check is for concurrent processing in this queue,
        # not for preventing duplicate notifications across the system.
        # The event_id mechanism handled by TradeExecutor will manage notification duplication.
        async with self.token_lock:
            if token_address in self.processing_tokens:
                logger.info(f"Token {token_address} already being processed by this queue, skipping buy. Event ID: {event_id}")
                return False

            # SURGICAL FIX: Atomic operation to mark token as being processed
            self.processing_tokens.add(token_address)

        # Create buy order
        buy_order = {
            'type': 'buy',
            'token_address': token_address,
            'amount': amount,
            'price': price,
            'slippage_bps': slippage_bps,
            'is_simulation': is_simulation,
            'timestamp': time.time(),
            'callback': callback,
            'event_id': event_id,
            'metadata': metadata or {}  # Use provided metadata or empty dict
        }

        # Queue the buy order
        try:
            await self.buy_queue.put((priority, buy_order))
            logger.info(f"Queued buy order for {token_address} with priority {priority}")
            return True
        except asyncio.QueueFull:
            logger.warning(f"Buy queue is full, rejecting buy for {token_address}")
            # SURGICAL FIX: Atomic safe removal from processing set
            async with self.token_lock:
                self.processing_tokens.discard(token_address)  # discard() won't raise KeyError
            return False

    async def queue_sell(self, token_address: str, token_amount: float, entry_price: float,
                        slippage_bps: int, sell_fraction: float = 1.0, is_simulation: bool = False,
                        priority: int = 0, callback: Optional[Callable] = None,
                        event_id: Optional[str] = None) -> bool:
        """
        Queue a sell order for execution.

        Args:
            token_address: The token address to sell
            token_amount: The amount of tokens to sell
            entry_price: The entry price of the position
            slippage_bps: Slippage tolerance in basis points
            sell_fraction: Fraction of the position to sell (0.0-1.0)
            is_simulation: Whether this is a simulation
            priority: Priority of the order (lower number = higher priority)
            callback: Optional callback function to call with the result
            event_id: Optional unique event identifier for this sell action

        Returns:
            bool: True if the order was queued, False if it was rejected
        """
        if not token_address:
            logger.warning("Cannot queue sell for empty token address")
            return False

        # Create sell order
        sell_order = {
            'type': 'sell',
            'token_address': token_address,
            'token_amount': token_amount,
            'entry_price': entry_price,
            'slippage_bps': slippage_bps,
            'sell_fraction': sell_fraction,
            'is_simulation': is_simulation,
            'timestamp': time.time(),
            'callback': callback,
            'event_id': event_id
        }

        # Queue the sell order
        try:
            await self.sell_queue.put((priority, sell_order))
            logger.info(f"Queued sell order for {token_address} with priority {priority}")
            return True
        except asyncio.QueueFull:
            logger.warning(f"Sell queue is full, rejecting sell for {token_address}")
            return False

    async def _worker_loop(self, worker_id: int):
        """Worker loop for processing trade executions."""
        logger.info(f"Execution worker {worker_id} started")

        while self.is_running:
            try:
                # SURGICAL FIX: Emergency sells get immediate processing
                emergency_processed = False

                # Check for emergency sells first (priority 0-2)
                if not self.sell_queue.empty():
                    try:
                        # Peek at the next sell order without removing it
                        priority, sell_order = await asyncio.wait_for(self.sell_queue.get(), timeout=0.001)

                        # SURGICAL: Emergency priority bypass (immediate processing)
                        if priority <= 2:  # Emergency priority
                            logger.warning(f"EMERGENCY SELL: Processing {sell_order['token_address']} immediately")
                            await self._process_sell_order(sell_order, worker_id)
                            self.sell_queue.task_done()
                            emergency_processed = True
                        else:
                            # Put back non-emergency sell for normal processing
                            await self.sell_queue.put((priority, sell_order))
                    except asyncio.TimeoutError:
                        pass  # No sell orders available

                # Normal processing if no emergency
                if not emergency_processed:
                    # Always check sell queue first (sells have priority)
                    if not self.sell_queue.empty():
                        _, sell_order = await self.sell_queue.get()
                        await self._process_sell_order(sell_order, worker_id)
                        self.sell_queue.task_done()
                    # If no sells, process buys
                    elif not self.buy_queue.empty():
                        _, buy_order = await self.buy_queue.get()
                        await self._process_buy_order(buy_order, worker_id)
                        self.buy_queue.task_done()
                    # If both queues are empty, wait a bit
                    else:
                        await asyncio.sleep(0.1)
            except asyncio.CancelledError:
                logger.info(f"Execution worker {worker_id} cancelled")
                break
            except Exception as e:
                logger.error(f"Error in execution worker {worker_id}: {e}", exc_info=True)
                await asyncio.sleep(1)  # Prevent tight loop on error

        logger.info(f"Execution worker {worker_id} stopped")

    async def _process_buy_order(self, order: Dict[str, Any], worker_id: int):
        """Process a buy order."""
        token_address = order['token_address']
        amount = order['amount']
        price = order['price']
        slippage_bps = order['slippage_bps']
        is_simulation = order['is_simulation']
        callback = order.get('callback')
        event_id = order.get('event_id')
        metadata = order.get('metadata', {})

        logger.info(f"Worker {worker_id} executing buy for {token_address} (Event ID: {event_id})")
        start_time = time.time()

        # Store metadata in trade executor's event_data dictionary
        if event_id:
            self.trade_executor.event_data[event_id] = {
                'metadata': metadata,
                'timestamp': time.time()
            }
            logger.debug(f"Stored metadata for event {event_id} in trade executor")

        try:
            # Execute the buy
            success = await self.trade_executor.execute_buy(
                token_address=token_address,
                amount=amount,
                price=price,
                slippage_bps=slippage_bps,
                is_simulation=is_simulation,
                event_id=event_id,
                skip_notification=True  # Skip notification from TradeExecutor since BotController will send it
            )

            # Update statistics
            processing_time = time.time() - start_time
            self.stats['total_buys_processed'] += 1
            self.stats['buy_times'].append(processing_time)

            # Keep only the last 100 processing times
            if len(self.stats['buy_times']) > 100:
                self.stats['buy_times'] = self.stats['buy_times'][-100:]

            # Calculate average processing time
            self.stats['avg_buy_time'] = sum(self.stats['buy_times']) / len(self.stats['buy_times'])

            if success:
                self.stats['successful_buys'] += 1
                logger.info(f"Worker {worker_id} successfully executed buy for {token_address} in {processing_time:.2f}s")
            else:
                self.stats['failed_buys'] += 1
                logger.warning(f"Worker {worker_id} failed to execute buy for {token_address}")

            # Execute callback if provided
            if callback:
                try:
                    await callback(success)
                except Exception as e:
                    logger.error(f"Error in buy callback for {token_address}: {e}")

        except Exception as e:
            logger.error(f"Worker {worker_id} error executing buy for {token_address}: {e}", exc_info=True)
            self.stats['failed_buys'] += 1

            # Execute callback with failure if provided
            if callback:
                try:
                    await callback(False)
                except Exception as e:
                    logger.error(f"Error in buy callback for {token_address}: {e}")

        finally:
            # Remove token from processing set
            async with self.token_lock:
                if token_address in self.processing_tokens:
                    self.processing_tokens.remove(token_address)

    async def _process_sell_order(self, order: Dict[str, Any], worker_id: int):
        """Process a sell order."""
        token_address = order['token_address']
        token_amount = order['token_amount']
        entry_price = order['entry_price']
        slippage_bps = order['slippage_bps']
        sell_fraction = order['sell_fraction']
        is_simulation = order['is_simulation']
        callback = order.get('callback')
        event_id = order.get('event_id')

        logger.info(f"Worker {worker_id} executing sell for {token_address} (Fraction: {sell_fraction*100:.1f}%, EventID: {event_id})")
        start_time = time.time()

        try:
            # Execute the sell
            success = await self.trade_executor.execute_sell(
                token_address=token_address,
                token_amount=token_amount,
                entry_price=entry_price,
                slippage_bps=slippage_bps,
                sell_fraction=sell_fraction,
                is_simulation=is_simulation,
                event_id=event_id
            )

            # Update statistics
            processing_time = time.time() - start_time
            self.stats['total_sells_processed'] += 1
            self.stats['sell_times'].append(processing_time)

            # Keep only the last 100 processing times
            if len(self.stats['sell_times']) > 100:
                self.stats['sell_times'] = self.stats['sell_times'][-100:]

            # Calculate average processing time
            self.stats['avg_sell_time'] = sum(self.stats['sell_times']) / len(self.stats['sell_times'])

            if success:
                self.stats['successful_sells'] += 1
                logger.info(f"Worker {worker_id} successfully executed sell for {token_address} in {processing_time:.2f}s")
            else:
                self.stats['failed_sells'] += 1
                logger.warning(f"Worker {worker_id} failed to execute sell for {token_address}")

            # Execute callback if provided
            if callback:
                try:
                    await callback(success)
                except Exception as e:
                    logger.error(f"Error in sell callback for {token_address}: {e}")

        except Exception as e:
            logger.error(f"Worker {worker_id} error executing sell for {token_address}: {e}", exc_info=True)
            self.stats['failed_sells'] += 1

            # Execute callback with failure if provided
            if callback:
                try:
                    await callback(False)
                except Exception as e:
                    logger.error(f"Error in sell callback for {token_address}: {e}")
