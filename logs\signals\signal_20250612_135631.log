2025-06-12 13:56:31,631 - signal_handler - DEBUG - Loaded Telegram API ID: 27993698
2025-06-12 13:56:31,631 - signal_handler - DEBUG - Loaded Telegram API Hash: ********************************
2025-06-12 13:56:31,632 - signal_handler - INFO - Session string file not found: c:\Users\<USER>\Downloads\billy who\session_string.json. Will create a new session.
2025-06-12 13:56:31,632 - signal_handler - INFO - Using unique session name with timestamp: trading_sim_session_1749716791
2025-06-12 13:56:31,634 - signal_handler - INFO - Using session path: c:\Users\<USER>\Downloads\billy who\trading_sim_session_1749716791
2025-06-12 13:56:31,637 - signal_handler - INFO - Signal queue initialized with size: 0
2025-06-12 13:56:31,655 - signal_handler - INFO - Direct signal callback registered
2025-06-12 13:56:45,458 - signal_handler - INFO - API ID loaded: True
2025-06-12 13:56:45,458 - signal_handler - INFO - API Hash loaded: True
2025-06-12 13:56:45,459 - signal_handler - INFO - Phone number loaded: True
2025-06-12 13:56:45,459 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-12 13:56:45,459 - signal_handler - INFO - Creating new Telegram client (attempt 1/3)
2025-06-12 13:56:45,459 - signal_handler - INFO - Using file-based session
2025-06-12 13:56:45,501 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-12 13:56:48,970 - signal_handler - INFO - Checking authorization status...
2025-06-12 13:56:49,258 - signal_handler - INFO - Authorization required. Sending code request...
2025-06-12 13:57:00,857 - signal_handler - INFO - Signing in with code (attempt 1/3)...
2025-06-12 13:57:01,083 - signal_handler - INFO - Got valid session string (length: 353)
2025-06-12 13:57:01,088 - signal_handler - INFO - Saved session string to file: c:\Users\<USER>\Downloads\billy who\session_string.json (length: 353)
2025-06-12 13:57:01,090 - signal_handler - INFO - Session string file saved successfully (size: 375 bytes)
2025-06-12 13:57:01,091 - signal_handler - INFO - Saved session string for future use
2025-06-12 13:57:01,145 - signal_handler - INFO - Successfully retrieved own user ID: 768056701 after authorization
2025-06-12 13:57:01,145 - signal_handler - INFO - Successfully connected and authorized Telegram
2025-06-12 13:57:01,198 - signal_handler - INFO - Successfully retrieved own user ID: 768056701 after authorization
2025-06-12 13:57:26,063 - signal_handler - INFO - API ID loaded: True
2025-06-12 13:57:26,063 - signal_handler - INFO - API Hash loaded: True
2025-06-12 13:57:26,063 - signal_handler - INFO - Phone number loaded: True
2025-06-12 13:57:26,064 - signal_handler - INFO - Session file is valid and not locked
2025-06-12 13:57:26,064 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-12 13:57:26,065 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-12 13:57:26,066 - signal_handler - INFO - Checking authorization status...
2025-06-12 13:57:26,121 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 13:57:26,122 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-12 13:57:26,122 - signal_handler - INFO - Checking authorization status...
2025-06-12 13:57:26,177 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 13:57:26,177 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-12 13:57:26,178 - signal_handler - INFO - Checking authorization status...
2025-06-12 13:57:26,229 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 13:57:26,230 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-12 13:57:26,230 - signal_handler - INFO - Sending test message to verify connection to channel -1002525039395
2025-06-12 13:57:26,231 - signal_handler - INFO - Sending test message directly to channel ID: -1002525039395
2025-06-12 13:57:26,372 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-12 13:57:26,379 - signal_handler - INFO - [msg_1749716846376_5889] Queuing Telegram message:  PORTFOLIO UPDATE [?] PERIODIC UPDATE

 Timestamp: 2025-06-12 13:57:26
[REPEAT] Trade: SIMULATION

[...
2025-06-12 13:57:26,380 - signal_handler - INFO - Started Telegram message queue processor
2025-06-12 13:57:26,380 - signal_handler - INFO - [msg_1749716846376_5889] Message queued with normal priority
2025-06-12 13:57:26,380 - signal_handler - INFO - Message queue processor started with adaptive rate limiting
2025-06-12 13:57:26,381 - signal_handler - INFO - [msg_1749716846376_5889] Resolving info channel entity for ID: -1002525039395
2025-06-12 13:57:26,434 - signal_handler - INFO - [msg_1749716846376_5889] Successfully resolved info channel entity: Bot Info 1
2025-06-12 13:57:26,517 - signal_handler - INFO - [msg_1749716846376_5889] Message sent directly successfully using entity
2025-06-12 13:57:26,518 - signal_handler - INFO - [msg_1749716846376_5889] Message sent successfully via direct send method
2025-06-12 13:57:26,518 - signal_handler - INFO - [msg_1749716846376_5889] Message sent successfully on attempt 1
2025-06-12 13:57:26,887 - signal_handler - WARNING - [WARNING] Using channel IDs directly (no entities resolved)
2025-06-12 13:57:26,888 - signal_handler - WARNING - Added event handler using channel IDs (entities not resolved yet)
2025-06-12 13:57:26,888 - signal_handler - INFO - Signal listener started in background task. Waiting for messages...
2025-06-12 13:57:26,889 - signal_handler - INFO - Direct signal callback registered
2025-06-12 13:57:26,891 - signal_handler - INFO - [msg_1749716846890_4907] [STARTUP NOTIFICATION]: [ROCKET] Bot Started
[REFRESH] Mode: SIMULATION
[GRAPH] Strategy: default
[MONEY] Starting Capital: ...
2025-06-12 13:57:26,891 - signal_handler - INFO - [msg_1749716846890_4907] Resolving info channel entity for ID: -1002525039395
2025-06-12 13:57:26,893 - signal_handler - INFO - Signal listener running (attempt 1/3).
2025-06-12 13:57:26,950 - signal_handler - INFO - [msg_1749716846890_4907] Successfully resolved info channel entity: Bot Info 1
2025-06-12 13:57:27,033 - signal_handler - INFO - [msg_1749716846890_4907] Message sent directly successfully using entity
2025-06-12 13:57:27,033 - signal_handler - INFO - [msg_1749716846890_4907] Message sent directly successfully
2025-06-12 13:57:33,734 - signal_handler - INFO - Received message from channel -1002177594166: FdbPeMhc1RjXinePUqyn1TYTzcwZcrknsDBwNWCUpump...
2025-06-12 13:57:33,734 - signal_handler - INFO - Setting channel_identifier to 'OxyZen Calls' based on chat_id -1002177594166
2025-06-12 13:57:33,735 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 13:57:33,738 - signal_handler - INFO - Found contract address with 'pump' suffix: FdbPeMhc1RjXinePUqyn1TYTzcwZcrknsDBwNWCUpump
2025-06-12 13:57:33,738 - signal_handler - INFO - Using 'pump' address as highest priority: FdbPeMhc1RjXinePUqyn1TYTzcwZcrknsDBwNWCUpump
2025-06-12 13:57:33,739 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: FdbPeMhc1RjXinePUqyn1TYTzcwZcrknsDBwNWCUpump
2025-06-12 13:57:33,740 - signal_handler - INFO - Detected signal from OxyZen Calls for token: FdbPeMhc1RjXinePUqyn1TYTzcwZcrknsDBwNWCUpump
2025-06-12 13:57:33,740 - signal_handler - INFO - Extracted signal: Token=FdbPeMhc1RjXinePUqyn1TYTzcwZcrknsDBwNWCUpump, Source=OxyZen Calls, Metrics Count=1
2025-06-12 13:57:33,740 - signal_handler - WARNING - LOW SIGNAL VOLUME: GMGN - 0/8 expected this hour
2025-06-12 13:57:33,741 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solbix - 0/2 expected this hour
2025-06-12 13:57:33,741 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solana Early Trending - 0/1 expected this hour
2025-06-12 13:57:33,741 - signal_handler - INFO - Signal processing health: 1 received, 1 processed, 0 dropped (100.0% success rate)
2025-06-12 13:57:33,742 - signal_handler - INFO - Added signal to queue: FdbPeMhc1RjXinePUqyn1TYTzcwZcrknsDBwNWCUpump from OxyZen Calls with confidence 0.5, GMGN channel: False
2025-06-12 13:57:33,742 - signal_handler - INFO - Calling direct signal callback for FdbPeMhc1RjXinePUqyn1TYTzcwZcrknsDBwNWCUpump
