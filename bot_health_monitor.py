#!/usr/bin/env python3
"""
Bot Health Monitor
Comprehensive health monitoring and diagnostics for the Solana trading bot
"""

import asyncio
import logging
import time
import psutil
import os
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List
from config_manager import ConfigManager

logger = logging.getLogger(__name__)

class BotHealthMonitor:
    """Comprehensive health monitoring for the trading bot"""
    
    def __init__(self, config_manager: ConfigManager):
        self.config = config_manager
        self.start_time = time.time()
        self.health_data = {
            'system': {},
            'memory': {},
            'performance': {},
            'api': {},
            'trading': {},
            'errors': []
        }
        
    async def run_comprehensive_health_check(self) -> Dict[str, Any]:
        """Run a comprehensive health check of all bot components"""
        logger.info("🔍 Starting comprehensive bot health check...")
        
        health_report = {
            'timestamp': datetime.now().isoformat(),
            'uptime_seconds': time.time() - self.start_time,
            'status': 'HEALTHY',
            'issues': [],
            'recommendations': [],
            'metrics': {}
        }
        
        try:
            # System health
            await self._check_system_health(health_report)
            
            # Memory usage
            await self._check_memory_usage(health_report)
            
            # Configuration validation
            await self._check_configuration(health_report)
            
            # File system health
            await self._check_file_system(health_report)
            
            # Performance metrics
            await self._check_performance_metrics(health_report)
            
            # Determine overall status
            self._determine_overall_status(health_report)
            
            logger.info(f"✅ Health check completed. Status: {health_report['status']}")
            return health_report
            
        except Exception as e:
            logger.error(f"❌ Error during health check: {e}")
            health_report['status'] = 'ERROR'
            health_report['issues'].append(f"Health check failed: {str(e)}")
            return health_report
    
    async def _check_system_health(self, report: Dict[str, Any]):
        """Check system resource usage"""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            report['metrics']['cpu_usage_percent'] = cpu_percent
            
            if cpu_percent > 80:
                report['issues'].append(f"High CPU usage: {cpu_percent:.1f}%")
                report['recommendations'].append("Consider reducing worker count or analysis frequency")
            
            # Memory usage
            memory = psutil.virtual_memory()
            report['metrics']['memory_usage_percent'] = memory.percent
            report['metrics']['memory_available_gb'] = memory.available / (1024**3)
            
            # Adjusted threshold for development machine with 16GB RAM
            if memory.percent > 90:
                report['issues'].append(f"High memory usage: {memory.percent:.1f}%")
                report['recommendations'].append("Close unnecessary applications or restart system")
            elif memory.percent > 85:
                report['issues'].append(f"Elevated memory usage: {memory.percent:.1f}% (development environment)")
                report['recommendations'].append("Consider closing browser tabs or VS Code windows if needed")
            
            # Disk usage
            disk = psutil.disk_usage('.')
            report['metrics']['disk_usage_percent'] = (disk.used / disk.total) * 100
            report['metrics']['disk_free_gb'] = disk.free / (1024**3)
            
            if (disk.used / disk.total) > 0.9:
                report['issues'].append("Low disk space")
                report['recommendations'].append("Clean up old log files and simulation data")
                
        except Exception as e:
            report['issues'].append(f"System health check failed: {str(e)}")
    
    async def _check_memory_usage(self, report: Dict[str, Any]):
        """Check for potential memory leaks"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            
            report['metrics']['process_memory_mb'] = memory_info.rss / (1024**2)
            report['metrics']['process_memory_percent'] = process.memory_percent()
            
            # Check for excessive memory usage
            if memory_info.rss > 1024**3:  # 1GB
                report['issues'].append("High process memory usage (>1GB)")
                report['recommendations'].append("Check for memory leaks in data structures")
            
            # Check open file descriptors
            try:
                open_files = len(process.open_files())
                report['metrics']['open_files'] = open_files
                
                if open_files > 100:
                    report['issues'].append(f"Many open files: {open_files}")
                    report['recommendations'].append("Check for unclosed file handles")
            except:
                pass  # Not available on all systems
                
        except Exception as e:
            report['issues'].append(f"Memory check failed: {str(e)}")
    
    async def _check_configuration(self, report: Dict[str, Any]):
        """Validate configuration settings"""
        try:
            # Check critical configuration values
            trading_settings = self.config.get_section('trading_settings')
            
            if not trading_settings:
                report['issues'].append("Missing trading_settings in configuration")
                return
            
            # Check trading capital settings
            sol_trade_amount = trading_settings.get('sol_trade_amount', 0)
            total_sol_capital = trading_settings.get('total_sol_capital', 0)
            max_concurrent_trades = trading_settings.get('max_concurrent_trades', 0)
            
            # SURGICAL FIX: Critical capital validation with system protection
            if sol_trade_amount * max_concurrent_trades > total_sol_capital:
                report['issues'].append("CRITICAL: Trade size * max trades exceeds total capital")
                report['recommendations'].append("URGENT: Adjust trade size or max concurrent trades before starting")
                # SURGICAL: Add critical flag for immediate attention
                report['critical_config_error'] = True
            
            # Check rug protection settings
            rug_protection = trading_settings.get('rug_protection', {})
            if not rug_protection.get('enabled', False):
                report['issues'].append("Rug protection is disabled")
                report['recommendations'].append("Enable rug protection for safety")
            
            # Check API rate limits
            api_limits = self.config.get_section('api_rate_limits')
            if api_limits:
                dex_rpm = api_limits.get('dexscreener_rpm', 0)
                helius_rpm = api_limits.get('helius_rpc_rpm', 0)
                
                if dex_rpm > 300:
                    report['issues'].append("DexScreener RPM too high (>300)")
                    report['recommendations'].append("Reduce DexScreener rate limit to avoid bans")
                
                if helius_rpm > 600:
                    report['issues'].append("Helius RPM too high (>600)")
                    report['recommendations'].append("Reduce Helius rate limit")
            
            report['metrics']['config_validation'] = 'PASSED'
            
        except Exception as e:
            report['issues'].append(f"Configuration check failed: {str(e)}")
    
    async def _check_file_system(self, report: Dict[str, Any]):
        """Check file system health and log files"""
        try:
            # Check log directory size
            log_dir = 'logs'
            if os.path.exists(log_dir):
                total_size = 0
                file_count = 0
                
                for root, dirs, files in os.walk(log_dir):
                    for file in files:
                        file_path = os.path.join(root, file)
                        try:
                            total_size += os.path.getsize(file_path)
                            file_count += 1
                        except:
                            pass
                
                report['metrics']['log_files_count'] = file_count
                report['metrics']['log_files_size_mb'] = total_size / (1024**2)
                
                if total_size > 500 * 1024**2:  # 500MB
                    report['issues'].append("Large log directory (>500MB)")
                    report['recommendations'].append("Clean up old log files")
            
            # Check simulation logs
            sim_dir = 'simulation_logs'
            if os.path.exists(sim_dir):
                sim_files = len([f for f in os.listdir(sim_dir) if f.endswith('.csv')])
                report['metrics']['simulation_files'] = sim_files
                
                if sim_files > 100:
                    report['issues'].append("Many simulation files")
                    report['recommendations'].append("Archive old simulation data")
            
            # Check for critical files
            critical_files = ['finalconfig.json', '.env']
            for file in critical_files:
                if not os.path.exists(file):
                    report['issues'].append(f"Missing critical file: {file}")
                    
        except Exception as e:
            report['issues'].append(f"File system check failed: {str(e)}")
    
    async def _check_performance_metrics(self, report: Dict[str, Any]):
        """Check performance-related metrics"""
        try:
            # Check recent error logs
            error_count = 0
            recent_errors = []
            
            try:
                if os.path.exists('error_log.csv'):
                    # SURGICAL FIX: Read only last 100 lines to prevent memory issues
                    with open('error_log.csv', 'r') as f:
                        # Efficient tail reading - seek to end and read backwards
                        f.seek(0, 2)  # Go to end of file
                        file_size = f.tell()

                        # Read last ~10KB (approximately 100-200 lines)
                        read_size = min(10240, file_size)
                        f.seek(max(0, file_size - read_size))

                        # Read and split into lines
                        tail_content = f.read()
                        lines = tail_content.split('\n')[-100:]  # Last 100 lines only

                    # Check last 100 lines for recent errors
                    for line in lines:
                        if line.strip():
                            error_count += 1
                            if len(recent_errors) < 5:
                                recent_errors.append(line.strip())
                
                report['metrics']['recent_errors'] = error_count
                if error_count > 50:
                    report['issues'].append(f"Many recent errors: {error_count}")
                    report['recommendations'].append("Investigate error patterns")
                    
            except:
                pass
            
            # Check trade performance if available
            try:
                if os.path.exists('simulation_logs/trade_log.csv'):
                    with open('simulation_logs/trade_log.csv', 'r') as f:
                        lines = f.readlines()
                    
                    if len(lines) > 1:  # Has data beyond header
                        report['metrics']['total_trades'] = len(lines) - 1
                        
                        # Simple win rate calculation
                        wins = 0
                        for line in lines[1:]:  # Skip header
                            if 'pnl_percent' in line and ',' in line:
                                try:
                                    parts = line.split(',')
                                    pnl_idx = None
                                    for i, part in enumerate(parts):
                                        if 'pnl_percent' in parts[0]:  # Header check
                                            break
                                    # This is a simplified check
                                    wins += 1 if 'SELL' in line and 'profit' in line.lower() else 0
                                except:
                                    pass
                        
                        if len(lines) > 1:
                            win_rate = (wins / (len(lines) - 1)) * 100
                            report['metrics']['estimated_win_rate'] = win_rate
                            
                            if win_rate < 30:
                                report['issues'].append(f"Low win rate: {win_rate:.1f}%")
                                report['recommendations'].append("Review trading strategy")
            except:
                pass
                
        except Exception as e:
            report['issues'].append(f"Performance check failed: {str(e)}")
    
    def _determine_overall_status(self, report: Dict[str, Any]):
        """Determine overall health status"""
        issue_count = len(report['issues'])
        
        if issue_count == 0:
            report['status'] = 'HEALTHY'
        elif issue_count <= 3:
            report['status'] = 'WARNING'
        else:
            report['status'] = 'CRITICAL'
    
    def format_health_report(self, report: Dict[str, Any]) -> str:
        """Format health report for display"""
        lines = []
        lines.append("🏥 BOT HEALTH REPORT")
        lines.append("=" * 50)
        lines.append(f"Status: {report['status']}")
        lines.append(f"Timestamp: {report['timestamp']}")
        lines.append(f"Uptime: {report['uptime_seconds']:.0f} seconds")
        lines.append("")
        
        # Metrics
        if report['metrics']:
            lines.append("📊 METRICS:")
            for key, value in report['metrics'].items():
                if isinstance(value, float):
                    lines.append(f"  {key}: {value:.2f}")
                else:
                    lines.append(f"  {key}: {value}")
            lines.append("")
        
        # Issues
        if report['issues']:
            lines.append("⚠️ ISSUES:")
            for issue in report['issues']:
                lines.append(f"  • {issue}")
            lines.append("")
        
        # Recommendations
        if report['recommendations']:
            lines.append("💡 RECOMMENDATIONS:")
            for rec in report['recommendations']:
                lines.append(f"  • {rec}")
            lines.append("")
        
        lines.append("=" * 50)
        return "\n".join(lines)

async def main():
    """Run standalone health check"""
    config = ConfigManager()
    monitor = BotHealthMonitor(config)
    
    report = await monitor.run_comprehensive_health_check()
    print(monitor.format_health_report(report))
    
    # Save report to file
    with open(f"health_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", 'w') as f:
        json.dump(report, f, indent=2)

if __name__ == "__main__":
    asyncio.run(main())
