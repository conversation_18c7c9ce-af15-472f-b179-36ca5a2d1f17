#!/usr/bin/env python3
"""
Wallet Manager for PumpPortal Trading
Integrates add_wallet.py functionality into the main bot CLI
"""

import os
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv
import base58
from solders.keypair import Keypair

logger = logging.getLogger(__name__)

class WalletManager:
    """Manages wallets stored in .env file"""
    
    def __init__(self):
        self.env_file = '.env'
        load_dotenv(override=True)
    
    def list_wallets(self) -> List[Dict[str, Any]]:
        """List all wallets from .env file"""
        try:
            wallets = []
            load_dotenv(override=True)
            
            # Find all wallet entries
            wallet_names = set()
            for key in os.environ:
                if key.startswith('WALLET_') and key.endswith('_PRIVATE_KEY'):
                    wallet_name = key.replace('WALLET_', '').replace('_PRIVATE_KEY', '')
                    wallet_names.add(wallet_name)
            
            for wallet_name in sorted(wallet_names):
                wallet_info = self.get_wallet_info(wallet_name)
                if wallet_info:
                    wallets.append(wallet_info)
            
            return wallets
            
        except Exception as e:
            logger.error(f"Error listing wallets: {e}")
            return []
    
    def get_wallet_info(self, wallet_name: str) -> Optional[Dict[str, Any]]:
        """Get wallet information"""
        try:
            load_dotenv(override=True)
            
            private_key = os.getenv(f'WALLET_{wallet_name.upper()}_PRIVATE_KEY')
            created_at = os.getenv(f'WALLET_{wallet_name.upper()}_CREATED_AT')
            trades_count = os.getenv(f'WALLET_{wallet_name.upper()}_TRADES_COUNT', '0')
            
            if not private_key:
                return None
            
            # Get public key
            try:
                private_key_bytes = base58.b58decode(private_key)
                if len(private_key_bytes) == 64:
                    keypair = Keypair.from_bytes(private_key_bytes)
                elif len(private_key_bytes) == 32:
                    keypair = Keypair.from_seed(private_key_bytes)
                else:
                    keypair = Keypair.from_bytes(private_key_bytes)
                
                public_key = str(keypair.pubkey())
            except Exception:
                public_key = "Invalid Key"
            
            return {
                'name': wallet_name,
                'public_key': public_key,
                'created_at': created_at or 'Unknown',
                'trades_count': int(trades_count),
                'has_private_key': True
            }
            
        except Exception as e:
            logger.error(f"Error getting wallet info for {wallet_name}: {e}")
            return None
    
    def add_wallet(self, wallet_name: str, private_key: str) -> bool:
        """Add a new wallet to .env file"""
        try:
            # Validate wallet name
            wallet_name = wallet_name.strip().upper()
            if not wallet_name:
                print("❌ Wallet name required")
                return False
            
            # Validate private key
            private_key = private_key.strip()
            if not private_key:
                print("❌ Private key required")
                return False
            
            # Test private key validity
            try:
                private_key_bytes = base58.b58decode(private_key)
                if len(private_key_bytes) == 64:
                    keypair = Keypair.from_bytes(private_key_bytes)
                elif len(private_key_bytes) == 32:
                    keypair = Keypair.from_seed(private_key_bytes)
                else:
                    keypair = Keypair.from_bytes(private_key_bytes)
                
                public_key = str(keypair.pubkey())
                print(f"✅ Valid key - Public key: {public_key}")
                
            except Exception as e:
                print(f"❌ Invalid private key: {e}")
                return False
            
            # Read existing .env content
            env_content = []
            if os.path.exists(self.env_file):
                with open(self.env_file, 'r') as f:
                    env_content = f.readlines()
            
            # Remove existing entries for this wallet
            env_content = [line for line in env_content 
                          if not line.startswith(f'WALLET_{wallet_name}_')]
            
            # Add new wallet entries
            wallet_vars = [
                f'WALLET_{wallet_name}_PRIVATE_KEY={private_key}\n',
                f'WALLET_{wallet_name}_CREATED_AT={datetime.now().isoformat()}\n',
                f'WALLET_{wallet_name}_TRADES_COUNT=0\n'
            ]
            
            env_content.extend(wallet_vars)
            
            # Write back to .env
            with open(self.env_file, 'w') as f:
                f.writelines(env_content)
            
            print(f"✅ Wallet '{wallet_name}' added to .env")
            print(f"📁 File: {os.path.abspath(self.env_file)}")
            
            # Reload environment
            load_dotenv(override=True)
            
            return True
            
        except Exception as e:
            logger.error(f"Error adding wallet: {e}")
            print(f"❌ Error adding wallet: {e}")
            return False
    
    def remove_wallet(self, wallet_name: str) -> bool:
        """Remove a wallet from .env file"""
        try:
            wallet_name = wallet_name.strip().upper()
            
            # Check if wallet exists
            if not self.get_wallet_info(wallet_name):
                print(f"❌ Wallet '{wallet_name}' not found")
                return False
            
            # Read existing .env content
            env_content = []
            if os.path.exists(self.env_file):
                with open(self.env_file, 'r') as f:
                    env_content = f.readlines()
            
            # Remove wallet entries
            original_count = len(env_content)
            env_content = [line for line in env_content 
                          if not line.startswith(f'WALLET_{wallet_name}_')]
            
            if len(env_content) == original_count:
                print(f"❌ No entries found for wallet '{wallet_name}'")
                return False
            
            # Write back to .env
            with open(self.env_file, 'w') as f:
                f.writelines(env_content)
            
            print(f"✅ Wallet '{wallet_name}' removed from .env")
            
            # Reload environment
            load_dotenv(override=True)
            
            return True
            
        except Exception as e:
            logger.error(f"Error removing wallet: {e}")
            print(f"❌ Error removing wallet: {e}")
            return False
    
    def get_default_wallet(self) -> Optional[str]:
        """Get the first available wallet as default"""
        try:
            wallets = self.list_wallets()
            if wallets:
                return wallets[0]['name']
            return None
        except Exception as e:
            logger.error(f"Error getting default wallet: {e}")
            return None
    
    def increment_trades_count(self, wallet_name: str) -> bool:
        """Increment the trades count for a wallet"""
        try:
            wallet_name = wallet_name.upper()
            current_count = int(os.getenv(f'WALLET_{wallet_name}_TRADES_COUNT', '0'))
            new_count = current_count + 1
            
            # Read existing .env content
            env_content = []
            if os.path.exists(self.env_file):
                with open(self.env_file, 'r') as f:
                    env_content = f.readlines()
            
            # Update trades count
            updated = False
            for i, line in enumerate(env_content):
                if line.startswith(f'WALLET_{wallet_name}_TRADES_COUNT='):
                    env_content[i] = f'WALLET_{wallet_name}_TRADES_COUNT={new_count}\n'
                    updated = True
                    break
            
            # If not found, add it
            if not updated:
                env_content.append(f'WALLET_{wallet_name}_TRADES_COUNT={new_count}\n')
            
            # Write back to .env
            with open(self.env_file, 'w') as f:
                f.writelines(env_content)
            
            # Reload environment
            load_dotenv(override=True)
            
            return True
            
        except Exception as e:
            logger.error(f"Error incrementing trades count: {e}")
            return False

def add_wallet_interactive():
    """Interactive wallet addition (from add_wallet.py)"""
    print("🔐 ADD WALLET TO .ENV")
    print("=" * 30)
    
    wallet_manager = WalletManager()
    
    # Get wallet details
    wallet_name = input("Enter wallet name: ").strip()
    if not wallet_name:
        print("❌ Wallet name required")
        return False
    
    private_key = input("Enter private key (base58): ").strip()
    if not private_key:
        print("❌ Private key required")
        return False
    
    return wallet_manager.add_wallet(wallet_name, private_key)

def list_wallets_interactive():
    """Interactive wallet listing"""
    print("\n💼 WALLET LIST")
    print("=" * 50)
    
    wallet_manager = WalletManager()
    wallets = wallet_manager.list_wallets()
    
    if not wallets:
        print("❌ No wallets found in .env file")
        print("💡 Use 'Add Wallet' option to add a wallet")
        return
    
    for i, wallet in enumerate(wallets, 1):
        print(f"\n{i}. {wallet['name']}")
        print(f"   📍 Public Key: {wallet['public_key']}")
        print(f"   📅 Created: {wallet['created_at']}")
        print(f"   📊 Trades: {wallet['trades_count']}")

def remove_wallet_interactive():
    """Interactive wallet removal"""
    print("\n🗑️ REMOVE WALLET")
    print("=" * 30)
    
    wallet_manager = WalletManager()
    wallets = wallet_manager.list_wallets()
    
    if not wallets:
        print("❌ No wallets found")
        return False
    
    # Show available wallets
    print("\nAvailable wallets:")
    for i, wallet in enumerate(wallets, 1):
        print(f"{i}. {wallet['name']} ({wallet['public_key'][:8]}...)")
    
    # Get wallet to remove
    try:
        choice = input("\nEnter wallet name to remove: ").strip()
        if not choice:
            print("❌ No wallet selected")
            return False
        
        # Confirm removal
        confirm = input(f"⚠️ Remove wallet '{choice}'? (yes/no): ").strip().lower()
        if confirm not in ['yes', 'y']:
            print("❌ Removal cancelled")
            return False
        
        return wallet_manager.remove_wallet(choice)
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    # Test the wallet manager
    add_wallet_interactive()
