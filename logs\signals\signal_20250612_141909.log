2025-06-12 14:19:09,400 - signal_handler - DEBUG - Loaded Telegram API ID: 27993698
2025-06-12 14:19:09,401 - signal_handler - DEBUG - Loaded Telegram API Hash: ********************************
2025-06-12 14:19:09,401 - signal_handler - INFO - Session string file not found: c:\Users\<USER>\Downloads\billy who\session_string.json. Will create a new session.
2025-06-12 14:19:09,401 - signal_handler - INFO - Using unique session name with timestamp: trading_sim_session_1749718149
2025-06-12 14:19:09,402 - signal_handler - INFO - Using session path: c:\Users\<USER>\Downloads\billy who\trading_sim_session_1749718149
2025-06-12 14:19:09,403 - signal_handler - INFO - Signal queue initialized with size: 0
2025-06-12 14:19:09,414 - signal_handler - INFO - Direct signal callback registered
2025-06-12 14:19:16,676 - signal_handler - INFO - API ID loaded: True
2025-06-12 14:19:16,676 - signal_handler - INFO - API Hash loaded: True
2025-06-12 14:19:16,676 - signal_handler - INFO - Phone number loaded: True
2025-06-12 14:19:16,676 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-12 14:19:16,676 - signal_handler - INFO - Creating new Telegram client (attempt 1/3)
2025-06-12 14:19:16,677 - signal_handler - INFO - Using file-based session
2025-06-12 14:19:16,704 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-12 14:19:19,901 - signal_handler - INFO - Checking authorization status...
2025-06-12 14:19:20,214 - signal_handler - INFO - Authorization required. Sending code request...
2025-06-12 14:19:29,416 - signal_handler - INFO - Signing in with code (attempt 1/3)...
2025-06-12 14:19:29,600 - signal_handler - INFO - Got valid session string (length: 353)
2025-06-12 14:19:29,601 - signal_handler - INFO - Saved session string to file: c:\Users\<USER>\Downloads\billy who\session_string.json (length: 353)
2025-06-12 14:19:29,601 - signal_handler - INFO - Session string file saved successfully (size: 375 bytes)
2025-06-12 14:19:29,602 - signal_handler - INFO - Saved session string for future use
2025-06-12 14:19:29,653 - signal_handler - INFO - Successfully retrieved own user ID: 768056701 after authorization
2025-06-12 14:19:29,653 - signal_handler - INFO - Successfully connected and authorized Telegram
2025-06-12 14:19:29,702 - signal_handler - INFO - Successfully retrieved own user ID: 768056701 after authorization
2025-06-12 14:19:31,319 - signal_handler - INFO - API ID loaded: True
2025-06-12 14:19:31,319 - signal_handler - INFO - API Hash loaded: True
2025-06-12 14:19:31,319 - signal_handler - INFO - Phone number loaded: True
2025-06-12 14:19:31,320 - signal_handler - INFO - Session file is valid and not locked
2025-06-12 14:19:31,320 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-12 14:19:31,321 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-12 14:19:31,321 - signal_handler - INFO - Checking authorization status...
2025-06-12 14:19:31,375 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 14:19:31,375 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-12 14:19:31,376 - signal_handler - INFO - Checking authorization status...
2025-06-12 14:19:31,437 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 14:19:31,437 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-12 14:19:31,437 - signal_handler - INFO - Checking authorization status...
2025-06-12 14:19:31,488 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 14:19:31,488 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-12 14:19:31,488 - signal_handler - INFO - Sending test message to verify connection to channel -1002525039395
2025-06-12 14:19:31,489 - signal_handler - INFO - Sending test message directly to channel ID: -1002525039395
2025-06-12 14:19:31,647 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-12 14:19:31,654 - signal_handler - INFO - [msg_1749718171651_2394] Queuing Telegram message:  PORTFOLIO UPDATE [?] PERIODIC UPDATE

 Timestamp: 2025-06-12 14:19:31
[REPEAT] Trade: SIMULATION

[...
2025-06-12 14:19:31,654 - signal_handler - INFO - Started Telegram message queue processor
2025-06-12 14:19:31,654 - signal_handler - INFO - [msg_1749718171651_2394] Message queued with normal priority
2025-06-12 14:19:31,655 - signal_handler - INFO - Message queue processor started with adaptive rate limiting
2025-06-12 14:19:31,655 - signal_handler - INFO - [msg_1749718171651_2394] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:19:31,729 - signal_handler - INFO - [msg_1749718171651_2394] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:19:31,823 - signal_handler - INFO - [msg_1749718171651_2394] Message sent directly successfully using entity
2025-06-12 14:19:31,824 - signal_handler - INFO - [msg_1749718171651_2394] Message sent successfully via direct send method
2025-06-12 14:19:31,824 - signal_handler - INFO - [msg_1749718171651_2394] Message sent successfully on attempt 1
2025-06-12 14:19:32,160 - signal_handler - WARNING - [WARNING] Using channel IDs directly (no entities resolved)
2025-06-12 14:19:32,161 - signal_handler - WARNING - Added event handler using channel IDs (entities not resolved yet)
2025-06-12 14:19:32,161 - signal_handler - INFO - Signal listener started in background task. Waiting for messages...
2025-06-12 14:19:32,167 - signal_handler - INFO - Direct signal callback registered
2025-06-12 14:19:32,170 - signal_handler - INFO - [msg_1749718172169_7718] [STARTUP NOTIFICATION]: [ROCKET] Bot Started
[REFRESH] Mode: SIMULATION
[GRAPH] Strategy: default
[MONEY] Starting Capital: ...
2025-06-12 14:19:32,171 - signal_handler - INFO - [msg_1749718172169_7718] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:19:32,173 - signal_handler - INFO - Signal listener running (attempt 1/3).
2025-06-12 14:19:32,237 - signal_handler - INFO - [msg_1749718172169_7718] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:19:32,352 - signal_handler - INFO - [msg_1749718172169_7718] Message sent directly successfully using entity
2025-06-12 14:19:32,352 - signal_handler - INFO - [msg_1749718172169_7718] Message sent directly successfully
2025-06-12 14:20:55,163 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$11.5K(+234.1%)**
*...
2025-06-12 14:20:55,164 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 14:20:55,165 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 14:20:55,165 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 14:20:55,166 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 14:20:55,170 - signal_handler - INFO - Found contract address: D6Xwb3mGP8NVm4f8DuUcw1pgz4Y4QJ9i2yePWZQiEiTv
2025-06-12 14:20:55,172 - signal_handler - INFO - Found contract address: F15bpTaXvRogsiJU4MiYBfBnAkzBrN9yftwotPMk2Pfa
2025-06-12 14:20:55,173 - signal_handler - INFO - Found 2 unique token addresses
2025-06-12 14:20:55,176 - signal_handler - INFO - Token address: D6Xwb3mGP8NVm4f8DuUcw1pgz4Y4QJ9i2yePWZQiEiTv
2025-06-12 14:20:55,178 - signal_handler - INFO - Token address: F15bpTaXvRogsiJU4MiYBfBnAkzBrN9yftwotPMk2Pfa
2025-06-12 14:20:55,179 - signal_handler - INFO - Detected GMGN format
2025-06-12 14:20:55,180 - signal_handler - INFO - Found token symbol: 11
2025-06-12 14:20:55,180 - signal_handler - INFO - Found FDV: 11.5K - 11.50 (+234.1%)
2025-06-12 14:20:55,181 - signal_handler - INFO - Detected GMGN channel signal for token: D6Xwb3mGP8NVm4f8DuUcw1pgz4Y4QJ9i2yePWZQiEiTv
2025-06-12 14:20:55,181 - signal_handler - INFO - Extracted signal: Token=D6Xwb3mGP8NVm4f8DuUcw1pgz4Y4QJ9i2yePWZQiEiTv, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 14:20:55,181 - signal_handler - WARNING - LOW SIGNAL VOLUME: GMGN - 0/8 expected this hour
2025-06-12 14:20:55,181 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solbix - 0/2 expected this hour
2025-06-12 14:20:55,182 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solana Early Trending - 0/1 expected this hour
2025-06-12 14:20:55,182 - signal_handler - INFO - Signal processing health: 1 received, 1 processed, 0 dropped (100.0% success rate)
2025-06-12 14:20:55,182 - signal_handler - INFO - Added signal to queue: D6Xwb3mGP8NVm4f8DuUcw1pgz4Y4QJ9i2yePWZQiEiTv from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 14:20:55,182 - signal_handler - INFO - Calling direct signal callback for D6Xwb3mGP8NVm4f8DuUcw1pgz4Y4QJ9i2yePWZQiEiTv
