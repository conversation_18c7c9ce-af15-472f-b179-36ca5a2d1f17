2025-06-12 13:37:00,503 - signal_handler - DEBUG - Loaded Telegram API ID: 27993698
2025-06-12 13:37:00,505 - signal_handler - DEBUG - Loaded Telegram API Hash: ********************************
2025-06-12 13:37:00,505 - signal_handler - INFO - Session string file not found: c:\Users\<USER>\Downloads\billy who\session_string.json. Will create a new session.
2025-06-12 13:37:00,505 - signal_handler - INFO - Using unique session name with timestamp: trading_sim_session_1749715620
2025-06-12 13:37:00,506 - signal_handler - INFO - Using session path: c:\Users\<USER>\Downloads\billy who\trading_sim_session_1749715620
2025-06-12 13:37:00,507 - signal_handler - INFO - Signal queue initialized with size: 0
2025-06-12 13:37:00,520 - signal_handler - INFO - Direct signal callback registered
2025-06-12 13:37:25,375 - signal_handler - INFO - [msg_1749715645370_2228] Queuing Telegram message: [WRENCH] CRITICAL STATE FIX COMPLETED [?] 2025-06-12 13:37:25

[CHECK] STATE SYNCHRONIZATION SUCCESS...
2025-06-12 13:37:25,413 - signal_handler - INFO - Started Telegram message queue processor
2025-06-12 13:37:25,425 - signal_handler - INFO - [msg_1749715645370_2228] Message queued with normal priority
2025-06-12 13:37:25,459 - signal_handler - INFO - Message queue processor started with adaptive rate limiting
2025-06-12 13:37:25,459 - signal_handler - WARNING - [msg_1749715645370_2228] Telegram client not connected before sending, attempting to reconnect
2025-06-12 13:37:25,459 - signal_handler - INFO - API ID loaded: True
2025-06-12 13:37:25,460 - signal_handler - INFO - API Hash loaded: True
2025-06-12 13:37:25,460 - signal_handler - INFO - Phone number loaded: True
2025-06-12 13:37:25,460 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-12 13:37:25,460 - signal_handler - INFO - Creating new Telegram client (attempt 1/3)
2025-06-12 13:37:25,461 - signal_handler - INFO - Using file-based session
2025-06-12 13:37:25,502 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-12 13:37:28,795 - signal_handler - INFO - Checking authorization status...
2025-06-12 13:37:29,193 - signal_handler - INFO - Authorization required. Sending code request...
2025-06-12 13:38:07,767 - signal_handler - INFO - [msg_1749715645370_2228] Resolving info channel entity for ID: -1002525039395
2025-06-12 13:38:07,952 - signal_handler - WARNING - [msg_1749715645370_2228] Entity resolution or send failed: The key is not registered in the system (caused by GetChannelsRequest), trying direct ID approach
2025-06-12 13:38:07,952 - signal_handler - INFO - [msg_1749715645370_2228] Sending message directly to channel ID: -1002525039395
2025-06-12 13:38:08,006 - signal_handler - WARNING - [msg_1749715645370_2228] Direct send failed: The key is not registered in the system (caused by GetChannelsRequest)
2025-06-12 13:38:08,007 - signal_handler - INFO - [msg_1749715645370_2228] Trying final approach with string ID
2025-06-12 13:38:08,060 - signal_handler - ERROR - [msg_1749715645370_2228] All direct send approaches failed: The key is not registered in the system (caused by GetContactsRequest)
2025-06-12 13:38:08,060 - signal_handler - WARNING - [msg_1749715645370_2228] Direct send method failed, falling back to legacy approach
2025-06-12 13:38:08,061 - signal_handler - INFO - [msg_1749715645370_2228] Resolving info channel entity for ID: -1002525039395
2025-06-12 13:38:08,114 - signal_handler - WARNING - [msg_1749715645370_2228] Failed to resolve info channel entity: The key is not registered in the system (caused by GetChannelsRequest)
2025-06-12 13:38:08,114 - signal_handler - INFO - [msg_1749715645370_2228] Attempting to send message using direct channel ID: -1002525039395
2025-06-12 13:38:08,168 - signal_handler - ERROR - [msg_1749715645370_2228] Direct ID send failed: The key is not registered in the system (caused by GetChannelsRequest)
2025-06-12 13:38:08,169 - signal_handler - INFO - [msg_1749715645370_2228] Attempting reconnection for retry...
2025-06-12 13:38:08,863 - signal_handler - INFO - [msg_1749715645370_2228] Reconnected to Telegram for retry
2025-06-12 13:38:08,920 - signal_handler - ERROR - [msg_1749715645370_2228] Retry after reconnect failed: The key is not registered in the system (caused by GetChannelsRequest)
2025-06-12 13:38:08,920 - signal_handler - ERROR - [msg_1749715645370_2228] Failed to send message after 1 attempts
2025-06-12 13:38:12,967 - signal_handler - INFO - [msg_1749715692966_3032] Queuing Telegram message: [WARNING] IMPORTANT BOT MESSAGE [?] 2025-06-12 13:38:12

[REFRESH] BOT STATE RESET

All positions, s...
2025-06-12 13:38:12,967 - signal_handler - INFO - [msg_1749715692966_3032] Message queued with normal priority
2025-06-12 13:38:12,994 - signal_handler - INFO - [msg_1749715692994_1711] Queuing Telegram message:  PORTFOLIO UPDATE [?] STATE RESET

 Timestamp: 2025-06-12 13:38:12
[REPEAT] Trade: SIMULATION

[MONE...
2025-06-12 13:38:12,994 - signal_handler - INFO - [msg_1749715692994_1711] Message queued with normal priority
2025-06-12 13:38:13,002 - signal_handler - INFO - Message queue processor active with 2 messages pending
2025-06-12 13:38:13,003 - signal_handler - INFO - [msg_1749715692966_3032] Resolving info channel entity for ID: -1002525039395
2025-06-12 13:38:13,004 - signal_handler - WARNING - [msg_1749715692966_3032] Entity resolution or send failed: file is not a database, trying direct ID approach
2025-06-12 13:38:13,004 - signal_handler - INFO - [msg_1749715692966_3032] Sending message directly to channel ID: -1002525039395
2025-06-12 13:38:13,005 - signal_handler - WARNING - [msg_1749715692966_3032] Direct send failed: file is not a database
2025-06-12 13:38:13,005 - signal_handler - INFO - [msg_1749715692966_3032] Trying final approach with string ID
2025-06-12 13:38:13,007 - signal_handler - ERROR - [msg_1749715692966_3032] All direct send approaches failed: file is not a database
2025-06-12 13:38:13,008 - signal_handler - WARNING - [msg_1749715692966_3032] Direct send method failed, falling back to legacy approach
2025-06-12 13:38:13,009 - signal_handler - INFO - [msg_1749715692966_3032] Resolving info channel entity for ID: -1002525039395
2025-06-12 13:38:13,009 - signal_handler - WARNING - [msg_1749715692966_3032] Failed to resolve info channel entity: file is not a database
2025-06-12 13:38:13,010 - signal_handler - INFO - [msg_1749715692966_3032] Attempting to send message using direct channel ID: -1002525039395
2025-06-12 13:38:13,010 - signal_handler - ERROR - [msg_1749715692966_3032] Direct ID send failed: file is not a database
2025-06-12 13:38:13,011 - signal_handler - INFO - [msg_1749715692966_3032] Attempting reconnection for retry...
2025-06-12 13:38:13,012 - signal_handler - ERROR - [msg_1749715692966_3032] Retry after reconnect failed: file is not a database
2025-06-12 13:38:13,013 - signal_handler - ERROR - [msg_1749715692966_3032] Failed to send message after 1 attempts
2025-06-12 13:38:13,013 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 13:38:13,516 - signal_handler - WARNING - [msg_1749715692994_1711] Telegram client not connected before sending, attempting to reconnect
2025-06-12 13:38:13,565 - signal_handler - ERROR - [msg_1749715692994_1711] Error reconnecting Telegram client: file is not a database
2025-06-12 13:38:13,566 - signal_handler - INFO - [msg_1749715692994_1711] Resolving info channel entity for ID: -1002525039395
2025-06-12 13:38:13,567 - signal_handler - WARNING - [msg_1749715692994_1711] Entity resolution or send failed: file is not a database, trying direct ID approach
2025-06-12 13:38:13,568 - signal_handler - INFO - [msg_1749715692994_1711] Sending message directly to channel ID: -1002525039395
2025-06-12 13:38:13,569 - signal_handler - WARNING - [msg_1749715692994_1711] Direct send failed: file is not a database
2025-06-12 13:38:13,569 - signal_handler - INFO - [msg_1749715692994_1711] Trying final approach with string ID
2025-06-12 13:38:13,570 - signal_handler - ERROR - [msg_1749715692994_1711] All direct send approaches failed: file is not a database
2025-06-12 13:38:13,571 - signal_handler - WARNING - [msg_1749715692994_1711] Direct send method failed, falling back to legacy approach
2025-06-12 13:38:13,572 - signal_handler - INFO - [msg_1749715692994_1711] Resolving info channel entity for ID: -1002525039395
2025-06-12 13:38:13,573 - signal_handler - WARNING - [msg_1749715692994_1711] Failed to resolve info channel entity: file is not a database
2025-06-12 13:38:13,573 - signal_handler - INFO - [msg_1749715692994_1711] Attempting to send message using direct channel ID: -1002525039395
2025-06-12 13:38:13,574 - signal_handler - ERROR - [msg_1749715692994_1711] Direct ID send failed: file is not a database
2025-06-12 13:38:13,575 - signal_handler - INFO - [msg_1749715692994_1711] Attempting reconnection for retry...
2025-06-12 13:38:13,577 - signal_handler - ERROR - [msg_1749715692994_1711] Retry after reconnect failed: file is not a database
2025-06-12 13:38:13,577 - signal_handler - ERROR - [msg_1749715692994_1711] Failed to send message after 1 attempts
