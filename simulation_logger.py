#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Simulation Logger for Solana Trading Bot

This module provides comprehensive logging of simulation mode trades to CSV files,
tracking detailed metrics for each trade including entry/exit points, profit/loss stages,
and time-series PNL analysis.
"""

import os
import csv
import time
import logging
import asyncio
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional, Set, Tuple

logger = logging.getLogger('simulation_logger')

class SimulationLogger:
    """
    Comprehensive logging system for simulation mode trades.

    Tracks:
    - Basic trade data (entry/exit, PNL, duration)
    - Signal source information
    - Staged profit/loss tracking (10%, 20%, 30%, etc.)
    - Time-series PNL analysis
    - Strategy performance metrics
    """

    def __init__(self, state_manager, fast_analysis_function=None):
        """
        Initialize the simulation logger.

        Args:
            state_manager: The state manager instance
            fast_analysis_function: The fast analysis function (simple_pump_analyzer)
        """
        self.state_manager = state_manager
        self.fast_analysis_function = fast_analysis_function

        # Create timestamp for this session
        self.session_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.log_dir = "simulation_logs"
        os.makedirs(self.log_dir, exist_ok=True)

        # Main CSV file for completed trades
        self.trades_file = os.path.join(self.log_dir, f"sim_trades_{self.session_timestamp}.csv")

        # Time-series file for tracking price changes over time
        self.timeseries_file = os.path.join(self.log_dir, f"sim_timeseries_{self.session_timestamp}.csv")

        # Profit stages file for tracking when specific profit thresholds are reached
        self.stages_file = os.path.join(self.log_dir, f"sim_profit_stages_{self.session_timestamp}.csv")

        # Summary file for session statistics
        self.summary_file = os.path.join(self.log_dir, f"sim_summary_{self.session_timestamp}.csv")

        # Initialize CSV files with headers
        self._initialize_csv_files()

        # Track active positions for monitoring
        self.active_positions: Dict[str, Dict[str, Any]] = {}

        # Track profit stages for each token
        self.profit_stages: Dict[str, Dict[int, bool]] = {}

        # Track price history for each token
        self.price_history: Dict[str, List[Dict[str, Any]]] = {}

        # Set of tokens we're monitoring
        self.monitored_tokens: Set[str] = set()

        # Monitoring task
        self.monitor_task = None
        self.is_running = False

        # Profit stage thresholds (percentages)
        self.profit_stages_thresholds = [10, 20, 30, 40, 50, 75, 100, 150, 200]
        self.loss_stages_thresholds = [-10, -20, -30, -50, -75]

        # Time intervals for time-series logging (in seconds)
        self.time_intervals = [60, 300, 600, 1800, 3600, 7200, 14400, 28800, 43200, 86400]  # 1m, 5m, 10m, 30m, 1h, 2h, 4h, 8h, 12h, 24h

        logger.info(f"Simulation logger initialized. Trades file: {self.trades_file}")

    def _initialize_csv_files(self):
        """Initialize CSV files with headers."""
        # Trades file header
        trades_header = [
            "token_address", "token_name", "token_symbol",
            "signal_source", "channel_id", "channel_name", "initial_confidence",
            "entry_time", "entry_price", "entry_amount_sol", "entry_token_amount",
            "exit_time", "exit_price", "exit_amount_sol", "exit_token_amount",
            "duration_seconds", "duration_formatted",
            "pnl_sol", "pnl_percent", "exit_reason",
            "token_age_at_entry", "liquidity_at_entry", "volume_at_entry",
            "highest_price", "lowest_price", "max_profit_percent", "max_loss_percent",
            "time_to_max_profit", "time_to_max_loss"
        ]

        # Time-series file header
        timeseries_header = [
            "token_address", "token_name", "token_symbol",
            "signal_source", "timestamp", "time_since_entry",
            "price", "price_change_percent", "theoretical_pnl_sol", "theoretical_pnl_percent",
            "volume", "liquidity", "buy_sell_ratio"
        ]

        # Profit stages file header
        stages_header = [
            "token_address", "token_name", "token_symbol",
            "profit_stage_percent", "timestamp", "time_since_entry",
            "price", "volume", "liquidity", "buy_sell_ratio"
        ]

        # Summary file header
        summary_header = [
            "metric", "value"
        ]

        # Write headers to files
        with open(self.trades_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(trades_header)

        with open(self.timeseries_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(timeseries_header)

        with open(self.stages_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(stages_header)

        with open(self.summary_file, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(summary_header)

    async def start(self):
        """Start the simulation logger monitoring task."""
        if self.is_running:
            logger.warning("Simulation logger already running")
            return

        self.is_running = True
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info("Simulation logger monitoring task started")

    async def stop(self):
        """Stop the simulation logger and write summary statistics."""
        if not self.is_running:
            logger.warning("Simulation logger not running")
            return

        self.is_running = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass

        # Write summary statistics
        await self._write_summary()
        logger.info("Simulation logger stopped and summary written")

    async def _monitor_loop(self):
        """Background task to monitor positions and log data."""
        logger.info("Starting simulation logger monitoring loop")

        try:
            while self.is_running:
                # Get current open positions from state manager
                open_positions = self.state_manager.get_open_positions()

                # Check for new positions to monitor
                for token_address, position_data in open_positions.items():
                    if token_address not in self.active_positions:
                        # New position detected
                        await self._register_new_position(token_address, position_data)

                # Update data for all monitored tokens
                for token_address in list(self.monitored_tokens):
                    await self._update_token_data(token_address)

                # Check for closed positions
                for token_address in list(self.active_positions.keys()):
                    if token_address not in open_positions:
                        # Position closed, log final data
                        await self._log_closed_position(token_address)
                        del self.active_positions[token_address]

                # Sleep to avoid excessive CPU usage
                await asyncio.sleep(5)
        except asyncio.CancelledError:
            logger.info("Simulation logger monitoring loop cancelled")
            raise
        except Exception as e:
            logger.error(f"Error in simulation logger monitoring loop: {e}", exc_info=True)

    async def _register_new_position(self, token_address: str, position_data: Dict[str, Any]):
        """Register a new position for monitoring."""
        logger.info(f"Registering new position for monitoring: {token_address}")

        # Store position data
        self.active_positions[token_address] = position_data
        self.monitored_tokens.add(token_address)

        # Initialize profit stages tracking
        self.profit_stages[token_address] = {threshold: False for threshold in self.profit_stages_thresholds + self.loss_stages_thresholds}

        # Initialize price history
        self.price_history[token_address] = []

        # Get additional data about the token using fast analysis
        try:
            analysis = None
            if self.fast_analysis_function and callable(self.fast_analysis_function):
                analysis = await self.fast_analysis_function(token_address)

            # SURGICAL FIX: Handle cached results properly
            if not analysis or analysis.get('cached', False) or analysis.get('skip', False):
                logger.debug(f"Skipping simulation logging for {token_address}: cached or skipped analysis")
                return

            if not analysis.get('exists', False):
                logger.warning(f"Could not get analysis data for {token_address}")
                return

            # Extract signal source information
            signal_source = position_data.get('metadata', {}).get('source', 'unknown')
            channel_id = position_data.get('metadata', {}).get('channel_id', 'unknown')
            channel_name = position_data.get('metadata', {}).get('channel_name', 'unknown')

            # Log initial entry in time-series file
            entry_time = position_data.get('open_time', time.time())
            entry_price = position_data.get('entry_price', 0)

            # Get token details
            token_name = analysis.get('name', 'Unknown')
            token_symbol = analysis.get('symbol', 'Unknown')

            # Log initial time-series entry
            self._append_to_timeseries(
                token_address=token_address,
                token_name=token_name,
                token_symbol=token_symbol,
                signal_source=signal_source,
                timestamp=entry_time,
                time_since_entry=0,
                price=entry_price,
                price_change_percent=0,
                theoretical_pnl_sol=0,
                theoretical_pnl_percent=0,
                volume=analysis.get('volume_24h', 0),
                liquidity=analysis.get('liquidity_usd', 0),
                buy_sell_ratio=self._calculate_buy_sell_ratio(analysis)
            )

            logger.info(f"Registered position for {token_address} ({token_name}) from {signal_source}")
        except Exception as e:
            logger.error(f"Error registering position for {token_address}: {e}", exc_info=True)

    async def _update_token_data(self, token_address: str):
        """Update data for a monitored token."""
        if token_address not in self.active_positions:
            return

        try:
            # Get current position data
            position_data = self.active_positions[token_address]
            entry_time = position_data.get('open_time', time.time())
            entry_price = position_data.get('entry_price', 0)

            # Get latest token data using fast analysis
            analysis = None
            if self.fast_analysis_function and callable(self.fast_analysis_function):
                analysis = await self.fast_analysis_function(token_address)

            # SURGICAL FIX: Handle cached results properly
            if not analysis or analysis.get('cached', False) or analysis.get('skip', False):
                logger.debug(f"Skipping simulation logging for {token_address}: cached or skipped analysis")
                return

            if not analysis.get('exists', False):
                logger.warning(f"Could not get analysis data for {token_address}")
                return

            current_price = analysis.get('price', 0)
            if current_price <= 0:
                logger.warning(f"Invalid price for {token_address}: {current_price}")
                return

            # Calculate metrics
            current_time = time.time()
            time_since_entry = current_time - entry_time
            price_change_percent = ((current_price - entry_price) / entry_price * 100) if entry_price > 0 else 0

            # Calculate theoretical PNL
            sol_amount = position_data.get('sol_amount', 0)
            token_amount = position_data.get('token_amount', 0)
            theoretical_sol_value = token_amount * current_price
            theoretical_pnl_sol = theoretical_sol_value - sol_amount
            theoretical_pnl_percent = (theoretical_pnl_sol / sol_amount * 100) if sol_amount > 0 else 0

            # Update price history
            self.price_history[token_address].append({
                'timestamp': current_time,
                'price': current_price,
                'pnl_percent': theoretical_pnl_percent,
                'volume': analysis.get('volume_24h', 0),
                'liquidity': analysis.get('liquidity_usd', 0),
                'buy_sell_ratio': self._calculate_buy_sell_ratio(analysis)
            })

            # Check if we should log a time-series entry
            should_log_timeseries = False
            if len(self.price_history[token_address]) <= 1:
                # Always log the first update
                should_log_timeseries = True
            else:
                # Check if we've reached any of our time intervals
                for interval in self.time_intervals:
                    if int(time_since_entry) % interval < 5:  # Within 5 seconds of the interval
                        should_log_timeseries = True
                        break

            if should_log_timeseries:
                # Get token details
                token_name = analysis.get('name', 'Unknown')
                token_symbol = analysis.get('symbol', 'Unknown')
                signal_source = position_data.get('metadata', {}).get('source', 'unknown')

                # Log time-series entry
                self._append_to_timeseries(
                    token_address=token_address,
                    token_name=token_name,
                    token_symbol=token_symbol,
                    signal_source=signal_source,
                    timestamp=current_time,
                    time_since_entry=time_since_entry,
                    price=current_price,
                    price_change_percent=price_change_percent,
                    theoretical_pnl_sol=theoretical_pnl_sol,
                    theoretical_pnl_percent=theoretical_pnl_percent,
                    volume=analysis.get('volume_24h', 0),
                    liquidity=analysis.get('liquidity_usd', 0),
                    buy_sell_ratio=self._calculate_buy_sell_ratio(analysis)
                )

            # Check for profit/loss stage thresholds
            for threshold in self.profit_stages_thresholds + self.loss_stages_thresholds:
                # Skip if we've already logged this threshold
                if self.profit_stages[token_address][threshold]:
                    continue

                # Check if we've crossed the threshold
                if (threshold > 0 and theoretical_pnl_percent >= threshold) or \
                   (threshold < 0 and theoretical_pnl_percent <= threshold):
                    self.profit_stages[token_address][threshold] = True

                    # Log profit/loss stage
                    self._append_to_stages(
                        token_address=token_address,
                        token_name=analysis.get('name', 'Unknown'),
                        token_symbol=analysis.get('symbol', 'Unknown'),
                        profit_stage_percent=threshold,
                        timestamp=current_time,
                        time_since_entry=time_since_entry,
                        price=current_price,
                        volume=analysis.get('volume_24h', 0),
                        liquidity=analysis.get('liquidity_usd', 0),
                        buy_sell_ratio=self._calculate_buy_sell_ratio(analysis)
                    )

                    logger.info(f"Token {token_address} reached {threshold}% profit/loss stage after {time_since_entry:.1f} seconds")

        except Exception as e:
            logger.error(f"Error updating data for {token_address}: {e}", exc_info=True)

    async def _log_closed_position(self, token_address: str):
        """Log data for a closed position."""
        logger.info(f"Logging closed position: {token_address}")

        try:
            # Get position data from state manager's trade history
            trade_history = self.state_manager.trade_history
            position_data = None

            # Find the trade in history that matches this token
            for trade in reversed(trade_history):
                if trade.get('token') == token_address:
                    position_data = trade
                    break

            if not position_data:
                logger.warning(f"Could not find closed position data for {token_address}")
                return

            # Get the original position data
            original_position = self.active_positions.get(token_address, {})

            # Get price history
            price_history = self.price_history.get(token_address, [])

            # Calculate metrics
            entry_time = original_position.get('open_time', 0)
            exit_time = position_data.get('timestamp', datetime.now(timezone.utc).isoformat())
            if isinstance(exit_time, str):
                exit_time = datetime.fromisoformat(exit_time).timestamp()

            duration_seconds = exit_time - entry_time
            duration_formatted = self._format_duration(duration_seconds)

            # Calculate max profit/loss
            max_profit_percent = 0
            max_loss_percent = 0
            time_to_max_profit = 0
            time_to_max_loss = 0
            highest_price = original_position.get('entry_price', 0)
            lowest_price = original_position.get('entry_price', 0)

            for idx, price_point in enumerate(price_history):
                price = price_point.get('price', 0)
                pnl_percent = price_point.get('pnl_percent', 0)
                timestamp = price_point.get('timestamp', 0)

                if price > highest_price:
                    highest_price = price

                if price < lowest_price or lowest_price == 0:
                    lowest_price = price

                if pnl_percent > max_profit_percent:
                    max_profit_percent = pnl_percent
                    time_to_max_profit = timestamp - entry_time

                if pnl_percent < max_loss_percent:
                    max_loss_percent = pnl_percent
                    time_to_max_loss = timestamp - entry_time

            # Get token details from the last analysis using fast analysis
            analysis = None
            if self.fast_analysis_function and callable(self.fast_analysis_function):
                analysis = await self.fast_analysis_function(token_address)

            token_name = analysis.get('name', 'Unknown') if analysis and analysis.get('exists', False) else 'Unknown'
            token_symbol = analysis.get('symbol', 'Unknown') if analysis and analysis.get('exists', False) else 'Unknown'

            # Get signal source information
            signal_source = original_position.get('metadata', {}).get('source', 'unknown')
            channel_id = original_position.get('metadata', {}).get('channel_id', 'unknown')
            channel_name = original_position.get('metadata', {}).get('channel_name', 'unknown')

            # Log the completed trade
            self._append_to_trades(
                token_address=token_address,
                token_name=token_name,
                token_symbol=token_symbol,
                signal_source=signal_source,
                channel_id=channel_id,
                channel_name=channel_name,
                initial_confidence=original_position.get('metadata', {}).get('initial_confidence', 0),
                entry_time=entry_time,
                entry_price=original_position.get('entry_price', 0),
                entry_amount_sol=original_position.get('sol_amount', 0),
                entry_token_amount=original_position.get('token_amount', 0),
                exit_time=exit_time,
                exit_price=position_data.get('exit_price', 0),
                exit_amount_sol=position_data.get('sol_received', 0),
                exit_token_amount=position_data.get('token_amount_sold', 0),
                duration_seconds=duration_seconds,
                duration_formatted=duration_formatted,
                pnl_sol=position_data.get('pnl_sol', 0),
                pnl_percent=position_data.get('pnl_percent', 0),
                exit_reason=position_data.get('reason', 'unknown'),
                token_age_at_entry=original_position.get('metadata', {}).get('token_age', 0),
                liquidity_at_entry=original_position.get('metadata', {}).get('liquidity', 0),
                volume_at_entry=original_position.get('metadata', {}).get('volume', 0),
                highest_price=highest_price,
                lowest_price=lowest_price,
                max_profit_percent=max_profit_percent,
                max_loss_percent=max_loss_percent,
                time_to_max_profit=time_to_max_profit,
                time_to_max_loss=time_to_max_loss
            )

            logger.info(f"Logged closed position for {token_address} with PNL: {position_data.get('pnl_percent', 0):.2f}%")

            # Clean up
            if token_address in self.monitored_tokens:
                self.monitored_tokens.remove(token_address)

            if token_address in self.profit_stages:
                del self.profit_stages[token_address]

        except Exception as e:
            logger.error(f"Error logging closed position for {token_address}: {e}", exc_info=True)

    def _calculate_buy_sell_ratio(self, analysis: Dict[str, Any]) -> float:
        """Calculate buy/sell ratio from analysis data."""
        try:
            txns = analysis.get('raw_data', {}).get('dexscreener', {}).get('txns', {})
            if not txns:
                return 1.0

            h1_txns = txns.get('h1', {})
            buys = h1_txns.get('buys', 0)
            sells = h1_txns.get('sells', 0)

            if sells == 0:
                return buys if buys > 0 else 1.0

            return buys / sells
        except Exception:
            return 1.0

    def _format_duration(self, seconds: float) -> str:
        """Format duration in seconds to a human-readable string."""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            minutes = seconds / 60
            return f"{minutes:.1f}m"
        elif seconds < 86400:
            hours = seconds / 3600
            return f"{hours:.1f}h"
        else:
            days = seconds / 86400
            return f"{days:.1f}d"

    def _append_to_trades(self, **kwargs):
        """Append a row to the trades CSV file."""
        with open(self.trades_file, 'a', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow([
                kwargs.get('token_address', ''),
                kwargs.get('token_name', ''),
                kwargs.get('token_symbol', ''),
                kwargs.get('signal_source', ''),
                kwargs.get('channel_id', ''),
                kwargs.get('channel_name', ''),
                kwargs.get('initial_confidence', 0),
                kwargs.get('entry_time', 0),
                kwargs.get('entry_price', 0),
                kwargs.get('entry_amount_sol', 0),
                kwargs.get('entry_token_amount', 0),
                kwargs.get('exit_time', 0),
                kwargs.get('exit_price', 0),
                kwargs.get('exit_amount_sol', 0),
                kwargs.get('exit_token_amount', 0),
                kwargs.get('duration_seconds', 0),
                kwargs.get('duration_formatted', ''),
                kwargs.get('pnl_sol', 0),
                kwargs.get('pnl_percent', 0),
                kwargs.get('exit_reason', ''),
                kwargs.get('token_age_at_entry', 0),
                kwargs.get('liquidity_at_entry', 0),
                kwargs.get('volume_at_entry', 0),
                kwargs.get('highest_price', 0),
                kwargs.get('lowest_price', 0),
                kwargs.get('max_profit_percent', 0),
                kwargs.get('max_loss_percent', 0),
                kwargs.get('time_to_max_profit', 0),
                kwargs.get('time_to_max_loss', 0)
            ])

    def _append_to_timeseries(self, **kwargs):
        """Append a row to the time-series CSV file."""
        with open(self.timeseries_file, 'a', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow([
                kwargs.get('token_address', ''),
                kwargs.get('token_name', ''),
                kwargs.get('token_symbol', ''),
                kwargs.get('signal_source', ''),
                kwargs.get('timestamp', 0),
                kwargs.get('time_since_entry', 0),
                kwargs.get('price', 0),
                kwargs.get('price_change_percent', 0),
                kwargs.get('theoretical_pnl_sol', 0),
                kwargs.get('theoretical_pnl_percent', 0),
                kwargs.get('volume', 0),
                kwargs.get('liquidity', 0),
                kwargs.get('buy_sell_ratio', 0)
            ])

    def _append_to_stages(self, **kwargs):
        """Append a row to the profit stages CSV file."""
        with open(self.stages_file, 'a', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow([
                kwargs.get('token_address', ''),
                kwargs.get('token_name', ''),
                kwargs.get('token_symbol', ''),
                kwargs.get('profit_stage_percent', 0),
                kwargs.get('timestamp', 0),
                kwargs.get('time_since_entry', 0),
                kwargs.get('price', 0),
                kwargs.get('volume', 0),
                kwargs.get('liquidity', 0),
                kwargs.get('buy_sell_ratio', 0)
            ])

    async def _write_summary(self):
        """Write summary statistics to the summary file."""
        try:
            # Get stats from state manager
            stats = self.state_manager.get_stats()

            # Calculate additional metrics
            total_trades = stats.get('total_trades', 0)
            win_count = stats.get('win_count', 0)
            loss_count = stats.get('loss_count', 0)

            winrate = (win_count / total_trades * 100) if total_trades > 0 else 0

            # Calculate average trade duration
            trade_durations = []
            for trade in self.state_manager.trade_history:
                if 'exit_time' in trade and 'open_time' in trade:
                    duration = trade['exit_time'] - trade['open_time']
                    trade_durations.append(duration)

            avg_duration = sum(trade_durations) / len(trade_durations) if trade_durations else 0

            # Calculate average profit per winning trade
            winning_trades_pnl = [trade.get('pnl_sol', 0) for trade in self.state_manager.trade_history if trade.get('pnl_sol', 0) > 0]
            avg_win_pnl = sum(winning_trades_pnl) / len(winning_trades_pnl) if winning_trades_pnl else 0

            # Calculate average loss per losing trade
            losing_trades_pnl = [trade.get('pnl_sol', 0) for trade in self.state_manager.trade_history if trade.get('pnl_sol', 0) < 0]
            avg_loss_pnl = sum(losing_trades_pnl) / len(losing_trades_pnl) if losing_trades_pnl else 0

            # Write summary to file
            with open(self.summary_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(["metric", "value"])
                writer.writerow(["session_start", self.session_timestamp])
                writer.writerow(["session_end", datetime.now().strftime("%Y%m%d_%H%M%S")])
                writer.writerow(["starting_sol", stats.get('starting_sol', 0)])
                writer.writerow(["ending_sol", stats.get('current_available_sol', 0)])
                writer.writerow(["total_profit_sol", stats.get('total_profit_sol', 0)])
                writer.writerow(["roi_percent", stats.get('roi_percent', 0)])
                writer.writerow(["total_trades", total_trades])
                writer.writerow(["winning_trades", win_count])
                writer.writerow(["losing_trades", loss_count])
                writer.writerow(["winrate_percent", winrate])
                writer.writerow(["average_trade_duration_seconds", avg_duration])
                writer.writerow(["average_win_pnl_sol", avg_win_pnl])
                writer.writerow(["average_loss_pnl_sol", avg_loss_pnl])

                # Add signal source statistics
                signal_sources = {}
                for trade in self.state_manager.trade_history:
                    source = trade.get('metadata', {}).get('source', 'unknown')
                    if source not in signal_sources:
                        signal_sources[source] = {'count': 0, 'wins': 0, 'losses': 0, 'pnl': 0}

                    signal_sources[source]['count'] += 1
                    if trade.get('pnl_sol', 0) > 0:
                        signal_sources[source]['wins'] += 1
                    else:
                        signal_sources[source]['losses'] += 1

                    signal_sources[source]['pnl'] += trade.get('pnl_sol', 0)

                for source, data in signal_sources.items():
                    writer.writerow([f"source_{source}_count", data['count']])
                    writer.writerow([f"source_{source}_wins", data['wins']])
                    writer.writerow([f"source_{source}_losses", data['losses']])
                    writer.writerow([f"source_{source}_pnl", data['pnl']])
                    writer.writerow([f"source_{source}_winrate", (data['wins'] / data['count'] * 100) if data['count'] > 0 else 0])

            logger.info(f"Summary statistics written to {self.summary_file}")
        except Exception as e:
            logger.error(f"Error writing summary statistics: {e}", exc_info=True)
