#!/usr/bin/env python3
"""
SURGICAL FIX: Emergency sell script for rugged tokens
Quickly sell positions with liquidity < $1000
"""

import asyncio
import sys
import os

# Add the current directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from bot_controller import BotControll<PERSON>

async def emergency_sell_rugged_positions():
    """Emergency sell all rugged positions."""
    print("🚨 EMERGENCY SELL SCRIPT - RUGGED TOKEN DETECTION")
    print("=" * 50)
    
    try:
        # Initialize bot controller
        bot = BotController()
        
        # Set to real mode for actual selling
        bot.run_mode = "REAL"
        
        # Start trading to initialize components
        print("🔧 Initializing bot components...")
        await bot.start_trading()
        
        # Run emergency sell for rugged positions
        print("\n🔍 Scanning for rugged positions...")
        await bot.emergency_sell_rugged()
        
        print("\n✅ Emergency sell scan completed!")
        print("Check the bot logs for execution details.")
        
    except Exception as e:
        print(f"❌ Error during emergency sell: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        print("\n🔧 Cleaning up...")
        try:
            if 'bot' in locals():
                await bot.stop_trading()
        except:
            pass

if __name__ == "__main__":
    print("Starting emergency sell for rugged positions...")
    asyncio.run(emergency_sell_rugged_positions())
