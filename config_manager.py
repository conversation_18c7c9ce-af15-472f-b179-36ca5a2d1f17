import json
import os
import logging
from typing import Dict, Any
from dotenv import load_dotenv

# OPTIMIZED: Removed unused Optional import

# OPTIMIZED: Removed unused shared_utils import and flag

# Get logger
logger = logging.getLogger(__name__)

class ConfigManager:
    def __init__(self):
        """Initialize the configuration manager."""
        # Load environment variables from .env
        load_dotenv()

        # OPTIMIZED: Simplified initialization - removed unused unified_config
        self.config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "finalconfig.json")
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from finalconfig.json"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    logger.info(f"Loading config from: {self.config_path}")
                    print(f"Loading config from: {self.config_path}")
                    return json.load(f)
            else:
                # Fallback to looking in the parent directory
                fallback_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "finalconfig.json")
                if os.path.exists(fallback_path):
                    with open(fallback_path, 'r', encoding='utf-8') as f:
                        logger.info(f"Loading config from fallback path: {fallback_path}")
                        print(f"Loading config from fallback path: {fallback_path}")
                        return json.load(f)
                else:
                    logger.error(f"Error: Config file not found at {self.config_path} or {fallback_path}")
                    print(f"Error: Config file not found at {self.config_path} or {fallback_path}")
                    return {}
        except Exception as e:
            logger.error(f"Error loading config: {e}")
            print(f"Error loading config: {e}")
            return {}

    def get(self, section: str, key: str, default: Any = None) -> Any:
        """Generic method to get a specific key from a section."""
        # OPTIMIZED: Simplified direct config access
        return self.config.get(section, {}).get(key, default)

    def get_section(self, section: str) -> Dict[str, Any]:
        """Get an entire configuration section."""
        # OPTIMIZED: Simplified direct config access
        return self.config.get(section, {})

    def get_api_keys(self) -> Dict[str, str]:
        """Get API keys from .env and config"""
        # Combine API endpoints from config with API keys from .env
        api_keys = {
            "solana_rpc": self.get_solana_rpc_url()
        }

        # Add API endpoints from config
        endpoints = self.config.get('api_endpoints', {})
        for key, value in endpoints.items():
            if key not in api_keys:
                api_keys[key] = value

        return api_keys

    def get_telegram_settings(self) -> Dict[str, Any]:
        """Get Telegram settings from config"""
        return self.get_section('telegram_settings')

    def get_trading_settings(self) -> Dict[str, Any]:
        """Get trading settings from config"""
        return self.get_section('trading_settings')

    def get_simulation_settings(self) -> Dict[str, Any]:
        """Get simulation settings from config"""
        return self.get_section('simulation_settings')

    def get_bot_session_name(self) -> str:
        """Get bot session name from config"""
        return self.get_telegram_settings().get('bot_session_name', 'trading_sim_session')

    # --- Added for v3 compatibility ---
    def get_slippage_bps(self) -> int:
        """Get slippage tolerance in basis points (e.g., 50 for 0.5%)"""
        # SURGICAL FIX: Get from transaction_settings at root level
        transaction_settings = self.get_section('transaction_settings')
        if transaction_settings:
            # Convert percentage to basis points (1% = 100 bps)
            slippage_percent = transaction_settings.get('slippage_percent', 1.0)
            return int(slippage_percent * 100)  # Convert percentage to basis points

        # Fallback to legacy slippage_bps
        return self.get('trading_settings', 'slippage_bps', default=100)  # Default 1%

    def get_external_fees(self) -> dict:
        """SURGICAL FIX: Get external fees from transaction_settings at root level"""
        transaction_settings = self.get_section('transaction_settings')
        return {
            'buy_tip_sol': transaction_settings.get('buy_tip_sol', 0.005),
            'gas_price_sol': transaction_settings.get('gas_price_sol', 0.005),
            'handling_fee_percent': transaction_settings.get('handling_fee_percent', 1.0),
            'platform_fee_percent': transaction_settings.get('platform_fee_percent', 1.0)
        }

    def get_tp_sl_settings(self) -> Dict[str, Any]:
        """Get Take Profit (TP) and Stop Loss (SL) settings."""
        defaults = {
            'pnl_sell_percent_min': 15.0,      # First TP target
            'pnl_sell_percent_max': 25.0,      # Second TP target (unused if partial frac = 1)
            'partial_sell_fraction': 0.8,     # Fraction to sell at first TP
            'stop_loss_percent': -30.0,       # Hard stop loss
        }
        settings = self.get_section('tp_sl_settings')
        # Merge defaults with loaded settings
        for key, value in defaults.items():
            if key not in settings:
                settings[key] = value
        return settings

    def get_risk_detection_thresholds(self) -> Dict[str, Any]:
        """Get risk detection thresholds for position monitoring."""
        defaults = {
            # Critical risk thresholds
            'critical_pnl_percent': -12.0,
            'critical_profit_percent': 40.0,
            'critical_volatility': 0.08,
            'critical_drawdown_percent': -8.0,
            'critical_age_minutes': 1.5,
            # High risk thresholds
            'high_pnl_loss_percent': -8.0,
            'high_pnl_profit_percent': 25.0,
            'high_volatility': 0.04,
            'high_drawdown_percent': -4.0,
            'high_age_minutes': 3.0,
            'high_profit_drop_threshold': 1.15,
            'high_profit_drop_ratio': 0.92,
            # Medium risk thresholds
            'medium_pnl_loss_percent': -4.0,
            'medium_pnl_profit_percent': 12.0,
            'medium_volatility': 0.015,
            'medium_age_minutes': 10.0,
            'medium_profit_drop_threshold': 1.08,
            'medium_profit_drop_ratio': 0.96
        }

        # Get settings from trading_settings.risk_detection_thresholds
        trading_settings = self.get_section('trading_settings')
        risk_settings = trading_settings.get('risk_detection_thresholds', {})

        # Merge defaults with loaded settings
        for key, value in defaults.items():
            if key not in risk_settings:
                risk_settings[key] = value

        return risk_settings
    # --- End added ---

    def get_strategy_params(self, strategy_name: str) -> Dict[str, Any]:
        """Get parameters for a specific trading strategy."""
        strategies = self.get('trading_settings', 'strategies', default={})
        return strategies.get(strategy_name, {})

    # --- Added for GMGN trader client ---
    def get_telegram_api_id(self) -> str:
        """Get Telegram API ID from .env"""
        # FIXED: Direct env access only
        return os.getenv("TELEGRAM_API_ID", "")

    def get_telegram_api_hash(self) -> str:
        """Get Telegram API hash from .env"""
        # FIXED: Direct env access only
        return os.getenv("TELEGRAM_API_HASH", "")

    def get_telegram_phone(self) -> str:
        """Get Telegram phone number from .env"""
        # FIXED: Direct env access only
        return os.getenv("TELEGRAM_PHONE", "")

    def get_telegram_bot_username(self) -> str:
        """Get Telegram bot username from .env"""
        # No equivalent in UnifiedConfigManager, always use direct env access
        return os.getenv("GMGN_BOT_USERNAME", "@GMGN_sol_bot")

    def get_telegram_session_name(self) -> str:
        """Get Telegram session name from config"""
        return self.get_telegram_settings().get('bot_session_name', 'gmgn_session')

    # Moralis and Birdeye API integration removed

    def get_solana_rpc_url(self) -> str:
        """Get Solana RPC URL from config or .env"""
        # FIXED: Direct config access only
        config_url = self.config.get('api_endpoints', {}).get('solana_rpc', "")
        if config_url:
            return config_url
        return os.getenv("SOLANA_RPC_URL", "https://api.mainnet-beta.solana.com")

    def validate_config(self) -> Dict[str, Any]:
        """
        SURGICAL FIX: Comprehensive configuration validation for production readiness

        Returns:
            Dict with validation results and any critical issues found
        """
        validation_result = {
            'is_valid': True,
            'errors': [],
            'warnings': [],
            'critical_issues': []
        }

        try:
            # Validate trading settings
            trading_settings = self.get_trading_settings()
            if not trading_settings:
                validation_result['critical_issues'].append("Missing trading_settings section")
                validation_result['is_valid'] = False
            else:
                # Check critical trading parameters
                total_sol_capital = trading_settings.get('total_sol_capital', 0)
                if total_sol_capital <= 0:
                    validation_result['critical_issues'].append("total_sol_capital must be > 0")
                    validation_result['is_valid'] = False

                # Validate transaction settings
                tx_settings = trading_settings.get('transaction_settings', {})
                if not tx_settings:
                    validation_result['warnings'].append("Missing transaction_settings")
                else:
                    required_tx_fields = ['buy_tip_sol', 'gas_price_sol', 'slippage_percent']
                    for field in required_tx_fields:
                        if field not in tx_settings:
                            validation_result['warnings'].append(f"Missing transaction setting: {field}")

                # Validate rug protection settings
                rug_protection = trading_settings.get('rug_protection', {})
                if not rug_protection:
                    validation_result['warnings'].append("Missing rug_protection settings")

            # Validate API endpoints
            api_endpoints = self.get_section('api_endpoints')
            if not api_endpoints:
                validation_result['critical_issues'].append("Missing api_endpoints section")
                validation_result['is_valid'] = False

            # Validate Telegram settings
            telegram_settings = self.get_telegram_settings()
            if not telegram_settings:
                validation_result['warnings'].append("Missing telegram_settings section")

            # Check environment variables
            required_env_vars = ['TELEGRAM_API_ID', 'TELEGRAM_API_HASH', 'HELIUS_API_KEY']
            for env_var in required_env_vars:
                if not os.getenv(env_var):
                    validation_result['critical_issues'].append(f"Missing environment variable: {env_var}")
                    validation_result['is_valid'] = False

            # Check wallet configuration
            wallet_found = False
            for key in os.environ:
                if key.startswith('WALLET_') and key.endswith('_PRIVATE_KEY'):
                    wallet_value = os.getenv(key)
                    if wallet_value and wallet_value != 'demo_wallet_replace_with_real_private_key_for_trading':
                        wallet_found = True
                        break

            if not wallet_found:
                validation_result['critical_issues'].append("No valid wallet private key configured")
                validation_result['is_valid'] = False

        except Exception as e:
            validation_result['critical_issues'].append(f"Configuration validation error: {e}")
            validation_result['is_valid'] = False

        return validation_result