"""
Helius RPC Rate Limiter
Implements smart rate limiting for Helius RPC API calls with 10 RPS limit
"""

import asyncio
import time
import logging
from typing import Dict, Any, Optional
import requests
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class CircuitBreakerState(Enum):
    CLOSED = "closed"
    OPEN = "open"
    HALF_OPEN = "half_open"

@dataclass
class RateLimitConfig:
    requests_per_second: float = 10.0
    min_request_spacing_ms: int = 100
    retry_delay_ms: int = 300
    max_retries: int = 2
    backoff_multiplier: float = 1.5
    circuit_breaker_threshold: int = 5
    circuit_breaker_timeout_seconds: int = 30

class HeliusRateLimiter:
    """Smart rate limiter for Helius RPC API with circuit breaker pattern"""
    
    def __init__(self, config: Optional[RateLimitConfig] = None):
        self.config = config or RateLimitConfig()
        
        # Rate limiting state
        self.last_request_time = 0.0
        self.request_count = 0
        self.window_start = time.time()
        
        # Circuit breaker state
        self.circuit_state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.last_failure_time = 0.0
        self.success_count = 0
        
        # Request tracking
        self.total_requests = 0
        self.total_failures = 0
        self.total_rate_limited = 0
        
        logger.info(f"Helius Rate Limiter initialized: {self.config.requests_per_second} RPS")
    
    def _should_allow_request(self) -> bool:
        """Check if request should be allowed based on rate limiting"""
        current_time = time.time()
        
        # Reset window if needed (1-second sliding window)
        if current_time - self.window_start >= 1.0:
            self.request_count = 0
            self.window_start = current_time
        
        # Check rate limit
        if self.request_count >= self.config.requests_per_second:
            return False
        
        # Check minimum spacing
        time_since_last = (current_time - self.last_request_time) * 1000  # Convert to ms
        if time_since_last < self.config.min_request_spacing_ms:
            return False
        
        return True
    
    def _check_circuit_breaker(self) -> bool:
        """Check circuit breaker state"""
        current_time = time.time()
        
        if self.circuit_state == CircuitBreakerState.OPEN:
            # Check if timeout has passed
            if current_time - self.last_failure_time >= self.config.circuit_breaker_timeout_seconds:
                self.circuit_state = CircuitBreakerState.HALF_OPEN
                self.success_count = 0
                logger.info("Circuit breaker moving to HALF_OPEN state")
                return True
            return False
        
        return True
    
    def _record_success(self):
        """Record successful request"""
        self.failure_count = 0
        
        if self.circuit_state == CircuitBreakerState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= 3:  # Require 3 successes to close
                self.circuit_state = CircuitBreakerState.CLOSED
                logger.info("Circuit breaker CLOSED - service recovered")
    
    def _record_failure(self, is_rate_limit_error: bool = False):
        """Record failed request"""
        # SURGICAL FIX 3: Don't count rate limit errors as circuit breaker failures
        if not is_rate_limit_error:
            self.failure_count += 1
            self.last_failure_time = time.time()

            if self.failure_count >= self.config.circuit_breaker_threshold:
                if self.circuit_state != CircuitBreakerState.OPEN:
                    self.circuit_state = CircuitBreakerState.OPEN
                    logger.warning(f"Circuit breaker OPEN - too many failures ({self.failure_count})")
        else:
            logger.debug("Rate limit error - not counting towards circuit breaker failures")

        # Always count total failures for statistics
        self.total_failures += 1
    
    def _wait_for_rate_limit(self):
        """Wait until next request is allowed - SURGICAL FIX: Prevent CPU death spiral"""
        wait_count = 0
        while not self._should_allow_request():
            # SURGICAL: Progressive sleep to prevent CPU spinning
            sleep_time = min(0.1, 0.01 * (1 + wait_count // 10))  # 10ms -> 100ms max
            time.sleep(sleep_time)
            wait_count += 1
            # SURGICAL: Emergency break after 5 seconds to prevent infinite loop
            if wait_count > 500:
                logger.warning("Rate limit wait exceeded 5 seconds, breaking")
                break
    
    def make_rpc_request(self, rpc_url: str, payload: Dict[str, Any], timeout: int = 6) -> Optional[Dict[str, Any]]:
        """
        Make rate-limited RPC request with smart retry logic
        
        Args:
            rpc_url: Helius RPC endpoint URL
            payload: JSON-RPC payload
            timeout: Request timeout in seconds
            
        Returns:
            Response data or None if failed
        """
        # Check circuit breaker
        if not self._check_circuit_breaker():
            logger.debug("Circuit breaker OPEN - request blocked")
            return None
        
        # Wait for rate limit if needed
        self._wait_for_rate_limit()
        
        # Update request tracking
        current_time = time.time()
        self.last_request_time = current_time
        self.request_count += 1
        self.total_requests += 1
        
        # Attempt request with retries
        for attempt in range(self.config.max_retries + 1):
            try:
                response = requests.post(rpc_url, json=payload, timeout=timeout)
                
                if response.status_code == 200:
                    self._record_success()
                    return response.json()
                
                elif response.status_code == 429:
                    # Rate limited - wait and retry
                    self.total_rate_limited += 1
                    retry_delay = self.config.retry_delay_ms * (self.config.backoff_multiplier ** attempt)
                    logger.warning(f"RPC {payload.get('method', 'unknown')} HTTP 429 (attempt {attempt + 1})")
                    
                    if attempt < self.config.max_retries:
                        time.sleep(retry_delay / 1000.0)  # Convert to seconds
                        continue
                    else:
                        # SURGICAL FIX 3: Mark as rate limit error, don't count towards circuit breaker
                        self._record_failure(is_rate_limit_error=True)
                        return None
                
                else:
                    # Other HTTP error
                    logger.warning(f"RPC {payload.get('method', 'unknown')} HTTP {response.status_code} (attempt {attempt + 1})")
                    if attempt < self.config.max_retries:
                        time.sleep(self.config.retry_delay_ms / 1000.0)
                        continue
                    else:
                        self._record_failure()
                        return None
                        
            except Exception as e:
                logger.debug(f"RPC {payload.get('method', 'unknown')} error (attempt {attempt + 1}): {e}")
                if attempt < self.config.max_retries:
                    time.sleep(self.config.retry_delay_ms / 1000.0)
                    continue
                else:
                    self._record_failure()
                    return None
        
        return None
    
    def get_stats(self) -> Dict[str, Any]:
        """Get rate limiter statistics"""
        success_rate = ((self.total_requests - self.total_failures) / max(1, self.total_requests)) * 100
        
        return {
            "total_requests": self.total_requests,
            "total_failures": self.total_failures,
            "total_rate_limited": self.total_rate_limited,
            "success_rate_percent": round(success_rate, 2),
            "circuit_state": self.circuit_state.value,
            "failure_count": self.failure_count,
            "current_rps": self.request_count,
            "max_rps": self.config.requests_per_second
        }
    
    def reset_stats(self):
        """Reset statistics"""
        self.total_requests = 0
        self.total_failures = 0
        self.total_rate_limited = 0
        logger.info("Helius rate limiter stats reset")

# Global instance
_helius_rate_limiter = None

def get_helius_rate_limiter(config_manager=None) -> HeliusRateLimiter:
    """Get or create global Helius rate limiter instance"""
    global _helius_rate_limiter
    
    if _helius_rate_limiter is None:
        # Load config if available
        rate_limit_config = RateLimitConfig()
        
        if config_manager:
            try:
                # Get RPS from main api_rate_limits section
                api_limits = config_manager.get_section('api_rate_limits')
                if api_limits and 'helius_rpc_rpm' in api_limits:
                    # Convert RPM to RPS: 600 RPM = 10 RPS
                    helius_rpm = api_limits.get('helius_rpc_rpm', 600)
                    rate_limit_config.requests_per_second = helius_rpm / 60.0

                # Get other settings from helius_rpc_rate_limit section
                helius_config = config_manager.get_section('helius_rpc_rate_limit')
                if helius_config:
                    rate_limit_config.min_request_spacing_ms = helius_config.get('min_request_spacing_ms', 100)
                    rate_limit_config.retry_delay_ms = helius_config.get('retry_delay_ms', 300)
                    rate_limit_config.max_retries = helius_config.get('max_retries', 2)
                    rate_limit_config.backoff_multiplier = helius_config.get('backoff_multiplier', 1.5)
                    rate_limit_config.circuit_breaker_threshold = helius_config.get('circuit_breaker_threshold', 5)
                    rate_limit_config.circuit_breaker_timeout_seconds = helius_config.get('circuit_breaker_timeout_seconds', 30)
            except Exception as e:
                logger.warning(f"Failed to load Helius rate limit config: {e}")
        
        _helius_rate_limiter = HeliusRateLimiter(rate_limit_config)
    
    return _helius_rate_limiter
