2025-06-12 18:19:47,776 - signal_handler - DEBUG - Loaded Telegram API ID: 27993698
2025-06-12 18:19:47,776 - signal_handler - DEBUG - Loaded Telegram API Hash: ********************************
2025-06-12 18:19:47,787 - signal_handler - INFO - Loaded session string from c:\Users\<USER>\Downloads\billy who\session_string.json (length: 353)
2025-06-12 18:19:47,788 - signal_handler - INFO - Using unique session name with timestamp: trading_sim_session_1749732587
2025-06-12 18:19:47,789 - signal_handler - INFO - Using session path: c:\Users\<USER>\Downloads\billy who\trading_sim_session_1749732587
2025-06-12 18:19:47,789 - signal_handler - INFO - Signal queue initialized with size: 0
2025-06-12 18:19:47,801 - signal_handler - INFO - Direct signal callback registered
2025-06-12 18:19:57,521 - signal_handler - INFO - [msg_1749732597518_5312] Queuing Telegram message: [WARNING] IMPORTANT BOT MESSAGE [?] 2025-06-12 18:19:57

[REFRESH] BOT STATE RESET

All positions, s...
2025-06-12 18:19:57,522 - signal_handler - INFO - Started Telegram message queue processor
2025-06-12 18:19:57,522 - signal_handler - INFO - [msg_1749732597518_5312] Message queued with normal priority
2025-06-12 18:19:57,565 - signal_handler - INFO - [msg_1749732597565_8432] Queuing Telegram message:  PORTFOLIO UPDATE [?] STATE RESET

 Timestamp: 2025-06-12 18:19:57
[REPEAT] Trade: SIMULATION

[MONE...
2025-06-12 18:19:57,566 - signal_handler - INFO - [msg_1749732597565_8432] Message queued with normal priority
2025-06-12 18:19:57,573 - signal_handler - INFO - Message queue processor started with adaptive rate limiting
2025-06-12 18:19:57,574 - signal_handler - WARNING - [msg_1749732597518_5312] Telegram client not connected before sending, attempting to reconnect
2025-06-12 18:19:57,574 - signal_handler - INFO - API ID loaded: True
2025-06-12 18:19:57,575 - signal_handler - INFO - API Hash loaded: True
2025-06-12 18:19:57,575 - signal_handler - INFO - Phone number loaded: True
2025-06-12 18:19:57,576 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-12 18:19:57,576 - signal_handler - INFO - Creating new Telegram client (attempt 1/3)
2025-06-12 18:19:57,577 - signal_handler - INFO - Using saved session string
2025-06-12 18:19:57,580 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-12 18:19:57,948 - signal_handler - INFO - Checking authorization status...
2025-06-12 18:19:58,063 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 18:19:58,064 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-12 18:19:58,064 - signal_handler - INFO - Checking authorization status...
2025-06-12 18:19:58,131 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 18:19:58,132 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-12 18:19:58,132 - signal_handler - INFO - Checking authorization status...
2025-06-12 18:19:58,191 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 18:19:58,191 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-12 18:19:58,192 - signal_handler - INFO - Sending test message to verify connection to channel -1002525039395
2025-06-12 18:19:58,192 - signal_handler - INFO - Sending test message directly to channel ID: -1002525039395
2025-06-12 18:19:58,376 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-12 18:19:58,377 - signal_handler - INFO - [msg_1749732597518_5312] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:19:58,431 - signal_handler - INFO - [msg_1749732597518_5312] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:19:58,532 - signal_handler - INFO - [msg_1749732597518_5312] Message sent directly successfully using entity
2025-06-12 18:19:58,537 - signal_handler - INFO - [msg_1749732597518_5312] Message sent successfully via direct send method
2025-06-12 18:19:58,538 - signal_handler - INFO - [msg_1749732597518_5312] Message sent successfully on attempt 1
2025-06-12 18:19:58,539 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 18:19:59,048 - signal_handler - INFO - [msg_1749732597565_8432] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:19:59,131 - signal_handler - INFO - [msg_1749732597565_8432] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:19:59,241 - signal_handler - INFO - [msg_1749732597565_8432] Message sent directly successfully using entity
2025-06-12 18:19:59,241 - signal_handler - INFO - [msg_1749732597565_8432] Message sent successfully via direct send method
2025-06-12 18:19:59,242 - signal_handler - INFO - [msg_1749732597565_8432] Message sent successfully on attempt 1
2025-06-12 18:20:29,701 - signal_handler - INFO - API ID loaded: True
2025-06-12 18:20:29,701 - signal_handler - INFO - API Hash loaded: True
2025-06-12 18:20:29,702 - signal_handler - INFO - Phone number loaded: True
2025-06-12 18:20:29,702 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-12 18:20:29,702 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-12 18:20:29,702 - signal_handler - INFO - Checking authorization status...
2025-06-12 18:20:29,757 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 18:20:29,758 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-12 18:20:29,758 - signal_handler - INFO - Checking authorization status...
2025-06-12 18:20:29,810 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 18:20:29,811 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-12 18:20:29,811 - signal_handler - INFO - Checking authorization status...
2025-06-12 18:20:29,869 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 18:20:29,869 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-12 18:20:29,869 - signal_handler - INFO - Sending test message to verify connection to channel -1002525039395
2025-06-12 18:20:29,870 - signal_handler - INFO - Sending test message directly to channel ID: -1002525039395
2025-06-12 18:20:29,968 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-12 18:20:29,970 - signal_handler - INFO - [msg_1749732629970_5980] Queuing Telegram message:  PORTFOLIO UPDATE [?] PERIODIC UPDATE

 Timestamp: 2025-06-12 18:20:29
[REPEAT] Trade: SIMULATION

[...
2025-06-12 18:20:29,971 - signal_handler - INFO - [msg_1749732629970_5980] Message queued with normal priority
2025-06-12 18:20:30,032 - signal_handler - INFO - [msg_1749732629970_5980] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:20:30,089 - signal_handler - INFO - [msg_1749732629970_5980] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:20:30,176 - signal_handler - INFO - [msg_1749732629970_5980] Message sent directly successfully using entity
2025-06-12 18:20:30,177 - signal_handler - INFO - [msg_1749732629970_5980] Message sent successfully via direct send method
2025-06-12 18:20:30,177 - signal_handler - INFO - [msg_1749732629970_5980] Message sent successfully on attempt 1
2025-06-12 18:20:30,482 - signal_handler - WARNING - [WARNING] Using channel IDs directly (no entities resolved)
2025-06-12 18:20:30,483 - signal_handler - WARNING - Added event handler using channel IDs (entities not resolved yet)
2025-06-12 18:20:30,483 - signal_handler - INFO - Signal listener started in background task. Waiting for messages...
2025-06-12 18:20:30,484 - signal_handler - INFO - Direct signal callback registered
2025-06-12 18:20:30,485 - signal_handler - INFO - [msg_1749732630484_9672] [STARTUP NOTIFICATION]: [ROCKET] Bot Started
[REFRESH] Mode: SIMULATION
[GRAPH] Strategy: AGGRESSIVE
[MONEY] Starting Capita...
2025-06-12 18:20:30,485 - signal_handler - INFO - [msg_1749732630484_9672] Resolving info channel entity for ID: -1002525039395
2025-06-12 18:20:30,486 - signal_handler - INFO - Signal listener running (attempt 1/3).
2025-06-12 18:20:30,543 - signal_handler - INFO - [msg_1749732630484_9672] Successfully resolved info channel entity: Bot Info 1
2025-06-12 18:20:30,649 - signal_handler - INFO - [msg_1749732630484_9672] Message sent directly successfully using entity
2025-06-12 18:20:30,650 - signal_handler - INFO - [msg_1749732630484_9672] Message sent directly successfully
2025-06-12 18:22:24,744 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$9503.3795(+206%)**...
2025-06-12 18:22:24,745 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 18:22:24,745 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 18:22:24,746 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 18:22:24,746 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 18:22:24,749 - signal_handler - INFO - Found contract address with 'pump' suffix: 25eoV2rjiunpPDeCo4Eiqr3HwCcrPedANhXjYMFRpump
2025-06-12 18:22:24,750 - signal_handler - INFO - Found contract address: FBhGt2dW68gMmhAZ4DjzfDPcQqVT8dWnNHThRfVgAuiD
2025-06-12 18:22:24,750 - signal_handler - INFO - Using 'pump' address as highest priority: 25eoV2rjiunpPDeCo4Eiqr3HwCcrPedANhXjYMFRpump
2025-06-12 18:22:24,752 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 25eoV2rjiunpPDeCo4Eiqr3HwCcrPedANhXjYMFRpump
2025-06-12 18:22:24,759 - signal_handler - INFO - Detected GMGN format
2025-06-12 18:22:24,772 - signal_handler - INFO - Found token symbol: 9503
2025-06-12 18:22:24,773 - signal_handler - INFO - Found FDV: 9503.3795 - 9.50K (+206%)
2025-06-12 18:22:24,775 - signal_handler - INFO - Detected GMGN channel signal for token: 25eoV2rjiunpPDeCo4Eiqr3HwCcrPedANhXjYMFRpump
2025-06-12 18:22:24,775 - signal_handler - INFO - Extracted signal: Token=25eoV2rjiunpPDeCo4Eiqr3HwCcrPedANhXjYMFRpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 18:22:24,776 - signal_handler - WARNING - LOW SIGNAL VOLUME: GMGN - 0/8 expected this hour
2025-06-12 18:22:24,776 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solbix - 0/2 expected this hour
2025-06-12 18:22:24,776 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solana Early Trending - 0/1 expected this hour
2025-06-12 18:22:24,776 - signal_handler - INFO - Signal processing health: 1 received, 1 processed, 0 dropped (100.0% success rate)
2025-06-12 18:22:24,776 - signal_handler - INFO - Added signal to queue: 25eoV2rjiunpPDeCo4Eiqr3HwCcrPedANhXjYMFRpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 18:22:24,777 - signal_handler - INFO - Calling direct signal callback for 25eoV2rjiunpPDeCo4Eiqr3HwCcrPedANhXjYMFRpump
