2025-06-12 19:42:54,237 - signal_handler - DEBUG - Loaded Telegram API ID: 27993698
2025-06-12 19:42:54,238 - signal_handler - DEBUG - Loaded Telegram API Hash: ********************************
2025-06-12 19:42:54,238 - signal_handler - INFO - Loaded session string from c:\Users\<USER>\Downloads\billy who\session_string.json (length: 353)
2025-06-12 19:42:54,238 - signal_handler - INFO - Using unique session name with timestamp: trading_sim_session_1749737574
2025-06-12 19:42:54,239 - signal_handler - INFO - Using session path: c:\Users\<USER>\Downloads\billy who\trading_sim_session_1749737574
2025-06-12 19:42:54,239 - signal_handler - INFO - Signal queue initialized with size: 0
2025-06-12 19:42:54,255 - signal_handler - INFO - Direct signal callback registered
2025-06-12 19:43:24,352 - signal_handler - INFO - API ID loaded: True
2025-06-12 19:43:24,352 - signal_handler - INFO - API Hash loaded: True
2025-06-12 19:43:24,352 - signal_handler - INFO - Phone number loaded: True
2025-06-12 19:43:24,353 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-12 19:43:24,353 - signal_handler - INFO - Creating new Telegram client (attempt 1/3)
2025-06-12 19:43:24,353 - signal_handler - INFO - Using saved session string
2025-06-12 19:43:24,354 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-12 19:43:24,635 - signal_handler - INFO - Checking authorization status...
2025-06-12 19:43:24,697 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 19:43:24,697 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-12 19:43:24,698 - signal_handler - INFO - Checking authorization status...
2025-06-12 19:43:24,750 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 19:43:24,751 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-12 19:43:24,751 - signal_handler - INFO - Checking authorization status...
2025-06-12 19:43:24,808 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 19:43:24,810 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-12 19:43:24,811 - signal_handler - INFO - Sending test message to verify connection to channel -1002525039395
2025-06-12 19:43:24,811 - signal_handler - INFO - Sending test message directly to channel ID: -1002525039395
2025-06-12 19:43:24,972 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-12 19:43:24,980 - signal_handler - INFO - [msg_1749737604976_7799] Queuing Telegram message:  PORTFOLIO UPDATE [?] PERIODIC UPDATE

 Timestamp: 2025-06-12 19:43:24
[REPEAT] Trade: SIMULATION

[...
2025-06-12 19:43:24,981 - signal_handler - INFO - Started Telegram message queue processor
2025-06-12 19:43:24,981 - signal_handler - INFO - [msg_1749737604976_7799] Message queued with normal priority
2025-06-12 19:43:24,981 - signal_handler - INFO - Message queue processor started with adaptive rate limiting
2025-06-12 19:43:24,982 - signal_handler - INFO - [msg_1749737604976_7799] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:43:25,035 - signal_handler - INFO - [msg_1749737604976_7799] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:43:25,129 - signal_handler - INFO - [msg_1749737604976_7799] Message sent directly successfully using entity
2025-06-12 19:43:25,129 - signal_handler - INFO - [msg_1749737604976_7799] Message sent successfully via direct send method
2025-06-12 19:43:25,130 - signal_handler - INFO - [msg_1749737604976_7799] Message sent successfully on attempt 1
2025-06-12 19:43:25,484 - signal_handler - WARNING - [WARNING] Using channel IDs directly (no entities resolved)
2025-06-12 19:43:25,484 - signal_handler - WARNING - Added event handler using channel IDs (entities not resolved yet)
2025-06-12 19:43:25,484 - signal_handler - INFO - Signal listener started in background task. Waiting for messages...
2025-06-12 19:43:25,486 - signal_handler - INFO - Direct signal callback registered
2025-06-12 19:43:25,487 - signal_handler - INFO - [msg_1749737605487_9661] [STARTUP NOTIFICATION]: [ROCKET] Bot Started
[REFRESH] Mode: SIMULATION
[GRAPH] Strategy: AGGRESSIVE
[MONEY] Starting Capita...
2025-06-12 19:43:25,487 - signal_handler - INFO - [msg_1749737605487_9661] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:43:25,488 - signal_handler - INFO - Signal listener running (attempt 1/3).
2025-06-12 19:43:25,541 - signal_handler - INFO - [msg_1749737605487_9661] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:43:25,654 - signal_handler - INFO - [msg_1749737605487_9661] Message sent directly successfully using entity
2025-06-12 19:43:25,655 - signal_handler - INFO - [msg_1749737605487_9661] Message sent directly successfully
2025-06-12 19:43:50,851 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$19K(+393.6%)**
**[...
2025-06-12 19:43:50,852 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 19:43:50,852 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 19:43:50,853 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 19:43:50,854 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 19:43:50,856 - signal_handler - INFO - Found contract address with 'pump' suffix: 7xy4oHCdg1v9rBfPwMc5nePVHCubd6sWSbWkmYAxpump
2025-06-12 19:43:50,857 - signal_handler - INFO - Found contract address: 3Hxrid85ThHCkhb8jSGMSHAAgfQVb2MM3Eef7JrEi8oJ
2025-06-12 19:43:50,858 - signal_handler - INFO - Using 'pump' address as highest priority: 7xy4oHCdg1v9rBfPwMc5nePVHCubd6sWSbWkmYAxpump
2025-06-12 19:43:50,859 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 7xy4oHCdg1v9rBfPwMc5nePVHCubd6sWSbWkmYAxpump
2025-06-12 19:43:50,861 - signal_handler - INFO - Detected GMGN format
2025-06-12 19:43:50,862 - signal_handler - INFO - Found token symbol: 19K
2025-06-12 19:43:50,863 - signal_handler - INFO - Found token full name: +393.6%
2025-06-12 19:43:50,864 - signal_handler - INFO - Found FDV: 19K - 19.00K (+393.6%)
2025-06-12 19:43:50,865 - signal_handler - INFO - Detected GMGN channel signal for token: 7xy4oHCdg1v9rBfPwMc5nePVHCubd6sWSbWkmYAxpump
2025-06-12 19:43:50,865 - signal_handler - INFO - Extracted signal: Token=7xy4oHCdg1v9rBfPwMc5nePVHCubd6sWSbWkmYAxpump, Source=solana signal alert - gmgn, Metrics Count=6
2025-06-12 19:43:50,866 - signal_handler - WARNING - LOW SIGNAL VOLUME: GMGN - 0/8 expected this hour
2025-06-12 19:43:50,866 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solbix - 0/2 expected this hour
2025-06-12 19:43:50,867 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solana Early Trending - 0/1 expected this hour
2025-06-12 19:43:50,867 - signal_handler - INFO - Signal processing health: 1 received, 1 processed, 0 dropped (100.0% success rate)
2025-06-12 19:43:50,868 - signal_handler - INFO - Added signal to queue: 7xy4oHCdg1v9rBfPwMc5nePVHCubd6sWSbWkmYAxpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 19:43:50,869 - signal_handler - INFO - Calling direct signal callback for 7xy4oHCdg1v9rBfPwMc5nePVHCubd6sWSbWkmYAxpump
