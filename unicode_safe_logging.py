#!/usr/bin/env python3
"""
Unicode-Safe Logging Configuration
Fixes Windows console encoding issues with emojis and Unicode characters
"""

import logging
import sys
import os
from typing import Dict, Any

class UnicodeFormatter(logging.Formatter):
    """Custom formatter that safely handles Unicode characters"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.emoji_replacements = {
            '✅': '[SUCCESS]',
            '❌': '[ERROR]',
            '⚠️': '[WARNING]',
            '🚨': '[ALERT]',
            '📊': '[METRICS]',
            '🔍': '[SCAN]',
            '🎯': '[TARGET]',
            '🚀': '[LAUNCH]',
            '💰': '[MONEY]',
            '📈': '[UP]',
            '📉': '[DOWN]',
            '🔴': '[RED]',
            '🟡': '[YELLOW]',
            '🟢': '[GREEN]',
            '⚡': '[FAST]',
            '🔧': '[FIX]',
            '🛡️': '[SHIELD]',
            '🎉': '[CELEBRATE]',
            '💡': '[IDEA]',
            '🔥': '[HOT]',
            '⏱️': '[TIME]',
            '📡': '[SIGNAL]',
            '💵': '[CASH]',
            '🖥️': '[SYSTEM]',
            '💾': '[MEMORY]',
            '💿': '[DISK]',
            '📬': '[QUEUE]',
            '🏥': '[HEALTH]',
            '🔒': '[SECURE]',
            '🌐': '[NETWORK]',
            '📋': '[LIST]',
            '🎪': '[EVENT]',
            '🔄': '[REFRESH]',
            '👁️': '[WATCH]',
            '🧠': '[BRAIN]',
            '📊': '[CHART]',
            '🎯': '[AIM]',
            '💪': '[STRONG]',
            '🏆': '[WIN]',
            '⭐': '[STAR]',
            '🔺': '[UP_ARROW]',
            '🔻': '[DOWN_ARROW]',
            '👋': '[WAVE]',
            '🎪': '[CIRCUS]'
        }
    
    def format(self, record):
        """Format log record with Unicode safety"""
        try:
            # Get the original formatted message
            formatted = super().format(record)
            
            # Replace emojis with text equivalents for Windows compatibility
            for emoji, replacement in self.emoji_replacements.items():
                formatted = formatted.replace(emoji, replacement)
            
            return formatted
        except UnicodeEncodeError:
            # Fallback: encode to ASCII and ignore errors
            try:
                formatted = super().format(record)
                return formatted.encode('ascii', 'ignore').decode('ascii')
            except Exception:
                return f"[ENCODING_ERROR] {record.getMessage()}"

class SafeStreamHandler(logging.StreamHandler):
    """Stream handler that safely handles Unicode output"""
    
    def __init__(self, stream=None):
        super().__init__(stream)
        
        # Try to configure the stream for UTF-8 if possible
        if hasattr(self.stream, 'reconfigure'):
            try:
                self.stream.reconfigure(encoding='utf-8', errors='replace')
            except Exception:
                pass
    
    def emit(self, record):
        """Emit log record with Unicode safety"""
        try:
            super().emit(record)
        except UnicodeEncodeError:
            # Fallback: try with ASCII encoding
            try:
                msg = self.format(record)
                safe_msg = msg.encode('ascii', 'ignore').decode('ascii')
                self.stream.write(safe_msg + self.terminator)
                self.flush()
            except Exception:
                # Last resort: basic error message
                self.stream.write(f"[LOGGING_ERROR] {record.levelname}: Message encoding failed\n")
                self.flush()

def setup_unicode_safe_logging():
    """Setup Unicode-safe logging configuration"""
    
    # Create Unicode-safe formatter
    formatter = UnicodeFormatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Get root logger
    root_logger = logging.getLogger()
    
    # Remove existing handlers to avoid duplicates
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # Create safe console handler
    console_handler = SafeStreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    console_handler.setLevel(logging.INFO)
    
    # Create safe file handler (UTF-8 with error handling)
    try:
        file_handler = logging.FileHandler(
            'trading_bot.log', 
            encoding='utf-8', 
            errors='replace'
        )
        file_handler.setFormatter(formatter)
        file_handler.setLevel(logging.DEBUG)
        root_logger.addHandler(file_handler)
    except Exception as e:
        print(f"Warning: Could not setup file logging: {e}")
    
    # Add console handler
    root_logger.addHandler(console_handler)
    root_logger.setLevel(logging.DEBUG)
    
    # Test the configuration
    test_logger = logging.getLogger('unicode_test')
    test_logger.info("[SUCCESS] Unicode-safe logging configured")
    
    return True

def safe_log_message(logger, level: str, message: str, **kwargs):
    """Safely log a message with automatic emoji replacement"""
    
    # Emoji to text mapping
    emoji_map = {
        '✅': '[SUCCESS]',
        '❌': '[ERROR]',
        '⚠️': '[WARNING]',
        '🚨': '[ALERT]',
        '📊': '[METRICS]',
        '🔍': '[SCAN]',
        '🎯': '[TARGET]',
        '🚀': '[LAUNCH]',
        '💰': '[MONEY]',
        '📈': '[UP]',
        '📉': '[DOWN]',
        '🔴': '[RED]',
        '🟡': '[YELLOW]',
        '🟢': '[GREEN]',
        '⚡': '[FAST]',
        '🔧': '[FIX]',
        '🛡️': '[SHIELD]',
        '🎉': '[CELEBRATE]',
        '💡': '[IDEA]'
    }
    
    # Replace emojis in message
    safe_message = message
    for emoji, replacement in emoji_map.items():
        safe_message = safe_message.replace(emoji, replacement)
    
    # Log with appropriate level
    log_func = getattr(logger, level.lower(), logger.info)
    try:
        log_func(safe_message, **kwargs)
    except UnicodeEncodeError:
        # Fallback to ASCII-only message
        ascii_message = safe_message.encode('ascii', 'ignore').decode('ascii')
        log_func(f"[UNICODE_SAFE] {ascii_message}", **kwargs)

# Convenience functions for safe logging
def safe_info(logger, message: str, **kwargs):
    """Safely log info message"""
    safe_log_message(logger, 'info', message, **kwargs)

def safe_warning(logger, message: str, **kwargs):
    """Safely log warning message"""
    safe_log_message(logger, 'warning', message, **kwargs)

def safe_error(logger, message: str, **kwargs):
    """Safely log error message"""
    safe_log_message(logger, 'error', message, **kwargs)

def safe_critical(logger, message: str, **kwargs):
    """Safely log critical message"""
    safe_log_message(logger, 'critical', message, **kwargs)

def safe_debug(logger, message: str, **kwargs):
    """Safely log debug message"""
    safe_log_message(logger, 'debug', message, **kwargs)

def configure_windows_console():
    """Configure Windows console for better Unicode support"""
    if os.name == 'nt':  # Windows
        try:
            # Try to enable UTF-8 mode
            import locale
            locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
        except Exception:
            pass
        
        try:
            # Try to configure console code page
            import subprocess
            subprocess.run(['chcp', '65001'], shell=True, capture_output=True)
        except Exception:
            pass
        
        try:
            # Configure stdout/stderr encoding
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8', errors='replace')
            if hasattr(sys.stderr, 'reconfigure'):
                sys.stderr.reconfigure(encoding='utf-8', errors='replace')
        except Exception:
            pass

def test_unicode_logging():
    """Test Unicode logging configuration"""
    print("Testing Unicode-safe logging...")
    
    # Setup safe logging
    setup_unicode_safe_logging()
    
    # Test logger
    test_logger = logging.getLogger('test')
    
    # Test various Unicode characters
    test_messages = [
        "✅ Success message with emoji",
        "❌ Error message with emoji", 
        "🚀 Launch message with rocket",
        "📊 Metrics with chart emoji",
        "Regular ASCII message",
        "Mixed: ✅ Success and 🚨 Alert"
    ]
    
    print("Testing log messages:")
    for msg in test_messages:
        try:
            test_logger.info(msg)
            print(f"  ✓ Logged: {msg}")
        except Exception as e:
            print(f"  ✗ Failed: {msg} - {e}")
    
    print("Unicode logging test complete!")

if __name__ == "__main__":
    # Run test when executed directly
    configure_windows_console()
    test_unicode_logging()
