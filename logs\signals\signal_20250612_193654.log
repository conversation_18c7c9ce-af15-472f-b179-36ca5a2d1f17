2025-06-12 19:36:54,662 - signal_handler - DEBUG - Loaded Telegram API ID: 27993698
2025-06-12 19:36:54,662 - signal_handler - DEBUG - Loaded Telegram API Hash: ********************************
2025-06-12 19:36:54,663 - signal_handler - INFO - Loaded session string from C:\Users\<USER>\Downloads\billy who\session_string.json (length: 353)
2025-06-12 19:36:54,664 - signal_handler - INFO - Using unique session name with timestamp: trading_sim_session_1749737214
2025-06-12 19:36:54,665 - signal_handler - INFO - Using session path: C:\Users\<USER>\Downloads\billy who\trading_sim_session_1749737214
2025-06-12 19:36:54,666 - signal_handler - INFO - Signal queue initialized with size: 0
2025-06-12 19:36:54,679 - signal_handler - INFO - Direct signal callback registered
2025-06-12 19:37:19,671 - signal_handler - INFO - API ID loaded: True
2025-06-12 19:37:19,671 - signal_handler - INFO - API Hash loaded: True
2025-06-12 19:37:19,671 - signal_handler - INFO - Phone number loaded: True
2025-06-12 19:37:19,672 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-12 19:37:19,685 - signal_handler - INFO - Creating new Telegram client (attempt 1/3)
2025-06-12 19:37:19,737 - signal_handler - INFO - Using saved session string
2025-06-12 19:37:19,738 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-12 19:37:20,067 - signal_handler - INFO - Checking authorization status...
2025-06-12 19:37:20,118 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 19:37:20,118 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-12 19:37:20,119 - signal_handler - INFO - Checking authorization status...
2025-06-12 19:37:20,172 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 19:37:20,173 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-12 19:37:20,175 - signal_handler - INFO - Checking authorization status...
2025-06-12 19:37:20,255 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 19:37:20,256 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-12 19:37:20,256 - signal_handler - INFO - Sending test message to verify connection to channel -1002525039395
2025-06-12 19:37:20,257 - signal_handler - INFO - Sending test message directly to channel ID: -1002525039395
2025-06-12 19:37:20,464 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-12 19:37:20,471 - signal_handler - INFO - [msg_1749737240467_6785] Queuing Telegram message:  PORTFOLIO UPDATE [?] PERIODIC UPDATE

 Timestamp: 2025-06-12 19:37:20
[REPEAT] Trade: SIMULATION

[...
2025-06-12 19:37:20,471 - signal_handler - INFO - Started Telegram message queue processor
2025-06-12 19:37:20,471 - signal_handler - INFO - [msg_1749737240467_6785] Message queued with normal priority
2025-06-12 19:37:20,472 - signal_handler - INFO - Message queue processor started with adaptive rate limiting
2025-06-12 19:37:20,472 - signal_handler - INFO - [msg_1749737240467_6785] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:37:20,538 - signal_handler - INFO - [msg_1749737240467_6785] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:37:20,636 - signal_handler - INFO - [msg_1749737240467_6785] Message sent directly successfully using entity
2025-06-12 19:37:20,637 - signal_handler - INFO - [msg_1749737240467_6785] Message sent successfully via direct send method
2025-06-12 19:37:20,637 - signal_handler - INFO - [msg_1749737240467_6785] Message sent successfully on attempt 1
2025-06-12 19:37:20,982 - signal_handler - WARNING - [WARNING] Using channel IDs directly (no entities resolved)
2025-06-12 19:37:20,982 - signal_handler - WARNING - Added event handler using channel IDs (entities not resolved yet)
2025-06-12 19:37:20,983 - signal_handler - INFO - Signal listener started in background task. Waiting for messages...
2025-06-12 19:37:20,984 - signal_handler - INFO - Direct signal callback registered
2025-06-12 19:37:20,987 - signal_handler - INFO - [msg_1749737240986_9599] [STARTUP NOTIFICATION]: [ROCKET] Bot Started
[REFRESH] Mode: SIMULATION
[GRAPH] Strategy: default
[MONEY] Starting Capital: ...
2025-06-12 19:37:20,988 - signal_handler - INFO - [msg_1749737240986_9599] Resolving info channel entity for ID: -1002525039395
2025-06-12 19:37:20,990 - signal_handler - INFO - Signal listener running (attempt 1/3).
2025-06-12 19:37:21,068 - signal_handler - INFO - [msg_1749737240986_9599] Successfully resolved info channel entity: Bot Info 1
2025-06-12 19:37:21,153 - signal_handler - INFO - [msg_1749737240986_9599] Message sent directly successfully using entity
2025-06-12 19:37:21,153 - signal_handler - INFO - [msg_1749737240986_9599] Message sent directly successfully
2025-06-12 19:37:35,370 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$10.6K(+229.1%)**
*...
2025-06-12 19:37:35,371 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-12 19:37:35,372 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-12 19:37:35,372 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-12 19:37:35,373 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 19:37:35,374 - signal_handler - INFO - Found contract address with 'pump' suffix: 6c1meLFtqJ8eTmfSsuiTFv8BW7jBzKmNDBdiSV8opump
2025-06-12 19:37:35,375 - signal_handler - INFO - Found contract address: 914Qq2LvCnHYr2wrupXKLGFXZa2qSEE19bd1d9J1csNe
2025-06-12 19:37:35,379 - signal_handler - INFO - Using 'pump' address as highest priority: 6c1meLFtqJ8eTmfSsuiTFv8BW7jBzKmNDBdiSV8opump
2025-06-12 19:37:35,380 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 6c1meLFtqJ8eTmfSsuiTFv8BW7jBzKmNDBdiSV8opump
2025-06-12 19:37:35,382 - signal_handler - INFO - Detected GMGN format
2025-06-12 19:37:35,382 - signal_handler - INFO - Found token symbol: 10
2025-06-12 19:37:35,383 - signal_handler - INFO - Found FDV: 10.6K - 10.60 (+229.1%)
2025-06-12 19:37:35,384 - signal_handler - INFO - Detected GMGN channel signal for token: 6c1meLFtqJ8eTmfSsuiTFv8BW7jBzKmNDBdiSV8opump
2025-06-12 19:37:35,384 - signal_handler - INFO - Extracted signal: Token=6c1meLFtqJ8eTmfSsuiTFv8BW7jBzKmNDBdiSV8opump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-12 19:37:35,385 - signal_handler - WARNING - LOW SIGNAL VOLUME: GMGN - 0/8 expected this hour
2025-06-12 19:37:35,385 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solbix - 0/2 expected this hour
2025-06-12 19:37:35,385 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solana Early Trending - 0/1 expected this hour
2025-06-12 19:37:35,385 - signal_handler - INFO - Signal processing health: 1 received, 1 processed, 0 dropped (100.0% success rate)
2025-06-12 19:37:35,386 - signal_handler - INFO - Added signal to queue: 6c1meLFtqJ8eTmfSsuiTFv8BW7jBzKmNDBdiSV8opump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-12 19:37:35,386 - signal_handler - INFO - Calling direct signal callback for 6c1meLFtqJ8eTmfSsuiTFv8BW7jBzKmNDBdiSV8opump
