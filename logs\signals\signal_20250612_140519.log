2025-06-12 14:05:19,277 - signal_handler - DEBUG - Loaded Telegram API ID: 27993698
2025-06-12 14:05:19,278 - signal_handler - DEBUG - Loaded Telegram API Hash: ********************************
2025-06-12 14:05:19,278 - signal_handler - INFO - Session string file not found: c:\Users\<USER>\Downloads\billy who\session_string.json. Will create a new session.
2025-06-12 14:05:19,278 - signal_handler - INFO - Using unique session name with timestamp: trading_sim_session_1749717319
2025-06-12 14:05:19,279 - signal_handler - INFO - Using session path: c:\Users\<USER>\Downloads\billy who\trading_sim_session_1749717319
2025-06-12 14:05:19,280 - signal_handler - INFO - Signal queue initialized with size: 0
2025-06-12 14:05:19,288 - signal_handler - INFO - Direct signal callback registered
2025-06-12 14:05:52,546 - signal_handler - INFO - [msg_1749717352543_5600] Queuing Telegram message: [WARNING] IMPORTANT BOT MESSAGE [?] 2025-06-12 14:05:52

[REFRESH] BOT STATE RESET

All positions, s...
2025-06-12 14:05:52,547 - signal_handler - INFO - Started Telegram message queue processor
2025-06-12 14:05:52,547 - signal_handler - INFO - [msg_1749717352543_5600] Message queued with normal priority
2025-06-12 14:05:52,570 - signal_handler - INFO - [msg_1749717352569_6131] Queuing Telegram message:  PORTFOLIO UPDATE [?] STATE RESET

 Timestamp: 2025-06-12 14:05:52
[REPEAT] Trade: SIMULATION

[MONE...
2025-06-12 14:05:52,570 - signal_handler - INFO - [msg_1749717352569_6131] Message queued with normal priority
2025-06-12 14:05:52,580 - signal_handler - INFO - Message queue processor started with adaptive rate limiting
2025-06-12 14:05:52,581 - signal_handler - WARNING - [msg_1749717352543_5600] Telegram client not connected before sending, attempting to reconnect
2025-06-12 14:05:52,582 - signal_handler - INFO - API ID loaded: True
2025-06-12 14:05:52,583 - signal_handler - INFO - API Hash loaded: True
2025-06-12 14:05:52,583 - signal_handler - INFO - Phone number loaded: True
2025-06-12 14:05:52,584 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-12 14:05:52,584 - signal_handler - INFO - Creating new Telegram client (attempt 1/3)
2025-06-12 14:05:52,585 - signal_handler - INFO - Using file-based session
2025-06-12 14:05:52,617 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-12 14:05:59,019 - signal_handler - INFO - Checking authorization status...
2025-06-12 14:05:59,421 - signal_handler - INFO - Authorization required. Sending code request...
2025-06-12 14:06:25,214 - signal_handler - INFO - [msg_1749717352543_5600] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:06:25,328 - signal_handler - WARNING - [msg_1749717352543_5600] Entity resolution or send failed: The key is not registered in the system (caused by GetChannelsRequest), trying direct ID approach
2025-06-12 14:06:25,329 - signal_handler - INFO - [msg_1749717352543_5600] Sending message directly to channel ID: -1002525039395
2025-06-12 14:06:25,379 - signal_handler - WARNING - [msg_1749717352543_5600] Direct send failed: The key is not registered in the system (caused by GetChannelsRequest)
2025-06-12 14:06:25,380 - signal_handler - INFO - [msg_1749717352543_5600] Trying final approach with string ID
2025-06-12 14:06:25,432 - signal_handler - ERROR - [msg_1749717352543_5600] All direct send approaches failed: The key is not registered in the system (caused by GetContactsRequest)
2025-06-12 14:06:25,432 - signal_handler - WARNING - [msg_1749717352543_5600] Direct send method failed, falling back to legacy approach
2025-06-12 14:06:25,432 - signal_handler - INFO - [msg_1749717352543_5600] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:06:25,482 - signal_handler - WARNING - [msg_1749717352543_5600] Failed to resolve info channel entity: The key is not registered in the system (caused by GetChannelsRequest)
2025-06-12 14:06:25,482 - signal_handler - INFO - [msg_1749717352543_5600] Attempting to send message using direct channel ID: -1002525039395
2025-06-12 14:06:25,535 - signal_handler - ERROR - [msg_1749717352543_5600] Direct ID send failed: The key is not registered in the system (caused by GetChannelsRequest)
2025-06-12 14:06:25,535 - signal_handler - INFO - [msg_1749717352543_5600] Attempting reconnection for retry...
2025-06-12 14:06:26,217 - signal_handler - INFO - [msg_1749717352543_5600] Reconnected to Telegram for retry
2025-06-12 14:06:26,271 - signal_handler - ERROR - [msg_1749717352543_5600] Retry after reconnect failed: The key is not registered in the system (caused by GetChannelsRequest)
2025-06-12 14:06:26,272 - signal_handler - ERROR - [msg_1749717352543_5600] Failed to send message after 1 attempts
2025-06-12 14:06:26,272 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-12 14:06:26,780 - signal_handler - INFO - [msg_1749717352569_6131] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:06:26,839 - signal_handler - WARNING - [msg_1749717352569_6131] Entity resolution or send failed: The key is not registered in the system (caused by GetChannelsRequest), trying direct ID approach
2025-06-12 14:06:26,840 - signal_handler - INFO - [msg_1749717352569_6131] Sending message directly to channel ID: -1002525039395
2025-06-12 14:06:26,895 - signal_handler - WARNING - [msg_1749717352569_6131] Direct send failed: The key is not registered in the system (caused by GetChannelsRequest)
2025-06-12 14:06:26,897 - signal_handler - INFO - [msg_1749717352569_6131] Trying final approach with string ID
2025-06-12 14:06:26,949 - signal_handler - ERROR - [msg_1749717352569_6131] All direct send approaches failed: The key is not registered in the system (caused by GetContactsRequest)
2025-06-12 14:06:26,950 - signal_handler - WARNING - [msg_1749717352569_6131] Direct send method failed, falling back to legacy approach
2025-06-12 14:06:26,950 - signal_handler - INFO - [msg_1749717352569_6131] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:06:27,032 - signal_handler - WARNING - [msg_1749717352569_6131] Failed to resolve info channel entity: The key is not registered in the system (caused by GetChannelsRequest)
2025-06-12 14:06:27,033 - signal_handler - INFO - [msg_1749717352569_6131] Attempting to send message using direct channel ID: -1002525039395
2025-06-12 14:06:27,091 - signal_handler - ERROR - [msg_1749717352569_6131] Direct ID send failed: The key is not registered in the system (caused by GetChannelsRequest)
2025-06-12 14:06:27,093 - signal_handler - INFO - [msg_1749717352569_6131] Attempting reconnection for retry...
2025-06-12 14:06:27,785 - signal_handler - INFO - [msg_1749717352569_6131] Reconnected to Telegram for retry
2025-06-12 14:06:27,852 - signal_handler - ERROR - [msg_1749717352569_6131] Retry after reconnect failed: The key is not registered in the system (caused by GetChannelsRequest)
2025-06-12 14:06:27,853 - signal_handler - ERROR - [msg_1749717352569_6131] Failed to send message after 1 attempts
2025-06-12 14:07:02,200 - signal_handler - INFO - API ID loaded: True
2025-06-12 14:07:02,201 - signal_handler - INFO - API Hash loaded: True
2025-06-12 14:07:02,201 - signal_handler - INFO - Phone number loaded: True
2025-06-12 14:07:02,202 - signal_handler - INFO - Session file is valid and not locked
2025-06-12 14:07:02,202 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-12 14:07:02,202 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-12 14:07:02,203 - signal_handler - INFO - Checking authorization status...
2025-06-12 14:07:02,255 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 14:07:02,256 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-12 14:07:02,256 - signal_handler - INFO - Checking authorization status...
2025-06-12 14:07:02,306 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 14:07:02,306 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-12 14:07:02,306 - signal_handler - INFO - Checking authorization status...
2025-06-12 14:07:02,372 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-12 14:07:02,373 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-12 14:07:02,373 - signal_handler - INFO - Sending test message to verify connection to channel -1002525039395
2025-06-12 14:07:02,373 - signal_handler - INFO - Sending test message directly to channel ID: -1002525039395
2025-06-12 14:07:02,564 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-12 14:07:02,567 - signal_handler - INFO - [msg_1749717422567_7024] Queuing Telegram message:  PORTFOLIO UPDATE [?] PERIODIC UPDATE

 Timestamp: 2025-06-12 14:07:02
[REPEAT] Trade: SIMULATION

[...
2025-06-12 14:07:02,567 - signal_handler - INFO - [msg_1749717422567_7024] Message queued with normal priority
2025-06-12 14:07:02,630 - signal_handler - INFO - [msg_1749717422567_7024] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:07:02,721 - signal_handler - INFO - [msg_1749717422567_7024] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:07:02,818 - signal_handler - INFO - [msg_1749717422567_7024] Message sent directly successfully using entity
2025-06-12 14:07:02,819 - signal_handler - INFO - [msg_1749717422567_7024] Message sent successfully via direct send method
2025-06-12 14:07:02,820 - signal_handler - INFO - [msg_1749717422567_7024] Message sent successfully on attempt 1
2025-06-12 14:07:03,076 - signal_handler - WARNING - [WARNING] Using channel IDs directly (no entities resolved)
2025-06-12 14:07:03,077 - signal_handler - WARNING - Added event handler using channel IDs (entities not resolved yet)
2025-06-12 14:07:03,077 - signal_handler - INFO - Signal listener started in background task. Waiting for messages...
2025-06-12 14:07:03,080 - signal_handler - INFO - Direct signal callback registered
2025-06-12 14:07:03,083 - signal_handler - INFO - [msg_1749717423082_3077] [STARTUP NOTIFICATION]: [ROCKET] Bot Started
[REFRESH] Mode: SIMULATION
[GRAPH] Strategy: default
[MONEY] Starting Capital: ...
2025-06-12 14:07:03,084 - signal_handler - INFO - [msg_1749717423082_3077] Resolving info channel entity for ID: -1002525039395
2025-06-12 14:07:03,085 - signal_handler - INFO - Signal listener running (attempt 1/3).
2025-06-12 14:07:03,164 - signal_handler - INFO - [msg_1749717423082_3077] Successfully resolved info channel entity: Bot Info 1
2025-06-12 14:07:03,253 - signal_handler - INFO - [msg_1749717423082_3077] Message sent directly successfully using entity
2025-06-12 14:07:03,254 - signal_handler - INFO - [msg_1749717423082_3077] Message sent directly successfully
2025-06-12 14:09:12,802 - signal_handler - INFO - Received message from channel -1002177594166: 7us1kj3u5D5fTf5Z6kDjLTdLpKTEcbe728XguCLpump...
2025-06-12 14:09:12,804 - signal_handler - INFO - Setting channel_identifier to 'OxyZen Calls' based on chat_id -1002177594166
2025-06-12 14:09:12,806 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-12 14:09:12,807 - signal_handler - INFO - Found contract address with 'pump' suffix: 7us1kj3u5D5fTf5Z6kDjLTdLpKTEcbe728XguCLpump
2025-06-12 14:09:12,808 - signal_handler - INFO - Using 'pump' address as highest priority: 7us1kj3u5D5fTf5Z6kDjLTdLpKTEcbe728XguCLpump
2025-06-12 14:09:12,809 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: 7us1kj3u5D5fTf5Z6kDjLTdLpKTEcbe728XguCLpump
2025-06-12 14:09:12,810 - signal_handler - INFO - Detected signal from OxyZen Calls for token: 7us1kj3u5D5fTf5Z6kDjLTdLpKTEcbe728XguCLpump
2025-06-12 14:09:12,810 - signal_handler - INFO - Extracted signal: Token=7us1kj3u5D5fTf5Z6kDjLTdLpKTEcbe728XguCLpump, Source=OxyZen Calls, Metrics Count=1
2025-06-12 14:09:12,811 - signal_handler - WARNING - LOW SIGNAL VOLUME: GMGN - 0/8 expected this hour
2025-06-12 14:09:12,811 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solbix - 0/2 expected this hour
2025-06-12 14:09:12,811 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solana Early Trending - 0/1 expected this hour
2025-06-12 14:09:12,812 - signal_handler - INFO - Signal processing health: 1 received, 1 processed, 0 dropped (100.0% success rate)
2025-06-12 14:09:12,812 - signal_handler - INFO - Added signal to queue: 7us1kj3u5D5fTf5Z6kDjLTdLpKTEcbe728XguCLpump from OxyZen Calls with confidence 0.5, GMGN channel: False
2025-06-12 14:09:12,813 - signal_handler - INFO - Calling direct signal callback for 7us1kj3u5D5fTf5Z6kDjLTdLpKTEcbe728XguCLpump
