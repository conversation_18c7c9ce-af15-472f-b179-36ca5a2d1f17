import asyncio
import logging
import time
from datetime import datetime
from typing import Optional
from pumpportal_trader import PumpPortalTrader
from utils import (
    log_trade,
    log_performance,
    log_high_performing_token,
    log_error,
    format_sol_amount,
    format_usd_amount,
    calculate_pnl
)

# OPTIMIZED: Removed unused Dict, Any imports and added performance monitoring
def monitor_performance(operation_name: str):
    """Decorator to monitor trade execution performance"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = await func(*args, **kwargs)
                duration = time.time() - start_time
                logger.debug(f"{operation_name} completed in {duration:.3f}s")
                return result
            except Exception as e:
                duration = time.time() - start_time
                logger.error(f"{operation_name} failed after {duration:.3f}s: {e}")
                raise
        return wrapper
    return decorator

logger = logging.getLogger(__name__)

class TradeExecutor:
    def __init__(self, config_manager, state_manager, pumpportal_trader: Optional[PumpPortalTrader] = None):
        self.config_manager = config_manager
        self.state_manager = state_manager
        self.pumpportal_trader = pumpportal_trader
        self.trading_settings = config_manager.get_trading_settings()
        self.notified_buy_event_ids = set()
        self.notified_sell_event_ids = set()

        # Dictionary to store event data for metadata tracking
        self.event_data = {}

        logger.info("TradeExecutor initialized with PumpPortal trader for real mode")

    # GMGN-specific methods removed - using PumpPortal trader for real mode

    @monitor_performance("BUY_EXECUTION")
    async def execute_buy(self, token_address: str, amount: float, price: float, slippage_bps: int, is_simulation: bool = False, event_id: Optional[str] = None, skip_notification: bool = False) -> bool:
        """Execute buy order via simulation or PumpPortal trader, preventing duplicate actions for the same event_id."""
        try:
            if event_id:
                if event_id in self.notified_buy_event_ids:
                    logger.info(f"Buy action for event_id '{event_id}' (token: {token_address}) already processed. Skipping duplicate action.")
                    return True
            else:
                logger.warning(f"execute_buy called for {token_address} without an event_id. Duplicate action prevention may not be effective.")

            if is_simulation:
                print(f"[SIM] Buying {format_sol_amount(amount)} SOL of {token_address} at {format_usd_amount(price)} (Slippage: {slippage_bps}bps) EventID: {event_id}")
                success = True
            else:
                # Use PumpPortal trader for real buys
                if not self.pumpportal_trader:
                    logger.error("Cannot execute real buy: PumpPortal trader not initialized.")
                    return False

                # Convert slippage from bps to percentage
                slippage_percent = slippage_bps / 100.0

                print(f"[REAL - PUMPPORTAL] Attempting to buy {format_sol_amount(amount)} SOL of {token_address} via PumpPortal. EventID: {event_id}")
                logger.info(f"Real buy: {amount:.4f} SOL of {token_address} with {slippage_percent:.1f}% slippage")

                # Execute buy via PumpPortal
                try:
                    result = await self.pumpportal_trader.buy_token(
                        token_address=token_address.strip(),
                        sol_amount=amount,
                        slippage_percent=slippage_percent
                    )
                    success = result.get("success", False)

                    if not success:
                        error_msg = result.get("error", "Unknown error")
                        error_code = result.get("error_code", "UNKNOWN")
                        logger.error(f"Buy failed: {error_msg} (Code: {error_code})")

                        # Handle specific error codes
                        if error_code == "INSUFFICIENT_BALANCE":
                            logger.error("Insufficient balance - check wallet SOL amount")
                        elif error_code == "NO_WALLET":
                            logger.error("No wallet configured - check .env file")
                    else:
                        tx_sig = result.get("data", {}).get("transaction_signature", "")
                        logger.info(f"Buy successful: {tx_sig}")

                    if success:
                        print(f"[REAL - PUMPPORTAL] ✅ Buy successful for {token_address}")
                        logger.info(f"PumpPortal buy successful for {token_address}")
                    else:
                        print(f"[REAL - PUMPPORTAL] ❌ Buy failed for {token_address}")
                        logger.error(f"PumpPortal buy failed for {token_address}")

                except Exception as buy_ex:
                    logger.error(f"Exception in PumpPortal buy: {buy_ex}")
                    success = False

            if success:
                log_trade("buy", token_address, amount, price)
                if event_id:
                    self.notified_buy_event_ids.add(event_id)
                    logger.info(f"Added {event_id} to notified_buy_event_ids. Current size: {len(self.notified_buy_event_ids)}")

                # Send a direct buy notification to Telegram only if not skipped
                if is_simulation and not skip_notification:
                    # Create a buy notification for simulation mode
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    buy_notification_msg = (
                        f"🟢 [SIM BUY EXECUTED] — {timestamp}\n"
                        f"━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\n"
                        f"📍 Signal Source: Direct Execution\n"
                        f"📦 Token: {token_address}\n"
                        f"📡 Confidence Score: N/A (⚙️ Tech: N/A, Final: N/A)\n\n"
                        f"📈 Token Metrics:\n"
                        f" • Market Cap: N/A\n"
                        f" • Liquidity: N/A\n"
                        f" • 24h Volume: N/A\n"
                        f" • Volatility (24h): N/A\n"
                        f" • Age: N/A\n"
                        f" • Tx Count (1h): N/A\n\n"
                        f"💰 Trade Executed:\n"
                        f" • Mode: SIMULATION\n"
                        f" • Amount: {amount} SOL\n"
                        f" • Entry Price: ${price:.8f}\n\n"
                        f"🎯 Strategy: DEFAULT (TP +15.0% / SL -30.0%)\n\n"
                        f"💼 Wallet Update:\n"
                        f" • Post-Trade Balance: N/A\n"
                        f" • Total Trades: N/A\n"
                        f" • Winrate: N/A\n"
                        f" • PnL: N/A\n\n"
                        f"🔗 View Token:\n"
                        f" • [DexScreener](https://dexscreener.com/solana/{token_address})"
                    )

                    # Print to console
                    print(f"\nBUY NOTIFICATION BEING SENT DIRECTLY FROM TRADE EXECUTOR\n")

                    # Try to send the notification directly using the signal_handler from BotController
                    try:
                        # First, try to import BotController and use its instance
                        from bot_controller import BotController

                        # Check if BotController.instance exists
                        if hasattr(BotController, 'instance') and BotController.instance:
                            if hasattr(BotController.instance, 'signal_handler'):
                                # Create a task and wait for it to complete
                                notification_task = asyncio.create_task(
                                    BotController.instance.signal_handler.send_info_message(buy_notification_msg)
                                )
                                # Wait for the notification to be sent (with timeout)
                                try:
                                    await asyncio.wait_for(notification_task, timeout=10.0)
                                    logger.info(f"Buy notification sent successfully from TradeExecutor for {token_address}")
                                except asyncio.TimeoutError:
                                    logger.warning(f"Timeout waiting for buy notification to be sent for {token_address}")
                                except Exception as wait_error:
                                    logger.error(f"Error waiting for buy notification to be sent: {wait_error}")
                            else:
                                logger.warning("BotController.instance.signal_handler not available for sending buy notification")
                                # Try to use the config_manager to send a notification directly
                                if hasattr(self, 'config_manager') and self.config_manager:
                                    try:
                                        # Create a temporary SignalHandler
                                        from signal_handler import SignalHandler
                                        temp_signal_handler = SignalHandler(self.config_manager)
                                        await temp_signal_handler.connect()
                                        await temp_signal_handler.send_info_message(buy_notification_msg)
                                        logger.info(f"Buy notification sent using temporary SignalHandler for {token_address}")
                                        await temp_signal_handler.disconnect()
                                    except Exception as temp_handler_error:
                                        logger.error(f"Error using temporary SignalHandler: {temp_handler_error}")
                        else:
                            logger.warning("BotController.instance not available for sending buy notification")
                            # Try to use the config_manager to send a notification directly
                            if hasattr(self, 'config_manager') and self.config_manager:
                                try:
                                    # Create a temporary SignalHandler
                                    from signal_handler import SignalHandler
                                    temp_signal_handler = SignalHandler(self.config_manager)
                                    await temp_signal_handler.connect()
                                    await temp_signal_handler.send_info_message(buy_notification_msg)
                                    logger.info(f"Buy notification sent using temporary SignalHandler for {token_address}")
                                    await temp_signal_handler.disconnect()
                                except Exception as temp_handler_error:
                                    logger.error(f"Error using temporary SignalHandler: {temp_handler_error}")
                    except Exception as notification_error:
                        logger.error(f"Error sending buy notification from TradeExecutor: {notification_error}")

                # Update the state manager with the new position
                try:
                    # Get metadata from the event_data if available
                    event_metadata = self.event_data.get(event_id, {}).get('metadata', {}) if event_id else {}

                    # Create metadata for the position
                    metadata = {
                        "source": event_metadata.get('source', 'trade_executor'),
                        "signal_source": event_metadata.get('signal_source', 'unknown'),
                        "channel_id": event_metadata.get('channel_id', 'unknown'),
                        "channel_name": event_metadata.get('channel_name', 'unknown'),
                        "is_gmgn_channel": event_metadata.get('is_gmgn_channel', False),
                        "detection_time": event_metadata.get('detection_time', 0),
                        "event_id": event_id,
                        "timestamp": time.time(),
                        "token_symbol": event_metadata.get('token_symbol', "Unknown"),
                        "token_name": event_metadata.get('token_name', "Unknown"),
                        "initial_confidence": event_metadata.get('initial_confidence', 0.0),
                        "entry_liquidity_usd": event_metadata.get('liquidity_usd', 0),
                        "entry_holder_count": event_metadata.get('holder_count', 0),
                        "entry_top10_percent": event_metadata.get('top10_percent', 0)
                    }

                    # CRITICAL FIX: Open the position in the state manager with await
                    await self.state_manager.open_position(
                        token=token_address,
                        entry_price=price,
                        sol_amount=amount,
                        strategy="default",  # Use default strategy
                        metadata=metadata
                    )

                    logger.info(f"Position opened in state manager for {token_address} with {amount:.4f} SOL at ${price:.8f}")

                    # BULLETPROOF: Mark token as bought for position monitoring
                    try:
                        from main import mark_token_permanent_decision
                        mark_token_permanent_decision(token_address, "bought", f"Successfully bought {amount:.4f} SOL")
                    except ImportError:
                        logger.warning(f"Could not mark {token_address} as bought - import failed")

                except Exception as state_error:
                    logger.error(f"Error updating state after buy for {token_address}: {state_error}")

                return True
            else:
                 # Error already logged by PumpPortal trader or printed above
                log_error("buy_error", f"Failed PumpPortal buy command for {token_address}", token_address)
                return False

        except Exception as e:
            logger.error(f"Error in execute_buy for {token_address}: {e}", exc_info=True)
            log_error("buy_error", str(e), token_address)
            return False

    @monitor_performance("SELL_EXECUTION")
    async def execute_sell(self, token_address: str, token_amount: float, entry_price: float, slippage_bps: int, sell_fraction: float = 1.0, is_simulation: bool = False, event_id: Optional[str] = None) -> bool:
        """Execute sell order (selling token_amount * sell_fraction) via simulation or GMGN bot, preventing duplicate actions for the same event_id."""
        try:
            logger.info(f"SELL DEBUG: execute_sell called for {token_address} with token_amount={token_amount}, entry_price={entry_price}, sell_fraction={sell_fraction}, EventID: {event_id}")

            if event_id:
                if event_id in self.notified_sell_event_ids:
                    logger.info(f"Sell action for event_id '{event_id}' (token: {token_address}) already processed. Skipping duplicate action.")
                    return True
            else:
                logger.warning(f"execute_sell called for {token_address} without an event_id. Duplicate action prevention may not be effective.")

            amount_to_sell_tokens = token_amount * sell_fraction # Calculate actual token amount based on fraction
            logger.info(f"SELL DEBUG: Calculated amount_to_sell_tokens={amount_to_sell_tokens} (token_amount={token_amount}, sell_fraction={sell_fraction})")

            if amount_to_sell_tokens <= 1e-9: # Avoid selling dust
                logger.error(f"SELL FAILED: Sell amount for {token_address} is near zero ({amount_to_sell_tokens}). Token amount: {token_amount}, Sell fraction: {sell_fraction}")
                return False

            if is_simulation:
                # Simulate sell - requires expected price which we don't have easily now
                # We'll rely on BotController/StateManager to handle sim PnL based on the price it used for the trigger
                print(f"[SIM] Selling {amount_to_sell_tokens:.4f} tokens ({sell_fraction*100:.1f}%) of {token_address} (Entry: {format_usd_amount(entry_price)} | Slippage: {slippage_bps}bps)")
                success = True
                # Sim PnL/logging happens in StateManager triggered by BotController now
            else:
                # Use PumpPortal trader for real sells
                if not self.pumpportal_trader:
                    logger.error("Cannot execute real sell: PumpPortal trader not initialized.")
                    return False

                # Convert slippage from bps to percentage
                slippage_percent = slippage_bps / 100.0

                clean_token_address = token_address.strip()

                print(f"[REAL - PUMPPORTAL] Attempting to sell {amount_to_sell_tokens:.4f} tokens ({sell_fraction*100:.1f}%) of {clean_token_address} via PumpPortal.")
                logger.info(f"Real sell: {amount_to_sell_tokens:.4f} tokens of {clean_token_address} with {slippage_percent:.1f}% slippage")

                # Execute sell via PumpPortal
                try:
                    result = await self.pumpportal_trader.sell_token(
                        token_address=clean_token_address,
                        token_amount=amount_to_sell_tokens,
                        slippage_percent=slippage_percent
                    )
                    success = result.get("success", False)

                    if not success:
                        error_msg = result.get("error", "Unknown error")
                        error_code = result.get("error_code", "UNKNOWN")
                        logger.error(f"Sell failed: {error_msg} (Code: {error_code})")

                        # Handle specific error codes
                        if error_code == "TRADE_EXECUTION_FAILED":
                            retry_after = result.get("retry_after", 5)
                            logger.warning(f"Trade execution failed - retry after {retry_after}s")
                        elif error_code == "NO_WALLET":
                            logger.error("No wallet configured - check .env file")
                    else:
                        tx_sig = result.get("data", {}).get("transaction_signature", "")
                        logger.info(f"Sell successful: {tx_sig}")

                    if success:
                        print(f"[REAL - PUMPPORTAL] ✅ Sell successful for {clean_token_address}")
                        logger.info(f"PumpPortal sell successful for {clean_token_address}")
                    else:
                        print(f"[REAL - PUMPPORTAL] ❌ Sell failed for {clean_token_address}")
                        logger.error(f"PumpPortal sell failed for {clean_token_address}")

                except Exception as sell_ex:
                    logger.error(f"Exception in PumpPortal sell: {sell_ex}")
                    success = False

            if success:
                # Get current market price for proper logging
                try:
                    current_price = 0.0
                    if hasattr(self, 'token_analyzer') and self.token_analyzer:
                        # Try to get current price from token analyzer
                        analysis = await self.token_analyzer.analyze(token_address, force_fresh=True)
                        if analysis and analysis.get('exists', False):
                            current_price = analysis.get('price', 0.0)

                    # If we couldn't get current price, use entry price as fallback
                    if current_price <= 0:
                        current_price = entry_price
                        logger.warning(f"Could not get current price for {token_address}, using entry price {entry_price}")

                    # Calculate estimated PnL for logging
                    estimated_pnl_percent = ((current_price - entry_price) / entry_price * 100) if entry_price > 0 else 0

                    logger.info(f"SELL PRICE LOGGING: {token_address} - Entry: {entry_price:.8f}, Current: {current_price:.8f}, Est. PnL: {estimated_pnl_percent:.2f}%")

                except Exception as e:
                    logger.error(f"Error getting current price for sell logging: {e}")
                    current_price = entry_price
                    estimated_pnl_percent = 0

                # Log trade attempt with actual price data
                log_trade("sell_attempt", token_address, amount_to_sell_tokens, current_price, estimated_pnl_percent, details=f"Fraction: {sell_fraction*100:.1f}%")
                if event_id:
                    self.notified_sell_event_ids.add(event_id)
                    logger.info(f"SELL DEBUG: Added {event_id} to notified_sell_event_ids. Current size: {len(self.notified_sell_event_ids)}")

                # BULLETPROOF: Remove from position monitoring if full sell
                if sell_fraction >= 1.0:
                    try:
                        from main import remove_sold_position
                        remove_sold_position(token_address, f"Full sell executed (fraction: {sell_fraction})")
                    except ImportError:
                        logger.warning(f"Could not remove {token_address} from position monitoring - import failed")

                return True
            else:
                # Error already logged by PumpPortal trader or printed above
                log_error("sell_error", f"Failed PumpPortal sell command for {token_address}", token_address)
                return False

        except Exception as e:
            logger.error(f"Error in execute_sell for {token_address}: {e}", exc_info=True)
            log_error("sell_error", str(e), token_address)
            return False

    def create_moonbag(self, token_address: str, amount: float, price: float, is_simulation: bool = False) -> bool:
        """Create moonbag position"""
        try:
            if is_simulation:
                print(f"[SIM] Creating moonbag of {format_sol_amount(amount)} at {format_usd_amount(price)}")
            else:
                # Implement real moonbag creation logic here
                pass

            log_trade("moonbag_create", token_address, amount, price)
            return True

        except Exception as e:
            log_error("moonbag_error", str(e), token_address)
            return False

    def sell_moonbag(self, token_address: str, amount: float, price: float, entry_price: float, is_simulation: bool = False) -> bool:
        """Sell moonbag position"""
        try:
            pnl = calculate_pnl(entry_price, price, amount)

            if is_simulation:
                print(f"[SIM] Selling moonbag of {format_sol_amount(amount)} at {format_usd_amount(price)} (PNL: {format_usd_amount(pnl)})")
            else:
                # Implement real moonbag sell logic here
                pass

            log_trade("moonbag_sell", token_address, amount, price, pnl)
            log_performance(token_address, entry_price, price, pnl, 0)  # Duration will be updated later

            if pnl > 0:
                log_high_performing_token(token_address, pnl, amount * price)

            return True

        except Exception as e:
            log_error("moonbag_sell_error", str(e), token_address)
            return False