2025-06-14 17:40:21,249 - signal_handler - INFO - Direct signal processing completed for E3coPuGRRkNiWuhYaXvceg1r7Vwh4xkLnbzYgAkZpump
2025-06-14 17:40:21,249 - signal_handler - INFO - Signal forwarded to bot controller: E3coPuGRRkNiWuhYaXvceg1r7Vwh4xkLnbzYgAkZpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-14 17:40:21,250 - signal_handler - INFO - Retrieved signal from queue: E3coPuGRRkNiWuhYaXvceg1r7Vwh4xkLnbzYgAkZpump from solana signal alert - gmgn
2025-06-14 17:40:21,251 - signal_handler - INFO - [msg_1749903021250_6643] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-14 17:40:21

 Token CA: E3coPuGRRkNiWuhYaXvceg1r7Vwh4xkLnbzYgAkZp...
2025-06-14 17:40:21,252 - signal_handler - INFO - [msg_1749903021250_6643] Message queued with normal priority
2025-06-14 17:40:28,216 - signal_handler - INFO - [msg_1749903021250_6643] Resolving info channel entity for ID: -1002525039395
2025-06-14 17:40:28,534 - signal_handler - INFO - [msg_1749903021250_6643] Successfully resolved info channel entity: Bot Info 1
2025-06-14 17:40:34,398 - signal_handler - INFO - Received sell notification: [PURPLE] [SIM SELL [?] EMERGENCY RUG DETECTED - ZERO LIQUIDITY ($0)] [?] 2025-06-14 17:40:34
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]...
2025-06-14 17:40:34,401 - signal_handler - INFO - [msg_1749903034398_3470] [SELL NOTIFICATION]:  [SIM SELL [?] EMERGENCY RUG DETECTED - ZERO LIQUIDITY ($0)] [?] 2025-06-14 17:40:34

 Triggered by:...
2025-06-14 17:40:34,406 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749903034398_3470.txt
2025-06-14 17:40:34,408 - signal_handler - INFO - [msg_1749903034398_3470] Resolving info channel entity for ID: -1002525039395
2025-06-14 17:40:34,414 - signal_handler - WARNING - [msg_1749903021250_6643] Entity resolution or send failed: , trying direct ID approach
2025-06-14 17:40:34,415 - signal_handler - INFO - [msg_1749903021250_6643] Sending message directly to channel ID: -1002525039395
2025-06-14 17:40:34,462 - signal_handler - INFO - [msg_1749903034398_3470] Successfully resolved info channel entity: Bot Info 1
2025-06-14 17:40:34,525 - signal_handler - INFO - [msg_1749903021250_6643] Message sent directly successfully using ID
2025-06-14 17:40:34,526 - signal_handler - INFO - [msg_1749903021250_6643] Message sent successfully via direct send method
2025-06-14 17:40:34,527 - signal_handler - INFO - [msg_1749903021250_6643] Message sent successfully on attempt 1
2025-06-14 17:40:34,557 - signal_handler - INFO - [msg_1749903034555_3536] [SELL NOTIFICATION]: [GREEN] [SIM BUY EXECUTED] [?] 2025-06-14 17:40:34

 Signal Source: solana signal alert - gmgn
 Toke...
2025-06-14 17:40:34,559 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749903034555_3536.txt
2025-06-14 17:40:34,559 - signal_handler - INFO - [msg_1749903034555_3536] Resolving info channel entity for ID: -1002525039395
2025-06-14 17:40:34,566 - signal_handler - INFO - [msg_1749903034398_3470] Message sent directly successfully using entity
2025-06-14 17:40:34,566 - signal_handler - INFO - [msg_1749903034398_3470] Message sent directly successfully
2025-06-14 17:40:34,567 - signal_handler - INFO - [msg_1749903034566_6802] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-14 17:40:34
[REPEAT] Trade: SIMULATION

[MONEY...
2025-06-14 17:40:34,567 - signal_handler - INFO - [msg_1749903034566_6802] Message queued with normal priority
2025-06-14 17:40:34,614 - signal_handler - INFO - [msg_1749903034555_3536] Successfully resolved info channel entity: Bot Info 1
2025-06-14 17:40:34,632 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.40s (interval: 0.50s)
2025-06-14 17:40:34,733 - signal_handler - INFO - [msg_1749903034555_3536] Message sent directly successfully using entity
2025-06-14 17:40:34,734 - signal_handler - INFO - [msg_1749903034555_3536] Message sent directly successfully
2025-06-14 17:40:34,736 - signal_handler - INFO - [msg_1749903034735_4580] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (SIMULATION) - E3COPUGR

 Timestamp: 2025-06-14 17:40:34
[REPEAT] Tr...
2025-06-14 17:40:34,737 - signal_handler - INFO - [msg_1749903034735_4580] Message queued with normal priority
2025-06-14 17:40:35,034 - signal_handler - INFO - [msg_1749903034566_6802] Resolving info channel entity for ID: -1002525039395
2025-06-14 17:40:35,111 - signal_handler - INFO - [msg_1749903034566_6802] Successfully resolved info channel entity: Bot Info 1
2025-06-14 17:40:35,211 - signal_handler - INFO - [msg_1749903034566_6802] Message sent directly successfully using entity
2025-06-14 17:40:35,213 - signal_handler - INFO - [msg_1749903034566_6802] Message sent successfully via direct send method
2025-06-14 17:40:35,214 - signal_handler - INFO - [msg_1749903034566_6802] Message sent successfully on attempt 1
2025-06-14 17:40:35,215 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-14 17:40:35,730 - signal_handler - INFO - [msg_1749903034735_4580] Resolving info channel entity for ID: -1002525039395
2025-06-14 17:40:35,796 - signal_handler - INFO - [msg_1749903034735_4580] Successfully resolved info channel entity: Bot Info 1
2025-06-14 17:40:35,898 - signal_handler - INFO - [msg_1749903034735_4580] Message sent directly successfully using entity
2025-06-14 17:40:35,900 - signal_handler - INFO - [msg_1749903034735_4580] Message sent successfully via direct send method
2025-06-14 17:40:35,901 - signal_handler - INFO - [msg_1749903034735_4580] Message sent successfully on attempt 1
2025-06-14 17:41:21,338 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$9625.2794(+225.3%)...
2025-06-14 17:41:21,340 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-14 17:41:21,341 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-14 17:41:21,342 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-14 17:41:21,343 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-14 17:41:21,344 - signal_handler - INFO - Found contract address with 'pump' suffix: ********************************************
2025-06-14 17:41:21,345 - signal_handler - INFO - Found contract address: 8cj9WsgHHNwZtAKprAyRGsccA84scaeery9YzmDihc7T
2025-06-14 17:41:21,347 - signal_handler - INFO - Using 'pump' address as highest priority: ********************************************
2025-06-14 17:41:21,347 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: ********************************************
2025-06-14 17:41:21,349 - signal_handler - INFO - Detected GMGN format
2025-06-14 17:41:21,350 - signal_handler - INFO - Found token symbol: 9625
2025-06-14 17:41:21,351 - signal_handler - INFO - Found FDV: 9625.2794 - 9.63K (+225.3%)
2025-06-14 17:41:21,352 - signal_handler - INFO - Detected GMGN channel signal for token: ********************************************
2025-06-14 17:41:21,353 - signal_handler - INFO - Extracted signal: Token=********************************************, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-14 17:41:21,355 - signal_handler - INFO - Added signal to queue: ******************************************** from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-14 17:41:21,356 - signal_handler - INFO - Calling direct signal callback for ********************************************
2025-06-14 17:41:21,362 - signal_handler - INFO - Direct signal processing completed for ********************************************
2025-06-14 17:41:21,362 - signal_handler - INFO - Signal forwarded to bot controller: ******************************************** from solana signal alert - gmgn (confidence: 0.50)
2025-06-14 17:41:21,363 - signal_handler - INFO - Retrieved signal from queue: ******************************************** from solana signal alert - gmgn
2025-06-14 17:41:21,365 - signal_handler - INFO - [msg_1749903081365_8472] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-14 17:41:21

 Token CA: 3taMaRsNHuimPeQGuv9AJLWqpVV4eEZpDQ5i1zhWp...
2025-06-14 17:41:21,365 - signal_handler - INFO - [msg_1749903081365_8472] Message queued with normal priority
2025-06-14 17:41:28,406 - signal_handler - INFO - [msg_1749903081365_8472] Resolving info channel entity for ID: -1002525039395
2025-06-14 17:41:28,559 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$11.1K(+218.9%)**
*...
2025-06-14 17:41:28,560 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-14 17:41:28,560 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-14 17:41:28,560 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-14 17:41:28,561 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-14 17:41:28,561 - signal_handler - INFO - Found contract address with 'pump' suffix in CA line: RcaCHU33trKHLHq5aUSUcqAZ1PurGTe7dpump
2025-06-14 17:41:28,561 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: RcaCHU33trKHLHq5aUSUcqAZ1PurGTe7dpump
2025-06-14 17:41:28,562 - signal_handler - INFO - Detected GMGN format
2025-06-14 17:41:28,562 - signal_handler - INFO - Found token symbol: 11
2025-06-14 17:41:28,563 - signal_handler - INFO - Found FDV: 11.1K - 11.10 (+218.9%)
2025-06-14 17:41:28,563 - signal_handler - INFO - Detected GMGN channel signal for token: RcaCHU33trKHLHq5aUSUcqAZ1PurGTe7dpump
2025-06-14 17:41:28,563 - signal_handler - INFO - Extracted signal: Token=RcaCHU33trKHLHq5aUSUcqAZ1PurGTe7dpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-14 17:41:28,564 - signal_handler - INFO - Added signal to queue: RcaCHU33trKHLHq5aUSUcqAZ1PurGTe7dpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-14 17:41:28,564 - signal_handler - INFO - Calling direct signal callback for RcaCHU33trKHLHq5aUSUcqAZ1PurGTe7dpump
2025-06-14 17:41:28,566 - signal_handler - INFO - Direct signal processing completed for RcaCHU33trKHLHq5aUSUcqAZ1PurGTe7dpump
2025-06-14 17:41:28,566 - signal_handler - INFO - Signal forwarded to bot controller: RcaCHU33trKHLHq5aUSUcqAZ1PurGTe7dpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-14 17:41:28,567 - signal_handler - INFO - [msg_1749903088567_7871] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-14 17:41:28

 Token CA: RcaCHU33trKHLHq5aUSUcqAZ1PurGTe7dpump
[SI...
2025-06-14 17:41:28,567 - signal_handler - INFO - [msg_1749903088567_7871] Message queued with normal priority
2025-06-14 17:41:30,611 - signal_handler - INFO - [msg_1749903090611_7067] Queuing Telegram message: [STOP] [TRADE SKIPPED [?] TOKEN NOT FOUND] [?] 2025-06-14 17:41:30

 Token: RcaCHU33trKHLHq5aUSUcqAZ...
2025-06-14 17:41:30,613 - signal_handler - INFO - [msg_1749903090611_7067] Message queued with normal priority
2025-06-14 17:41:30,615 - signal_handler - INFO - Retrieved signal from queue: RcaCHU33trKHLHq5aUSUcqAZ1PurGTe7dpump from solana signal alert - gmgn
2025-06-14 17:41:30,623 - signal_handler - INFO - [msg_1749903081365_8472] Successfully resolved info channel entity: Bot Info 1
2025-06-14 17:41:31,060 - signal_handler - INFO - [msg_1749903081365_8472] Message sent directly successfully using entity
2025-06-14 17:41:31,062 - signal_handler - INFO - [msg_1749903081365_8472] Message sent successfully via direct send method
2025-06-14 17:41:31,063 - signal_handler - INFO - [msg_1749903081365_8472] Message sent successfully on attempt 1
2025-06-14 17:41:31,063 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-14 17:41:36,937 - signal_handler - INFO - Received sell notification: [PURPLE] [SIM SELL [?] EMERGENCY RUG DETECTED - ZERO LIQUIDITY ($0)] [?] 2025-06-14 17:41:36
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]...
2025-06-14 17:41:36,942 - signal_handler - INFO - [msg_1749903096937_5888] [SELL NOTIFICATION]:  [SIM SELL [?] EMERGENCY RUG DETECTED - ZERO LIQUIDITY ($0)] [?] 2025-06-14 17:41:36

 Triggered by:...
2025-06-14 17:41:36,948 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749903096937_5888.txt
2025-06-14 17:41:36,952 - signal_handler - INFO - [msg_1749903096937_5888] Resolving info channel entity for ID: -1002525039395
2025-06-14 17:41:36,958 - signal_handler - INFO - [msg_1749903088567_7871] Resolving info channel entity for ID: -1002525039395
2025-06-14 17:41:37,013 - signal_handler - INFO - [msg_1749903096937_5888] Successfully resolved info channel entity: Bot Info 1
2025-06-14 17:41:37,024 - signal_handler - INFO - [msg_1749903088567_7871] Successfully resolved info channel entity: Bot Info 1
2025-06-14 17:41:37,116 - signal_handler - INFO - [msg_1749903096937_5888] Message sent directly successfully using entity
2025-06-14 17:41:37,116 - signal_handler - INFO - [msg_1749903096937_5888] Message sent directly successfully
2025-06-14 17:41:37,117 - signal_handler - INFO - [msg_1749903097117_1170] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-14 17:41:37
[REPEAT] Trade: SIMULATION

[MONEY...
2025-06-14 17:41:37,118 - signal_handler - INFO - [msg_1749903097117_1170] Message queued with normal priority
2025-06-14 17:41:37,126 - signal_handler - INFO - [msg_1749903088567_7871] Message sent directly successfully using entity
2025-06-14 17:41:37,127 - signal_handler - INFO - [msg_1749903088567_7871] Message sent successfully via direct send method
2025-06-14 17:41:37,127 - signal_handler - INFO - [msg_1749903088567_7871] Message sent successfully on attempt 1
2025-06-14 17:41:37,128 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-14 17:41:37,319 - signal_handler - INFO - [msg_1749903097317_8083] [SELL NOTIFICATION]: [GREEN] [SIM BUY EXECUTED] [?] 2025-06-14 17:41:37

 Signal Source: solana signal alert - gmgn
 Toke...
2025-06-14 17:41:37,322 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749903097317_8083.txt
2025-06-14 17:41:37,323 - signal_handler - INFO - [msg_1749903097317_8083] Resolving info channel entity for ID: -1002525039395
2025-06-14 17:41:37,380 - signal_handler - INFO - [msg_1749903097317_8083] Successfully resolved info channel entity: Bot Info 1
2025-06-14 17:41:37,487 - signal_handler - INFO - [msg_1749903097317_8083] Message sent directly successfully using entity
2025-06-14 17:41:37,488 - signal_handler - INFO - [msg_1749903097317_8083] Message sent directly successfully
2025-06-14 17:41:37,489 - signal_handler - INFO - [msg_1749903097488_3435] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (SIMULATION) - 3TAMARSN

 Timestamp: 2025-06-14 17:41:37
[REPEAT] Tr...
2025-06-14 17:41:37,490 - signal_handler - INFO - [msg_1749903097488_3435] Message queued with normal priority
2025-06-14 17:41:37,636 - signal_handler - INFO - [msg_1749903090611_7067] Resolving info channel entity for ID: -1002525039395
2025-06-14 17:41:37,696 - signal_handler - INFO - [msg_1749903090611_7067] Successfully resolved info channel entity: Bot Info 1
2025-06-14 17:41:37,801 - signal_handler - INFO - [msg_1749903090611_7067] Message sent directly successfully using entity
2025-06-14 17:41:37,803 - signal_handler - INFO - [msg_1749903090611_7067] Message sent successfully via direct send method
2025-06-14 17:41:37,804 - signal_handler - INFO - [msg_1749903090611_7067] Message sent successfully on attempt 1
2025-06-14 17:41:37,805 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-14 17:41:38,314 - signal_handler - INFO - [msg_1749903097117_1170] Resolving info channel entity for ID: -1002525039395
2025-06-14 17:41:38,373 - signal_handler - INFO - [msg_1749903097117_1170] Successfully resolved info channel entity: Bot Info 1
2025-06-14 17:41:38,480 - signal_handler - INFO - [msg_1749903097117_1170] Message sent directly successfully using entity
2025-06-14 17:41:38,481 - signal_handler - INFO - [msg_1749903097117_1170] Message sent successfully via direct send method
2025-06-14 17:41:38,482 - signal_handler - INFO - [msg_1749903097117_1170] Message sent successfully on attempt 1
2025-06-14 17:41:38,483 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-14 17:41:38,993 - signal_handler - INFO - [msg_1749903097488_3435] Resolving info channel entity for ID: -1002525039395
2025-06-14 17:41:39,053 - signal_handler - INFO - [msg_1749903097488_3435] Successfully resolved info channel entity: Bot Info 1
2025-06-14 17:41:39,153 - signal_handler - INFO - [msg_1749903097488_3435] Message sent directly successfully using entity
2025-06-14 17:41:39,154 - signal_handler - INFO - [msg_1749903097488_3435] Message sent successfully via direct send method
2025-06-14 17:41:39,155 - signal_handler - INFO - [msg_1749903097488_3435] Message sent successfully on attempt 1
2025-06-14 17:54:38,450 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$11.8K(+231.7%)**
*...
2025-06-14 17:54:38,451 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-14 17:54:38,451 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-14 17:54:38,451 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-14 17:54:38,451 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-14 17:54:38,452 - signal_handler - INFO - Found contract address with 'pump' suffix: q4cxHJRMRUj5EXHWRGzher5n6J8eJDfR1eZoq3Kpump
2025-06-14 17:54:38,452 - signal_handler - INFO - Using 'pump' address as highest priority: q4cxHJRMRUj5EXHWRGzher5n6J8eJDfR1eZoq3Kpump
2025-06-14 17:54:38,452 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: q4cxHJRMRUj5EXHWRGzher5n6J8eJDfR1eZoq3Kpump
2025-06-14 17:54:38,453 - signal_handler - INFO - Detected GMGN format
2025-06-14 17:54:38,454 - signal_handler - INFO - Found token symbol: 11
2025-06-14 17:54:38,455 - signal_handler - INFO - Found FDV: 11.8K - 11.80 (+231.7%)
2025-06-14 17:54:38,456 - signal_handler - INFO - Detected GMGN channel signal for token: q4cxHJRMRUj5EXHWRGzher5n6J8eJDfR1eZoq3Kpump
2025-06-14 17:54:38,456 - signal_handler - INFO - Extracted signal: Token=q4cxHJRMRUj5EXHWRGzher5n6J8eJDfR1eZoq3Kpump, Source=solana signal alert - gmgn, Metrics Count=5
2025-06-14 17:54:38,457 - signal_handler - INFO - Signal processing health: 4 received, 4 processed, 0 dropped (100.0% success rate)
2025-06-14 17:54:38,457 - signal_handler - INFO - Added signal to queue: q4cxHJRMRUj5EXHWRGzher5n6J8eJDfR1eZoq3Kpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-14 17:54:38,457 - signal_handler - INFO - Calling direct signal callback for q4cxHJRMRUj5EXHWRGzher5n6J8eJDfR1eZoq3Kpump
2025-06-14 17:54:38,460 - signal_handler - INFO - Direct signal processing completed for q4cxHJRMRUj5EXHWRGzher5n6J8eJDfR1eZoq3Kpump
2025-06-14 17:54:38,461 - signal_handler - INFO - Signal forwarded to bot controller: q4cxHJRMRUj5EXHWRGzher5n6J8eJDfR1eZoq3Kpump from solana signal alert - gmgn (confidence: 0.50)
2025-06-14 17:54:38,461 - signal_handler - INFO - [msg_1749903878461_9479] Queuing Telegram message: [ALARM] [SIGNAL ALERT] [?] 2025-06-14 17:54:38

 Token CA: q4cxHJRMRUj5EXHWRGzher5n6J8eJDfR1eZoq3Kpu...
2025-06-14 17:54:38,462 - signal_handler - INFO - [msg_1749903878461_9479] Message queued with normal priority
2025-06-14 17:54:46,148 - signal_handler - INFO - Retrieved signal from queue: q4cxHJRMRUj5EXHWRGzher5n6J8eJDfR1eZoq3Kpump from solana signal alert - gmgn
2025-06-14 17:54:46,166 - signal_handler - INFO - [msg_1749903878461_9479] Resolving info channel entity for ID: -1002525039395
2025-06-14 17:54:46,344 - signal_handler - INFO - [msg_1749903878461_9479] Successfully resolved info channel entity: Bot Info 1
2025-06-14 17:54:46,442 - signal_handler - INFO - [msg_1749903878461_9479] Message sent directly successfully using entity
2025-06-14 17:54:46,442 - signal_handler - INFO - [msg_1749903878461_9479] Message sent successfully via direct send method
2025-06-14 17:54:46,443 - signal_handler - INFO - [msg_1749903878461_9479] Message sent successfully on attempt 1
2025-06-14 17:54:52,762 - signal_handler - INFO - Received sell notification: [PURPLE] [SIM SELL [?] EMERGENCY RUG DETECTED - ZERO LIQUIDITY ($0)] [?] 2025-06-14 17:54:52
[?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?][?]...
2025-06-14 17:54:52,763 - signal_handler - INFO - [msg_1749903892762_9022] [SELL NOTIFICATION]:  [SIM SELL [?] EMERGENCY RUG DETECTED - ZERO LIQUIDITY ($0)] [?] 2025-06-14 17:54:52

 Triggered by:...
2025-06-14 17:54:52,765 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749903892762_9022.txt
2025-06-14 17:54:52,765 - signal_handler - INFO - [msg_1749903892762_9022] Resolving info channel entity for ID: -1002525039395
2025-06-14 17:54:52,878 - signal_handler - INFO - [msg_1749903892762_9022] Successfully resolved info channel entity: Bot Info 1
2025-06-14 17:54:52,981 - signal_handler - INFO - [msg_1749903892762_9022] Message sent directly successfully using entity
2025-06-14 17:54:52,982 - signal_handler - INFO - [msg_1749903892762_9022] Message sent directly successfully
2025-06-14 17:54:52,983 - signal_handler - INFO - [msg_1749903892983_4607] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER SELL

 Timestamp: 2025-06-14 17:54:52
[REPEAT] Trade: SIMULATION

[MONEY...
2025-06-14 17:54:52,983 - signal_handler - INFO - [msg_1749903892983_4607] Message queued with normal priority
2025-06-14 17:54:52,986 - signal_handler - INFO - [msg_1749903892983_4607] Resolving info channel entity for ID: -1002525039395
2025-06-14 17:54:53,051 - signal_handler - INFO - [msg_1749903893050_8768] [SELL NOTIFICATION]: [GREEN] [SIM BUY EXECUTED] [?] 2025-06-14 17:54:53

 Signal Source: solana signal alert - gmgn
 Toke...
2025-06-14 17:54:53,052 - signal_handler - INFO - Sell notification logged to logs/sell_notifications/sell_msg_1749903893050_8768.txt
2025-06-14 17:54:53,053 - signal_handler - INFO - [msg_1749903893050_8768] Resolving info channel entity for ID: -1002525039395
2025-06-14 17:54:53,080 - signal_handler - INFO - [msg_1749903892983_4607] Successfully resolved info channel entity: Bot Info 1
2025-06-14 17:54:53,114 - signal_handler - INFO - [msg_1749903893050_8768] Successfully resolved info channel entity: Bot Info 1
2025-06-14 17:54:53,187 - signal_handler - INFO - [msg_1749903892983_4607] Message sent directly successfully using entity
2025-06-14 17:54:53,197 - signal_handler - INFO - [msg_1749903892983_4607] Message sent successfully via direct send method
2025-06-14 17:54:53,197 - signal_handler - INFO - [msg_1749903892983_4607] Message sent successfully on attempt 1
2025-06-14 17:54:53,230 - signal_handler - INFO - [msg_1749903893050_8768] Message sent directly successfully using entity
2025-06-14 17:54:53,230 - signal_handler - INFO - [msg_1749903893050_8768] Message sent directly successfully
2025-06-14 17:54:53,231 - signal_handler - INFO - [msg_1749903893231_4121] Queuing Telegram message:  PORTFOLIO UPDATE [?] AFTER BUY (SIMULATION) - Q4CXHJRM

 Timestamp: 2025-06-14 17:54:53
[REPEAT] Tr...
2025-06-14 17:54:53,232 - signal_handler - INFO - [msg_1749903893231_4121] Message queued with normal priority
2025-06-14 17:54:53,305 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.39s (interval: 0.50s)
2025-06-14 17:54:53,712 - signal_handler - INFO - [msg_1749903893231_4121] Resolving info channel entity for ID: -1002525039395
2025-06-14 17:54:53,801 - signal_handler - INFO - [msg_1749903893231_4121] Successfully resolved info channel entity: Bot Info 1
2025-06-14 17:54:53,903 - signal_handler - INFO - [msg_1749903893231_4121] Message sent directly successfully using entity
2025-06-14 17:54:53,904 - signal_handler - INFO - [msg_1749903893231_4121] Message sent successfully via direct send method
2025-06-14 17:54:53,905 - signal_handler - INFO - [msg_1749903893231_4121] Message sent successfully on attempt 1
