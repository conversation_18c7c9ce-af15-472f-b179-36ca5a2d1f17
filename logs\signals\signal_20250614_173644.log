2025-06-14 17:36:44,716 - signal_handler - DEBUG - Loaded Telegram API ID: 27993698
2025-06-14 17:36:44,717 - signal_handler - DEBUG - Loaded Telegram API Hash: ********************************
2025-06-14 17:36:44,718 - signal_handler - INFO - Loaded session string from c:\Users\<USER>\Downloads\billy who\session_string.json (length: 353)
2025-06-14 17:36:44,718 - signal_handler - INFO - Using unique session name with timestamp: trading_sim_session_1749902804
2025-06-14 17:36:44,718 - signal_handler - INFO - Using session path: c:\Users\<USER>\Downloads\billy who\trading_sim_session_1749902804
2025-06-14 17:36:44,719 - signal_handler - INFO - Signal queue initialized with size: 0
2025-06-14 17:36:44,732 - signal_handler - INFO - Direct signal callback registered
2025-06-14 17:36:50,180 - signal_handler - INFO - [msg_1749902810177_3484] Queuing Telegram message: [WARNING] IMPORTANT BOT MESSAGE [?] 2025-06-14 17:36:50

[REFRESH] BOT STATE RESET

All positions, s...
2025-06-14 17:36:50,180 - signal_handler - INFO - Started Telegram message queue processor
2025-06-14 17:36:50,181 - signal_handler - INFO - [msg_1749902810177_3484] Message queued with normal priority
2025-06-14 17:36:50,229 - signal_handler - INFO - [msg_1749902810229_6630] Queuing Telegram message:  PORTFOLIO UPDATE [?] STATE RESET

 Timestamp: 2025-06-14 17:36:50
[REPEAT] Trade: SIMULATION

[MONE...
2025-06-14 17:36:50,230 - signal_handler - INFO - [msg_1749902810229_6630] Message queued with normal priority
2025-06-14 17:36:50,236 - signal_handler - INFO - Message queue processor started with adaptive rate limiting
2025-06-14 17:36:50,236 - signal_handler - WARNING - [msg_1749902810177_3484] Telegram client not connected before sending, attempting to reconnect
2025-06-14 17:36:50,236 - signal_handler - INFO - API ID loaded: True
2025-06-14 17:36:50,237 - signal_handler - INFO - API Hash loaded: True
2025-06-14 17:36:50,237 - signal_handler - INFO - Phone number loaded: True
2025-06-14 17:36:50,237 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-14 17:36:50,237 - signal_handler - INFO - Creating new Telegram client (attempt 1/3)
2025-06-14 17:36:50,239 - signal_handler - INFO - Using saved session string
2025-06-14 17:36:50,240 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-14 17:36:50,524 - signal_handler - INFO - Checking authorization status...
2025-06-14 17:36:50,580 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-14 17:36:50,581 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-14 17:36:50,582 - signal_handler - INFO - Checking authorization status...
2025-06-14 17:36:50,636 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-14 17:36:50,637 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-14 17:36:50,640 - signal_handler - INFO - Checking authorization status...
2025-06-14 17:36:50,697 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-14 17:36:50,697 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-14 17:36:50,698 - signal_handler - INFO - Sending test message to verify connection to channel -1002525039395
2025-06-14 17:36:50,698 - signal_handler - INFO - Sending test message directly to channel ID: -1002525039395
2025-06-14 17:36:50,866 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-14 17:36:50,867 - signal_handler - INFO - [msg_1749902810177_3484] Resolving info channel entity for ID: -1002525039395
2025-06-14 17:36:51,001 - signal_handler - INFO - [msg_1749902810177_3484] Successfully resolved info channel entity: Bot Info 1
2025-06-14 17:36:51,173 - signal_handler - INFO - [msg_1749902810177_3484] Message sent directly successfully using entity
2025-06-14 17:36:51,173 - signal_handler - INFO - [msg_1749902810177_3484] Message sent successfully via direct send method
2025-06-14 17:36:51,174 - signal_handler - INFO - [msg_1749902810177_3484] Message sent successfully on attempt 1
2025-06-14 17:36:51,174 - signal_handler - DEBUG - Adaptive rate limiting: sleeping 0.50s (interval: 0.50s)
2025-06-14 17:36:51,677 - signal_handler - INFO - [msg_1749902810229_6630] Resolving info channel entity for ID: -1002525039395
2025-06-14 17:36:51,734 - signal_handler - INFO - [msg_1749902810229_6630] Successfully resolved info channel entity: Bot Info 1
2025-06-14 17:36:51,832 - signal_handler - INFO - [msg_1749902810229_6630] Message sent directly successfully using entity
2025-06-14 17:36:51,832 - signal_handler - INFO - [msg_1749902810229_6630] Message sent successfully via direct send method
2025-06-14 17:36:51,833 - signal_handler - INFO - [msg_1749902810229_6630] Message sent successfully on attempt 1
2025-06-14 17:36:54,665 - signal_handler - INFO - API ID loaded: True
2025-06-14 17:36:54,665 - signal_handler - INFO - API Hash loaded: True
2025-06-14 17:36:54,665 - signal_handler - INFO - Phone number loaded: True
2025-06-14 17:36:54,665 - signal_handler - INFO - Attempting to connect to Telegram...
2025-06-14 17:36:54,666 - signal_handler - INFO - Connecting to Telegram (attempt 1/3)...
2025-06-14 17:36:54,668 - signal_handler - INFO - Checking authorization status...
2025-06-14 17:36:54,728 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-14 17:36:54,729 - signal_handler - INFO - Connecting to Telegram (attempt 2/3)...
2025-06-14 17:36:54,729 - signal_handler - INFO - Checking authorization status...
2025-06-14 17:36:54,807 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-14 17:36:54,807 - signal_handler - INFO - Connecting to Telegram (attempt 3/3)...
2025-06-14 17:36:54,808 - signal_handler - INFO - Checking authorization status...
2025-06-14 17:36:54,859 - signal_handler - INFO - Successfully retrieved own user ID: 768056701
2025-06-14 17:36:54,894 - signal_handler - INFO - Telegram authorization successful, but couldn't establish full connection
2025-06-14 17:36:54,895 - signal_handler - INFO - Sending test message to verify connection to channel -1002525039395
2025-06-14 17:36:54,895 - signal_handler - INFO - Sending test message directly to channel ID: -1002525039395
2025-06-14 17:36:54,987 - signal_handler - INFO - Test message sent successfully - connection is fully working
2025-06-14 17:36:54,991 - signal_handler - INFO - [msg_1749902814991_2577] Queuing Telegram message:  PORTFOLIO UPDATE [?] PERIODIC UPDATE

 Timestamp: 2025-06-14 17:36:54
[REPEAT] Trade: SIMULATION

[...
2025-06-14 17:36:54,991 - signal_handler - INFO - [msg_1749902814991_2577] Message queued with normal priority
2025-06-14 17:36:55,075 - signal_handler - INFO - [msg_1749902814991_2577] Resolving info channel entity for ID: -1002525039395
2025-06-14 17:36:55,129 - signal_handler - INFO - [msg_1749902814991_2577] Successfully resolved info channel entity: Bot Info 1
2025-06-14 17:36:55,210 - signal_handler - INFO - [msg_1749902814991_2577] Message sent directly successfully using entity
2025-06-14 17:36:55,210 - signal_handler - INFO - [msg_1749902814991_2577] Message sent successfully via direct send method
2025-06-14 17:36:55,211 - signal_handler - INFO - [msg_1749902814991_2577] Message sent successfully on attempt 1
2025-06-14 17:36:55,511 - signal_handler - WARNING - [WARNING] Using channel IDs directly (no entities resolved)
2025-06-14 17:36:55,511 - signal_handler - WARNING - Added event handler using channel IDs (entities not resolved yet)
2025-06-14 17:36:55,513 - signal_handler - INFO - Signal listener started in background task. Waiting for messages...
2025-06-14 17:36:55,516 - signal_handler - INFO - Direct signal callback registered
2025-06-14 17:36:55,520 - signal_handler - INFO - [msg_1749902815518_1946] [STARTUP NOTIFICATION]: [ROCKET] Bot Started
[REFRESH] Mode: SIMULATION
[GRAPH] Strategy: default
[MONEY] Starting Capital: ...
2025-06-14 17:36:55,526 - signal_handler - INFO - [msg_1749902815518_1946] Resolving info channel entity for ID: -1002525039395
2025-06-14 17:36:55,529 - signal_handler - INFO - Signal listener running (attempt 1/3).
2025-06-14 17:36:55,587 - signal_handler - INFO - [msg_1749902815518_1946] Successfully resolved info channel entity: Bot Info 1
2025-06-14 17:36:55,680 - signal_handler - INFO - [msg_1749902815518_1946] Message sent directly successfully using entity
2025-06-14 17:36:55,680 - signal_handler - INFO - [msg_1749902815518_1946] Message sent directly successfully
2025-06-14 17:40:21,181 - signal_handler - INFO - Received message from channel -1001938040677: **[PILL][PILL]Pump[MARKET_VALUE_SURGE] FDV Surge Alert**

**FDV in 5 min [GREEN]+$20K(+208.4%)**
**[...
2025-06-14 17:40:21,183 - signal_handler - INFO - Detected message from GMGN channel (ID: -1001938040677)
2025-06-14 17:40:21,184 - signal_handler - INFO - Setting channel_identifier to 'solana signal alert - gmgn' based on chat_id -1001938040677
2025-06-14 17:40:21,184 - signal_handler - INFO - Setting is_gmgn_channel flag based on chat_id -1001938040677
2025-06-14 17:40:21,184 - signal_handler - INFO - Is Solana Early Trending message: False
2025-06-14 17:40:21,186 - signal_handler - INFO - Found contract address with 'pump' suffix: E3coPuGRRkNiWuhYaXvceg1r7Vwh4xkLnbzYgAkZpump
2025-06-14 17:40:21,186 - signal_handler - INFO - Found contract address: 6guZ6HZYC5xZpVCV38bRN4ctZDjASGpS5A9RjH9En4UF
2025-06-14 17:40:21,186 - signal_handler - INFO - Using 'pump' address as highest priority: E3coPuGRRkNiWuhYaXvceg1r7Vwh4xkLnbzYgAkZpump
2025-06-14 17:40:21,187 - signal_handler - WARNING - PUMP TOKEN DETECTED in signal: E3coPuGRRkNiWuhYaXvceg1r7Vwh4xkLnbzYgAkZpump
2025-06-14 17:40:21,188 - signal_handler - INFO - Detected GMGN format
2025-06-14 17:40:21,189 - signal_handler - INFO - Found token symbol: 20K
2025-06-14 17:40:21,189 - signal_handler - INFO - Found token full name: +208.4%
2025-06-14 17:40:21,190 - signal_handler - INFO - Found FDV: 20K - 20.00K (+208.4%)
2025-06-14 17:40:21,190 - signal_handler - INFO - Detected GMGN channel signal for token: E3coPuGRRkNiWuhYaXvceg1r7Vwh4xkLnbzYgAkZpump
2025-06-14 17:40:21,190 - signal_handler - INFO - Extracted signal: Token=E3coPuGRRkNiWuhYaXvceg1r7Vwh4xkLnbzYgAkZpump, Source=solana signal alert - gmgn, Metrics Count=6
2025-06-14 17:40:21,191 - signal_handler - WARNING - LOW SIGNAL VOLUME: GMGN - 0/8 expected this hour
2025-06-14 17:40:21,191 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solbix - 0/2 expected this hour
2025-06-14 17:40:21,191 - signal_handler - WARNING - LOW SIGNAL VOLUME: Solana Early Trending - 0/1 expected this hour
2025-06-14 17:40:21,192 - signal_handler - INFO - Signal processing health: 1 received, 1 processed, 0 dropped (100.0% success rate)
2025-06-14 17:40:21,192 - signal_handler - INFO - Added signal to queue: E3coPuGRRkNiWuhYaXvceg1r7Vwh4xkLnbzYgAkZpump from solana signal alert - gmgn with confidence 0.5, GMGN channel: True
2025-06-14 17:40:21,192 - signal_handler - INFO - Calling direct signal callback for E3coPuGRRkNiWuhYaXvceg1r7Vwh4xkLnbzYgAkZpump
