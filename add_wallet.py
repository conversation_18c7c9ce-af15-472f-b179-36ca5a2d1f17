#!/usr/bin/env python3
"""
Simple script to add a wallet to .env file
"""

import os
from datetime import datetime

def add_wallet_to_env():
    """Add a wallet to .env file"""
    print("🔐 ADD WALLET TO .ENV")
    print("=" * 30)
    
    # Get wallet details
    wallet_name = input("Enter wallet name: ").strip().upper()
    if not wallet_name:
        print("❌ Wallet name required")
        return
    
    private_key = input("Enter private key (base58): ").strip()
    if not private_key:
        print("❌ Private key required")
        return
    
    # Read existing .env content
    env_content = []
    env_file = '.env'
    
    if os.path.exists(env_file):
        with open(env_file, 'r') as f:
            env_content = f.readlines()
    
    # Remove existing entries for this wallet
    env_content = [line for line in env_content 
                  if not line.startswith(f'WALLET_{wallet_name}_')]
    
    # Add new wallet entries
    wallet_vars = [
        f'WALLET_{wallet_name}_PRIVATE_KEY={private_key}\n',
        f'WALLET_{wallet_name}_CREATED_AT={datetime.now().isoformat()}\n',
        f'WALLET_{wallet_name}_TRADES_COUNT=0\n'
    ]
    
    env_content.extend(wallet_vars)
    
    # Write back to .env
    with open(env_file, 'w') as f:
        f.writelines(env_content)
    
    print(f"✅ Wallet '{wallet_name}' added to .env")
    print(f"📁 File: {os.path.abspath(env_file)}")
    print("\n🚀 You can now use:")
    print(f"  python pumpportal_manual.py")
    print(f"  Enter wallet name: {wallet_name.lower()}")

if __name__ == "__main__":
    try:
        add_wallet_to_env()
    except Exception as e:
        print(f"❌ Error: {e}")
