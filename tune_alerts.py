#!/usr/bin/env python3
"""
<PERSON><PERSON> - Optimize alert thresholds based on historical data
Usage: python tune_alerts.py
"""

import json
import statistics
from datetime import datetime, timedelta
from collections import defaultdict
import os

class AlertTuner:
    def __init__(self, health_file="production_health.jsonl"):
        self.health_file = health_file
        
    def analyze_baseline_metrics(self, days=7):
        """Analyze baseline system metrics over the last N days"""
        if not os.path.exists(self.health_file):
            print(f"❌ Health log file not found: {self.health_file}")
            return None
            
        cutoff_time = datetime.now() - timedelta(days=days)
        
        cpu_values = []
        memory_values = []
        disk_values = []
        queue_sizes = []
        error_counts = []
        
        try:
            with open(self.health_file, 'r') as f:
                for line in f:
                    try:
                        data = json.loads(line.strip())
                        timestamp = datetime.fromisoformat(data['system']['timestamp'].replace('Z', '+00:00'))
                        
                        if timestamp >= cutoff_time:
                            cpu_values.append(data['system']['cpu_percent'])
                            memory_values.append(data['system']['memory_percent'])
                            disk_values.append(data['system']['disk_free_gb'])
                            queue_sizes.append(data['trading']['signal_queue_size'])
                            error_counts.append(data['trading']['error_count_1h'])
                            
                    except (json.JSONDecodeError, KeyError):
                        continue
        except Exception as e:
            print(f"❌ Error analyzing baseline: {e}")
            return None
        
        if not cpu_values:
            print("❌ No data found for analysis")
            return None
            
        def calculate_stats(values):
            return {
                'mean': statistics.mean(values),
                'median': statistics.median(values),
                'stdev': statistics.stdev(values) if len(values) > 1 else 0,
                'p95': sorted(values)[int(len(values) * 0.95)] if values else 0,
                'p99': sorted(values)[int(len(values) * 0.99)] if values else 0,
                'max': max(values),
                'min': min(values)
            }
        
        return {
            'period_days': days,
            'data_points': len(cpu_values),
            'cpu_percent': calculate_stats(cpu_values),
            'memory_percent': calculate_stats(memory_values),
            'disk_free_gb': calculate_stats(disk_values),
            'signal_queue_size': calculate_stats(queue_sizes),
            'error_count_1h': calculate_stats(error_counts)
        }
    
    def recommend_thresholds(self, baseline_data):
        """Recommend alert thresholds based on baseline data"""
        if not baseline_data:
            return None
            
        recommendations = {}
        
        # CPU: Alert at P95 + 1 standard deviation, but at least 70%
        cpu_threshold = max(70.0, baseline_data['cpu_percent']['p95'] + baseline_data['cpu_percent']['stdev'])
        recommendations['cpu_percent'] = min(95.0, cpu_threshold)  # Cap at 95%
        
        # Memory: Alert at P95 + 1 standard deviation, but at least 80%
        memory_threshold = max(80.0, baseline_data['memory_percent']['p95'] + baseline_data['memory_percent']['stdev'])
        recommendations['memory_percent'] = min(95.0, memory_threshold)  # Cap at 95%
        
        # Disk: Alert when less than P5 - 1GB, but at least 2GB
        disk_p5 = sorted([baseline_data['disk_free_gb']['min']])[0]  # Simplified P5
        disk_threshold = max(2.0, disk_p5 - 1.0)
        recommendations['disk_free_gb'] = disk_threshold
        
        # Queue size: Alert at P99, but at least 50
        queue_threshold = max(50, baseline_data['signal_queue_size']['p99'])
        recommendations['signal_queue_size'] = int(queue_threshold)
        
        # Error rate: Alert at P95 + 2 standard deviations, but at least 5
        error_threshold = max(5, baseline_data['error_count_1h']['p95'] + 2 * baseline_data['error_count_1h']['stdev'])
        recommendations['error_rate_1h'] = int(error_threshold)
        
        return recommendations
    
    def print_tuning_report(self):
        """Print comprehensive tuning report"""
        print("\n" + "="*80)
        print("⚙️  ALERT THRESHOLD TUNING REPORT")
        print("="*80)
        
        # Analyze 7-day baseline
        baseline = self.analyze_baseline_metrics(7)
        if not baseline:
            print("❌ Insufficient data for analysis")
            return
            
        print(f"📊 BASELINE ANALYSIS ({baseline['data_points']} data points over {baseline['period_days']} days):")
        print()
        
        # Current thresholds (from production_monitor.py)
        current_thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_free_gb': 1.0,
            'error_rate_1h': 10,
            'signal_queue_size': 100
        }
        
        # Get recommendations
        recommended = self.recommend_thresholds(baseline)
        
        print("📈 METRIC ANALYSIS & RECOMMENDATIONS:")
        print()
        
        # CPU Analysis
        cpu_stats = baseline['cpu_percent']
        print(f"🖥️  CPU Usage:")
        print(f"   Current Threshold: {current_thresholds['cpu_percent']:.1f}%")
        print(f"   Your Average: {cpu_stats['mean']:.1f}% (±{cpu_stats['stdev']:.1f}%)")
        print(f"   Your P95: {cpu_stats['p95']:.1f}%")
        print(f"   Recommended: {recommended['cpu_percent']:.1f}%")
        
        if recommended['cpu_percent'] != current_thresholds['cpu_percent']:
            direction = "🔺 INCREASE" if recommended['cpu_percent'] > current_thresholds['cpu_percent'] else "🔻 DECREASE"
            print(f"   💡 {direction} threshold to reduce false alerts")
        else:
            print(f"   ✅ Current threshold is appropriate")
        print()
        
        # Memory Analysis
        mem_stats = baseline['memory_percent']
        print(f"💾 Memory Usage:")
        print(f"   Current Threshold: {current_thresholds['memory_percent']:.1f}%")
        print(f"   Your Average: {mem_stats['mean']:.1f}% (±{mem_stats['stdev']:.1f}%)")
        print(f"   Your P95: {mem_stats['p95']:.1f}%")
        print(f"   Recommended: {recommended['memory_percent']:.1f}%")
        
        if recommended['memory_percent'] != current_thresholds['memory_percent']:
            direction = "🔺 INCREASE" if recommended['memory_percent'] > current_thresholds['memory_percent'] else "🔻 DECREASE"
            print(f"   💡 {direction} threshold to reduce false alerts")
        else:
            print(f"   ✅ Current threshold is appropriate")
        print()
        
        # Disk Analysis
        disk_stats = baseline['disk_free_gb']
        print(f"💿 Disk Space:")
        print(f"   Current Threshold: {current_thresholds['disk_free_gb']:.1f}GB")
        print(f"   Your Average Free: {disk_stats['mean']:.1f}GB")
        print(f"   Your Minimum: {disk_stats['min']:.1f}GB")
        print(f"   Recommended: {recommended['disk_free_gb']:.1f}GB")
        
        if abs(recommended['disk_free_gb'] - current_thresholds['disk_free_gb']) > 0.5:
            direction = "🔺 INCREASE" if recommended['disk_free_gb'] > current_thresholds['disk_free_gb'] else "🔻 DECREASE"
            print(f"   💡 {direction} threshold based on your usage patterns")
        else:
            print(f"   ✅ Current threshold is appropriate")
        print()
        
        # Queue Size Analysis
        queue_stats = baseline['signal_queue_size']
        print(f"📬 Signal Queue:")
        print(f"   Current Threshold: {current_thresholds['signal_queue_size']}")
        print(f"   Your Average: {queue_stats['mean']:.1f}")
        print(f"   Your P99: {queue_stats['p99']:.1f}")
        print(f"   Recommended: {recommended['signal_queue_size']}")
        
        if recommended['signal_queue_size'] != current_thresholds['signal_queue_size']:
            direction = "🔺 INCREASE" if recommended['signal_queue_size'] > current_thresholds['signal_queue_size'] else "🔻 DECREASE"
            print(f"   💡 {direction} threshold based on your traffic patterns")
        else:
            print(f"   ✅ Current threshold is appropriate")
        print()
        
        # Error Rate Analysis
        error_stats = baseline['error_count_1h']
        print(f"🚨 Error Rate:")
        print(f"   Current Threshold: {current_thresholds['error_rate_1h']}/hour")
        print(f"   Your Average: {error_stats['mean']:.1f}/hour")
        print(f"   Your P95: {error_stats['p95']:.1f}/hour")
        print(f"   Recommended: {recommended['error_rate_1h']}/hour")
        
        if recommended['error_rate_1h'] != current_thresholds['error_rate_1h']:
            direction = "🔺 INCREASE" if recommended['error_rate_1h'] > current_thresholds['error_rate_1h'] else "🔻 DECREASE"
            print(f"   💡 {direction} threshold based on your error patterns")
        else:
            print(f"   ✅ Current threshold is appropriate")
        print()
        
        # Generate update code
        print("🔧 TO UPDATE THRESHOLDS, modify production_monitor.py:")
        print("   Find the line: self.alert_thresholds = {")
        print("   Replace with:")
        print("   self.alert_thresholds = {")
        for key, value in recommended.items():
            if isinstance(value, float):
                print(f"       '{key}': {value:.1f},")
            else:
                print(f"       '{key}': {value},")
        print("   }")
        
        print("="*80)

def main():
    tuner = AlertTuner()
    
    print("⚙️  ALERT THRESHOLD TUNER")
    print("This tool analyzes your system's behavior and recommends optimal alert thresholds.")
    print()
    
    # Check if we have enough data
    if not os.path.exists("production_health.jsonl"):
        print("❌ No health data found. Run the bot for at least 24 hours to collect baseline data.")
        return
    
    # Run analysis
    tuner.print_tuning_report()

if __name__ == "__main__":
    main()
