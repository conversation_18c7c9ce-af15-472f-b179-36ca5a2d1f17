#!/usr/bin/env python3
"""
Memory Analyzer - Detailed memory usage analysis for the trading bot
"""

import psutil
import os
import time
from datetime import datetime
from typing import Dict, List, Tuple

def get_system_memory_info() -> Dict:
    """Get detailed system memory information"""
    mem = psutil.virtual_memory()
    swap = psutil.swap_memory()
    
    return {
        'total_gb': mem.total / (1024**3),
        'used_gb': mem.used / (1024**3),
        'available_gb': mem.available / (1024**3),
        'free_gb': mem.free / (1024**3),
        'percent_used': mem.percent,
        'swap_total_gb': swap.total / (1024**3),
        'swap_used_gb': swap.used / (1024**3),
        'swap_percent': swap.percent
    }

def get_top_memory_processes(limit: int = 10) -> List[Tuple[str, float, float]]:
    """Get top memory-consuming processes"""
    processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'memory_percent', 'memory_info']):
        try:
            info = proc.info
            if info['memory_percent'] > 0.1:  # Only processes using > 0.1%
                memory_mb = info['memory_info'].rss / (1024**2)
                processes.append((
                    info['name'],
                    info['memory_percent'],
                    memory_mb
                ))
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    # Sort by memory percentage
    processes.sort(key=lambda x: x[1], reverse=True)
    return processes[:limit]

def find_python_processes() -> List[Dict]:
    """Find all Python processes and their memory usage"""
    python_processes = []
    
    for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'memory_percent', 'memory_info']):
        try:
            info = proc.info
            if 'python' in info['name'].lower():
                cmdline = ' '.join(info['cmdline']) if info['cmdline'] else 'N/A'
                memory_mb = info['memory_info'].rss / (1024**2)
                
                python_processes.append({
                    'pid': info['pid'],
                    'name': info['name'],
                    'cmdline': cmdline,
                    'memory_percent': info['memory_percent'],
                    'memory_mb': memory_mb
                })
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            continue
    
    return sorted(python_processes, key=lambda x: x['memory_percent'], reverse=True)

def analyze_bot_memory_usage() -> Dict:
    """Analyze memory usage specific to the trading bot"""
    current_process = psutil.Process()
    
    try:
        memory_info = current_process.memory_info()
        memory_percent = current_process.memory_percent()
        
        # Get memory details
        return {
            'pid': current_process.pid,
            'name': current_process.name(),
            'memory_mb': memory_info.rss / (1024**2),
            'memory_percent': memory_percent,
            'virtual_memory_mb': memory_info.vms / (1024**2),
            'num_threads': current_process.num_threads(),
            'num_fds': len(current_process.open_files()) if hasattr(current_process, 'open_files') else 0,
            'cpu_percent': current_process.cpu_percent()
        }
    except Exception as e:
        return {'error': str(e)}

def get_memory_recommendations(system_info: Dict, top_processes: List) -> List[str]:
    """Generate memory optimization recommendations"""
    recommendations = []
    
    # System-level recommendations
    if system_info['percent_used'] > 90:
        recommendations.append("🚨 CRITICAL: System memory usage is very high (>90%)")
        recommendations.append("   → Restart your computer or close major applications")
    elif system_info['percent_used'] > 85:
        recommendations.append("⚠️ WARNING: System memory usage is high (>85%)")
        recommendations.append("   → Consider closing some applications")
    
    # Process-specific recommendations
    if top_processes:
        top_process = top_processes[0]
        if top_process[1] > 20:  # If top process uses >20%
            recommendations.append(f"🔍 ANALYSIS: '{top_process[0]}' is using {top_process[1]:.1f}% memory")
            
            if 'firefox' in top_process[0].lower():
                recommendations.append("   → Close unnecessary browser tabs")
            elif 'chrome' in top_process[0].lower():
                recommendations.append("   → Close unnecessary Chrome tabs")
            elif 'code' in top_process[0].lower():
                recommendations.append("   → Close unused VS Code windows/extensions")
    
    # Available memory check
    if system_info['available_gb'] < 1.0:
        recommendations.append("🚨 LOW MEMORY: Less than 1GB available")
        recommendations.append("   → Close applications immediately")
    elif system_info['available_gb'] < 2.0:
        recommendations.append("⚠️ LIMITED MEMORY: Less than 2GB available")
        recommendations.append("   → Monitor memory usage closely")
    
    return recommendations

def format_memory_report() -> str:
    """Generate a comprehensive memory report"""
    lines = []
    lines.append("🧠 COMPREHENSIVE MEMORY ANALYSIS")
    lines.append("=" * 60)
    lines.append(f"Report Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    lines.append("")
    
    # System memory info
    system_info = get_system_memory_info()
    lines.append("💻 SYSTEM MEMORY:")
    lines.append(f"  Total RAM: {system_info['total_gb']:.1f} GB")
    lines.append(f"  Used: {system_info['used_gb']:.1f} GB ({system_info['percent_used']:.1f}%)")
    lines.append(f"  Available: {system_info['available_gb']:.1f} GB")
    lines.append(f"  Free: {system_info['free_gb']:.1f} GB")
    
    if system_info['swap_total_gb'] > 0:
        lines.append(f"  Swap Used: {system_info['swap_used_gb']:.1f} GB ({system_info['swap_percent']:.1f}%)")
    lines.append("")
    
    # Top processes
    top_processes = get_top_memory_processes(10)
    lines.append("🔝 TOP MEMORY CONSUMERS:")
    for i, (name, percent, mb) in enumerate(top_processes, 1):
        lines.append(f"  {i:2d}. {name:<20} {percent:5.1f}% ({mb:6.1f} MB)")
    lines.append("")
    
    # Python processes
    python_processes = find_python_processes()
    if python_processes:
        lines.append("🐍 PYTHON PROCESSES:")
        for proc in python_processes:
            lines.append(f"  PID {proc['pid']}: {proc['memory_percent']:.2f}% ({proc['memory_mb']:.1f} MB)")
            if 'main.py' in proc['cmdline'] or 'bot' in proc['cmdline'].lower():
                lines.append(f"    → TRADING BOT: {proc['cmdline'][:80]}...")
            else:
                lines.append(f"    → {proc['cmdline'][:80]}...")
        lines.append("")
    
    # Bot-specific analysis
    bot_info = analyze_bot_memory_usage()
    if 'error' not in bot_info:
        lines.append("🤖 TRADING BOT ANALYSIS:")
        lines.append(f"  Process ID: {bot_info['pid']}")
        lines.append(f"  Memory Usage: {bot_info['memory_mb']:.1f} MB ({bot_info['memory_percent']:.3f}%)")
        lines.append(f"  Virtual Memory: {bot_info['virtual_memory_mb']:.1f} MB")
        lines.append(f"  Threads: {bot_info['num_threads']}")
        lines.append(f"  Open Files: {bot_info['num_fds']}")
        lines.append(f"  CPU Usage: {bot_info['cpu_percent']:.1f}%")
        lines.append("")
        
        # Bot memory assessment
        if bot_info['memory_mb'] < 50:
            lines.append("  ✅ EXCELLENT: Bot memory usage is very low")
        elif bot_info['memory_mb'] < 100:
            lines.append("  ✅ GOOD: Bot memory usage is reasonable")
        elif bot_info['memory_mb'] < 200:
            lines.append("  ⚠️ MODERATE: Bot memory usage is getting high")
        else:
            lines.append("  🚨 HIGH: Bot memory usage needs investigation")
    lines.append("")
    
    # Recommendations
    recommendations = get_memory_recommendations(system_info, top_processes)
    if recommendations:
        lines.append("💡 RECOMMENDATIONS:")
        for rec in recommendations:
            lines.append(f"  {rec}")
        lines.append("")
    
    # Memory health status
    lines.append("🎯 MEMORY HEALTH STATUS:")
    if system_info['percent_used'] < 70:
        lines.append("  ✅ EXCELLENT: Plenty of memory available")
    elif system_info['percent_used'] < 80:
        lines.append("  ✅ GOOD: Memory usage is reasonable")
    elif system_info['percent_used'] < 85:
        lines.append("  ⚠️ MODERATE: Memory usage is getting high")
    elif system_info['percent_used'] < 90:
        lines.append("  ⚠️ HIGH: Memory usage is concerning")
    else:
        lines.append("  🚨 CRITICAL: Memory usage is very high")
    
    lines.append("")
    lines.append("=" * 60)
    
    return "\n".join(lines)

def main():
    """Run memory analysis"""
    print(format_memory_report())

if __name__ == "__main__":
    main()
